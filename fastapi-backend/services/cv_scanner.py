import cv2
import mediapipe as mp
import numpy as np
from PIL import Image
import asyncio
import logging
from typing import Tuple, List, Dict, Optional
from dataclasses import dataclass
from datetime import datetime
import io
import base64

@dataclass
class CVResult:
    landmarks: List[Dict]
    confidence: float
    recommendations: List[str]
    processing_time: float

@dataclass
class DepthResult:
    depth_map: np.ndarray
    binary_mask: np.ndarray
    processing_time: float
    quality_score: float

class CVScannerService:
    """
    Computer Vision service using OpenCV and MediaPipe for 3D body scanning
    """
    
    def __init__(self):
        # Initialize MediaPipe solutions
        self.mp_pose = mp.solutions.pose
        self.mp_hands = mp.solutions.hands
        self.mp_face_mesh = mp.solutions.face_mesh
        self.mp_selfie_segmentation = mp.solutions.selfie_segmentation
        
        # Initialize pose estimation
        self.pose = self.mp_pose.Pose(
            static_image_mode=True,
            model_complexity=2,
            enable_segmentation=True,
            min_detection_confidence=0.7
        )
        
        # Initialize hand detection
        self.hands = self.mp_hands.Hands(
            static_image_mode=True,
            max_num_hands=2,
            min_detection_confidence=0.7
        )
        
        # Initialize segmentation
        self.segmentation = self.mp_selfie_segmentation.SelfieSegmentation(
            model_selection=1  # General model
        )
        
        logging.info("CV Scanner Service initialized with MediaPipe")
    
    async def health_check(self) -> bool:
        """Check if CV services are operational"""
        try:
            # Test with a simple image
            test_image = np.zeros((100, 100, 3), dtype=np.uint8)
            result = self.pose.process(test_image)
            return True
        except Exception:
            return False
    
    async def process_image(
        self, 
        image_data: bytes, 
        body_part: str, 
        quality_level: str = "high"
    ) -> CVResult:
        """
        Process uploaded image with computer vision to extract landmarks and features
        """
        start_time = datetime.utcnow()
        
        try:
            # Convert bytes to OpenCV image
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if image is None:
                raise ValueError("Invalid image data")
            
            # Convert BGR to RGB for MediaPipe
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            landmarks = []
            confidence = 0.0
            recommendations = []
            
            # Process based on body part
            if body_part in ["forearm", "calf"]:
                landmarks, confidence = await self._process_limb(rgb_image, body_part)
            elif body_part == "torso":
                landmarks, confidence = await self._process_torso(rgb_image)
            elif body_part == "hand":
                landmarks, confidence = await self._process_hand(rgb_image)
            else:
                raise ValueError(f"Unsupported body part: {body_part}")
            
            # Generate recommendations based on analysis
            recommendations = self._generate_recommendations(
                body_part, confidence, image.shape
            )
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            return CVResult(
                landmarks=landmarks,
                confidence=confidence,
                recommendations=recommendations,
                processing_time=processing_time
            )
            
        except Exception as e:
            logging.error(f"CV processing failed: {str(e)}")
            raise
    
    async def _process_limb(self, image: np.ndarray, body_part: str) -> Tuple[List[Dict], float]:
        """Process arm or leg using pose estimation"""
        results = self.pose.process(image)
        
        landmarks = []
        confidence = 0.0
        
        if results.pose_landmarks:
            # Extract relevant landmarks for limbs
            for idx, landmark in enumerate(results.pose_landmarks.landmark):
                if body_part == "forearm":
                    # Focus on arm landmarks (11, 13, 15, 17, 19, 21 for left arm)
                    if idx in [11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]:
                        landmarks.append({
                            "id": idx,
                            "x": landmark.x,
                            "y": landmark.y,
                            "z": landmark.z,
                            "visibility": landmark.visibility
                        })
                elif body_part == "calf":
                    # Focus on leg landmarks (23, 25, 27, 29, 31 for left leg)
                    if idx in [23, 24, 25, 26, 27, 28, 29, 30, 31, 32]:
                        landmarks.append({
                            "id": idx,
                            "x": landmark.x,
                            "y": landmark.y,
                            "z": landmark.z,
                            "visibility": landmark.visibility
                        })
            
            # Calculate average confidence
            if landmarks:
                confidence = sum(lm["visibility"] for lm in landmarks) / len(landmarks)
        
        return landmarks, confidence
    
    async def _process_torso(self, image: np.ndarray) -> Tuple[List[Dict], float]:
        """Process torso using pose estimation"""
        results = self.pose.process(image)
        
        landmarks = []
        confidence = 0.0
        
        if results.pose_landmarks:
            # Focus on torso landmarks (shoulders, chest, waist)
            torso_indices = [11, 12, 23, 24]  # shoulders and hips
            
            for idx, landmark in enumerate(results.pose_landmarks.landmark):
                if idx in torso_indices:
                    landmarks.append({
                        "id": idx,
                        "x": landmark.x,
                        "y": landmark.y,
                        "z": landmark.z,
                        "visibility": landmark.visibility
                    })
            
            if landmarks:
                confidence = sum(lm["visibility"] for lm in landmarks) / len(landmarks)
        
        return landmarks, confidence
    
    async def _process_hand(self, image: np.ndarray) -> Tuple[List[Dict], float]:
        """Process hand using MediaPipe Hands"""
        results = self.hands.process(image)
        
        landmarks = []
        confidence = 0.0
        
        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                for idx, landmark in enumerate(hand_landmarks.landmark):
                    landmarks.append({
                        "id": idx,
                        "x": landmark.x,
                        "y": landmark.y,
                        "z": landmark.z,
                        "visibility": 1.0  # Hands don't have visibility score
                    })
                
                confidence = 0.9  # High confidence for detected hands
        
        return landmarks, confidence
    
    def _generate_recommendations(
        self, 
        body_part: str, 
        confidence: float, 
        image_shape: tuple
    ) -> List[str]:
        """Generate recommendations for better scanning"""
        recommendations = []
        
        if confidence < 0.7:
            recommendations.append("Improve lighting for better detection")
            recommendations.append("Ensure the body part is clearly visible")
        
        height, width = image_shape[:2]
        if width < 800 or height < 600:
            recommendations.append("Use higher resolution image for better results")
        
        if body_part == "forearm":
            recommendations.extend([
                "Position arm straight and parallel to camera",
                "Ensure good lighting on skin surface",
                "Remove any clothing or jewelry from forearm"
            ])
        elif body_part == "hand":
            recommendations.extend([
                "Lay hand flat on contrasting surface",
                "Spread fingers slightly for better mapping",
                "Ensure all fingers are visible"
            ])
        elif body_part == "torso":
            recommendations.extend([
                "Stand straight with arms slightly away from body",
                "Ensure even lighting across chest area",
                "Remove any clothing from target area"
            ])
        elif body_part == "calf":
            recommendations.extend([
                "Stand with leg straight, foot flat",
                "Capture full calf muscle area",
                "Position camera at calf level"
            ])
        
        return recommendations
    
    async def save_local_file(self, data: np.ndarray, file_path: str):
        """Save processed data to local file system"""
        try:
            # Ensure directory exists
            import os
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # Convert numpy array to image and save
            if len(data.shape) == 3:
                # Color image
                image = Image.fromarray(data.astype(np.uint8))
            else:
                # Grayscale image
                image = Image.fromarray(data.astype(np.uint8), mode='L')
            
            # Remove leading slash and save
            local_path = file_path.lstrip('/')
            image.save(local_path)
            
            logging.info(f"Saved file to {local_path}")
            
        except Exception as e:
            logging.error(f"Failed to save local file: {str(e)}")
            raise
    
    async def generate_stencil(self, baked_texture_url: str, artist_specs: dict):
        """Generate artist stencil from baked texture"""
        # This would implement stencil generation logic
        # For now, return mock data structure
        return type('StencilResult', (), {
            'session_id': f"stencil_{int(datetime.utcnow().timestamp())}",
            'stencil_image': np.zeros((512, 512), dtype=np.uint8)
        })()
    
    async def generate_svg_vector(self, baked_texture_url: str):
        """Generate SVG vector from baked texture"""
        # This would implement SVG vectorization
        return type('SVGResult', (), {
            'session_id': f"svg_{int(datetime.utcnow().timestamp())}",
            'svg_data': b'<svg></svg>'
        })()
    
    async def extract_color_palette(self, baked_texture_url: str):
        """Extract color palette from baked texture"""
        # This would implement color analysis
        return type('PaletteResult', (), {
            'colors': ['#000000', '#333333', '#666666'],
            'ink_brands': [
                {'color': '#000000', 'brand': 'Eternal Ink', 'code': 'EI-001'}
            ]
        })()
    
    async def create_artist_package(self, export_results: dict, package_path: str):
        """Create complete artist package ZIP"""
        # This would create a ZIP file with all assets
        import os
        os.makedirs(os.path.dirname(package_path), exist_ok=True)
        
        # Create empty file for now
        with open(package_path.lstrip('/'), 'w') as f:
            f.write("Artist package placeholder")