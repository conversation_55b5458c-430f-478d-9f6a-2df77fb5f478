import aiohttp
import asyncio
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import os
import json

@dataclass
class FluxGenerationResult:
    generation_id: str
    image_id: Optional[str]
    image_url: Optional[str]
    trace_id: str
    processing_time: float

@dataclass
class StylePreset:
    id: str
    name: str
    description: str
    category: str

class LeonardoService:
    """
    Leonardo AI service with FLUX.1 Kontext [pro] integration for professional tattoo generation
    """
    
    def __init__(self):
        self.api_key = os.getenv('LEONARDO_API_KEY')
        self.base_url = 'https://cloud.leonardo.ai/api/rest/v1'
        self.flux_model_id = '28aeddf8-bd19-4803-80fc-79602d1a9989'
        
        # FLUX.1 Kontext allowed dimensions
        self.allowed_sizes = [672, 720, 752, 832, 880, 944, 1024, 1104, 1184, 1248, 1392, 1456, 1568]
        
        # Body part to optimal size mapping
        self.body_part_sizes = {
            'forearm': (832, 1248),
            'calf': (832, 1248),
            'upper_arm': (1024, 1456),
            'hand': (944, 1392),
            'torso': (1248, 1568),
            'back': (1248, 1568),
            'chest': (1248, 1568)
        }
        
        # Style presets optimized for tattoos
        self.style_presets = {
            'realistic_bw': StylePreset(
                id='22a9a7d2-2166-4d86-80ff-22e2643adbcf',
                name='Realistic Black & Grey',
                description='Pro B&W photography style for realistic tattoo rendering',
                category='realism'
            ),
            'portrait': StylePreset(
                id='8e2bc543-a2a5-4c93-b59c-7b3a5a2d8f91',
                name='Portrait',
                description='Realistic lighting reference for detailed work',
                category='realism'
            ),
            'ray_traced': StylePreset(
                id='b504f83c-7e2d-4a9b-8c6f-3d5e1a2b9c8d',
                name='Ray Traced',
                description='Strong shading for depth and dimension',
                category='realism'
            ),
            'graphic_design': StylePreset(
                id='703d6fe5-7f1c-4a9e-8da0-5331f214d5cf',
                name='Graphic Design 2D',
                description='Clean linework for flash and traditional styles',
                category='linework'
            ),
            'illustration': StylePreset(
                id='645e4195-8b3c-4d2f-9e5a-1f6b7c8d9e0f',
                name='Illustration',
                description='Artistic illustration style with bold lines',
                category='linework'
            )
        }
        
        logging.info("Leonardo AI FLUX.1 Kontext service initialized")
    
    async def health_check(self) -> bool:
        """Check if Leonardo API is accessible"""
        try:
            if not self.api_key:
                return False
            
            async with aiohttp.ClientSession() as session:
                headers = {
                    'Authorization': f'Bearer {self.api_key}',
                    'Accept': 'application/json'
                }
                
                async with session.get(f'{self.base_url}/me', headers=headers) as response:
                    return response.status == 200
                    
        except Exception:
            return False
    
    def _validate_size(self, width: int, height: int) -> bool:
        """Validate that dimensions are allowed for FLUX.1 Kontext"""
        return width in self.allowed_sizes and height in self.allowed_sizes
    
    def _get_optimal_size(self, body_part: str) -> Tuple[int, int]:
        """Get optimal dimensions for body part"""
        return self.body_part_sizes.get(body_part, (832, 1248))
    
    def _build_tattoo_prompt(
        self,
        subject: str,
        body_part: str,
        style: str = 'realistic_bw',
        additional_instructions: str = ''
    ) -> str:
        """Build optimized prompt for tattoo generation"""
        
        # Base tattoo prompt template
        if style in ['realistic_bw', 'portrait', 'ray_traced']:
            base_template = (
                f"{subject}, black-and-grey tattoo design for {body_part}, "
                f"ultra-realistic texture and form, crisp linework, high micro-contrast, "
                f"flat artwork, NO background, NO border, NO text, margins 6-8% all sides, "
                f"transparent PNG look, ready for stencil conversion"
            )
        else:  # linework styles
            base_template = (
                f"{subject}, neo-traditional tattoo design for {body_part}, "
                f"bold outlines, controlled color palette, flat artwork on transparent background, "
                f"no background, no posterization, margin 6%, clean edges for stencil"
            )
        
        # Add additional instructions
        if additional_instructions:
            base_template += f", {additional_instructions}"
        
        return base_template
    
    def _get_negative_prompt(self) -> str:
        """Get negative prompt to exclude unwanted elements"""
        return "background, frame, watermark, poster, paper texture, border, text, logo"
    
    async def generate_tattoo(
        self,
        subject: str,
        body_part: str = 'forearm',
        style: str = 'realistic_bw',
        width: Optional[int] = None,
        height: Optional[int] = None,
        context_images: Optional[List[Dict]] = None,
        seed: Optional[int] = None,
        additional_instructions: str = '',
        enhance_prompt: bool = False
    ) -> FluxGenerationResult:
        """
        Generate tattoo design using FLUX.1 Kontext
        """
        start_time = datetime.utcnow()
        trace_id = f"flux_{int(start_time.timestamp())}"
        
        try:
            # Get optimal size if not specified
            if not width or not height:
                width, height = self._get_optimal_size(body_part)
            
            # Validate size
            if not self._validate_size(width, height):
                raise ValueError(f"Invalid size {width}x{height}. Must use allowed FLUX dimensions.")
            
            # Build prompt
            prompt = self._build_tattoo_prompt(subject, body_part, style, additional_instructions)
            
            # Get style UUID
            style_preset = self.style_presets.get(style)
            style_uuid = style_preset.id if style_preset else None
            
            # Build request payload
            payload = {
                'prompt': prompt,
                'modelId': self.flux_model_id,
                'num_images': 1,  # FLUX.1 Kontext constraint
                'width': width,
                'height': height,
                'enhancePrompt': enhance_prompt
            }
            
            if style_uuid:
                payload['styleUUID'] = style_uuid
            
            if context_images:
                payload['contextImages'] = context_images
            
            if seed:
                payload['seed'] = min(seed, 2147483638)  # FLUX seed limit
            
            # Make API request
            async with aiohttp.ClientSession() as session:
                headers = {
                    'Authorization': f'Bearer {self.api_key}',
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
                
                async with session.post(
                    f'{self.base_url}/generations',
                    headers=headers,
                    json=payload
                ) as response:
                    
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"Leonardo API error {response.status}: {error_text}")
                    
                    result = await response.json()
                    
                    processing_time = (datetime.utcnow() - start_time).total_seconds()
                    
                    return FluxGenerationResult(
                        generation_id=result['sdGenerationJob']['generationId'],
                        image_id=None,  # Will be available after processing
                        image_url=None,  # Will be available after processing
                        trace_id=trace_id,
                        processing_time=processing_time
                    )
        
        except Exception as e:
            logging.error(f"FLUX generation failed: {str(e)}")
            raise
    
    async def create_variation(
        self,
        base_prompt: str,
        base_image_id: str,
        image_type: str = 'GENERATED',  # 'GENERATED' or 'UPLOADED'
        body_part: str = 'forearm',
        style: str = 'realistic_bw',
        seed: Optional[int] = None,
        variation_instructions: str = ''
    ) -> FluxGenerationResult:
        """
        Create variation from existing image using context
        """
        start_time = datetime.utcnow()
        trace_id = f"flux_var_{int(start_time.timestamp())}"
        
        try:
            # Get optimal size for body part
            width, height = self._get_optimal_size(body_part)
            
            # Build variation prompt
            if variation_instructions:
                prompt = f"{base_prompt}; {variation_instructions}; keep composition, no background"
            else:
                prompt = f"{base_prompt}; refine details, enhance clarity, no background"
            
            # Context image reference
            context_images = [{
                'type': image_type,
                'id': base_image_id
            }]
            
            # Generate variation
            return await self.generate_tattoo(
                subject=prompt,
                body_part=body_part,
                style=style,
                width=width,
                height=height,
                context_images=context_images,
                seed=seed
            )
            
        except Exception as e:
            logging.error(f"Variation creation failed: {str(e)}")
            raise
    
    async def get_generation_status(self, generation_id: str) -> Dict:
        """
        Check generation status and get results when ready
        """
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    'Authorization': f'Bearer {self.api_key}',
                    'Accept': 'application/json'
                }
                
                async with session.get(
                    f'{self.base_url}/generations/{generation_id}',
                    headers=headers
                ) as response:
                    
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"Status check failed {response.status}: {error_text}")
                    
                    return await response.json()
        
        except Exception as e:
            logging.error(f"Status check failed: {str(e)}")
            raise
    
    async def upload_image(self, image_data: bytes, filename: str) -> str:
        """
        Upload image to Leonardo for use in context
        """
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    'Authorization': f'Bearer {self.api_key}'
                }
                
                # Create form data
                data = aiohttp.FormData()
                data.add_field('image', image_data, filename=filename, content_type='image/png')
                
                async with session.post(
                    f'{self.base_url}/upload',
                    headers=headers,
                    data=data
                ) as response:
                    
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"Upload failed {response.status}: {error_text}")
                    
                    result = await response.json()
                    return result['uploadedImageId']
        
        except Exception as e:
            logging.error(f"Image upload failed: {str(e)}")
            raise
    
    def get_style_presets(self) -> Dict[str, StylePreset]:
        """Get available style presets"""
        return self.style_presets
    
    def get_body_part_sizes(self) -> Dict[str, Tuple[int, int]]:
        """Get optimal sizes for each body part"""
        return self.body_part_sizes
    
    async def wait_for_completion(
        self,
        generation_id: str,
        max_wait_time: int = 300,
        poll_interval: int = 5
    ) -> Dict:
        """
        Wait for generation to complete and return results
        """
        start_time = datetime.utcnow()
        
        while (datetime.utcnow() - start_time).total_seconds() < max_wait_time:
            try:
                status = await self.get_generation_status(generation_id)
                
                # Check if complete
                if status.get('generations_by_pk', {}).get('status') == 'COMPLETE':
                    images = status.get('generations_by_pk', {}).get('generated_images', [])
                    if images:
                        return {
                            'status': 'COMPLETE',
                            'image_id': images[0]['id'],
                            'image_url': images[0]['url'],
                            'generation_id': generation_id
                        }
                
                # Check if failed
                elif status.get('generations_by_pk', {}).get('status') == 'FAILED':
                    return {
                        'status': 'FAILED',
                        'error': 'Generation failed'
                    }
                
                # Wait before next poll
                await asyncio.sleep(poll_interval)
                
            except Exception as e:
                logging.error(f"Polling error: {str(e)}")
                await asyncio.sleep(poll_interval)
        
        return {
            'status': 'TIMEOUT',
            'error': 'Generation timed out'
        }