from google.cloud import storage
import os
import logging
from typing import Optional
import uuid
from datetime import datetime, timedelta
import asyncio

class GCPStorageService:
    """
    Google Cloud Storage service for scalable file storage
    """
    
    def __init__(self):
        self.bucket_name = os.getenv('GCP_STORAGE_BUCKET', 'tattoo-ai-storage')
        self.client = None
        self.bucket = None
        self._initialize_storage()
        logging.info("GCP Storage Service initialized")
    
    def _initialize_storage(self):
        """Initialize Google Cloud Storage client"""
        try:
            # Initialize storage client
            self.client = storage.Client()
            self.bucket = self.client.bucket(self.bucket_name)
            
        except Exception as e:
            logging.error(f"GCP Storage initialization failed: {str(e)}")
            self.client = None
            self.bucket = None
    
    async def health_check(self) -> bool:
        """Check if GCP Storage is accessible"""
        try:
            if not self.client or not self.bucket:
                return False
            
            # Test bucket access
            self.bucket.reload()
            return True
            
        except Exception:
            return False
    
    async def upload_file(
        self,
        data: bytes,
        filename: str,
        content_type: str = 'image/png',
        folder: str = 'scans'
    ) -> str:
        """
        Upload file to GCP Storage and return public URL
        """
        try:
            if not self.client or not self.bucket:
                raise Exception("GCP Storage not initialized")
            
            # Generate unique filename
            timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
            unique_id = str(uuid.uuid4())[:8]
            blob_name = f"{folder}/{timestamp}_{unique_id}_{filename}"
            
            # Create blob and upload
            blob = self.bucket.blob(blob_name)
            blob.upload_from_string(data, content_type=content_type)
            
            # Make blob publicly readable
            blob.make_public()
            
            return blob.public_url
            
        except Exception as e:
            logging.error(f"File upload failed: {str(e)}")
            raise
    
    async def upload_file_from_path(
        self,
        file_path: str,
        destination_name: str,
        folder: str = 'exports'
    ) -> str:
        """
        Upload file from local path to GCP Storage
        """
        try:
            if not self.client or not self.bucket:
                raise Exception("GCP Storage not initialized")
            
            # Generate blob name
            timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
            blob_name = f"{folder}/{timestamp}_{destination_name}"
            
            # Upload file
            blob = self.bucket.blob(blob_name)
            blob.upload_from_filename(file_path)
            
            # Make blob publicly readable
            blob.make_public()
            
            return blob.public_url
            
        except Exception as e:
            logging.error(f"File upload from path failed: {str(e)}")
            raise
    
    async def download_file(
        self,
        blob_name: str,
        destination_path: str
    ) -> bool:
        """
        Download file from GCP Storage to local path
        """
        try:
            if not self.client or not self.bucket:
                return False
            
            blob = self.bucket.blob(blob_name)
            blob.download_to_filename(destination_path)
            
            return True
            
        except Exception as e:
            logging.error(f"File download failed: {str(e)}")
            return False
    
    async def delete_file(self, blob_name: str) -> bool:
        """
        Delete file from GCP Storage
        """
        try:
            if not self.client or not self.bucket:
                return False
            
            blob = self.bucket.blob(blob_name)
            blob.delete()
            
            return True
            
        except Exception as e:
            logging.error(f"File deletion failed: {str(e)}")
            return False
    
    async def generate_signed_url(
        self,
        blob_name: str,
        expiration_hours: int = 24
    ) -> Optional[str]:
        """
        Generate signed URL for temporary access to private files
        """
        try:
            if not self.client or not self.bucket:
                return None
            
            blob = self.bucket.blob(blob_name)
            
            # Generate signed URL with expiration
            expiration = datetime.utcnow() + timedelta(hours=expiration_hours)
            signed_url = blob.generate_signed_url(
                expiration=expiration,
                method='GET'
            )
            
            return signed_url
            
        except Exception as e:
            logging.error(f"Signed URL generation failed: {str(e)}")
            return None
    
    async def list_user_files(
        self,
        user_id: str,
        folder: str = 'scans'
    ) -> list:
        """
        List all files for a specific user
        """
        try:
            if not self.client or not self.bucket:
                return []
            
            # List blobs with user prefix
            prefix = f"{folder}/{user_id}/"
            blobs = self.bucket.list_blobs(prefix=prefix)
            
            files = []
            for blob in blobs:
                files.append({
                    'name': blob.name,
                    'size': blob.size,
                    'created': blob.time_created,
                    'updated': blob.updated,
                    'content_type': blob.content_type,
                    'public_url': blob.public_url if blob.public_url_set else None
                })
            
            return files
            
        except Exception as e:
            logging.error(f"File listing failed: {str(e)}")
            return []
    
    async def get_file_metadata(self, blob_name: str) -> Optional[dict]:
        """
        Get metadata for a specific file
        """
        try:
            if not self.client or not self.bucket:
                return None
            
            blob = self.bucket.blob(blob_name)
            blob.reload()
            
            return {
                'name': blob.name,
                'size': blob.size,
                'created': blob.time_created,
                'updated': blob.updated,
                'content_type': blob.content_type,
                'md5_hash': blob.md5_hash,
                'etag': blob.etag,
                'public_url': blob.public_url if blob.public_url_set else None
            }
            
        except Exception as e:
            logging.error(f"Metadata retrieval failed: {str(e)}")
            return None
    
    async def create_backup(
        self,
        source_folder: str,
        backup_name: str
    ) -> str:
        """
        Create backup of a folder
        """
        try:
            if not self.client or not self.bucket:
                raise Exception("GCP Storage not initialized")
            
            timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
            backup_folder = f"backups/{backup_name}_{timestamp}"
            
            # List all files in source folder
            source_blobs = self.bucket.list_blobs(prefix=f"{source_folder}/")
            
            copied_files = 0
            for source_blob in source_blobs:
                # Create backup blob name
                relative_path = source_blob.name[len(source_folder)+1:]
                backup_blob_name = f"{backup_folder}/{relative_path}"
                
                # Copy blob
                backup_blob = self.bucket.copy_blob(
                    source_blob, 
                    self.bucket, 
                    backup_blob_name
                )
                copied_files += 1
            
            logging.info(f"Created backup {backup_folder} with {copied_files} files")
            return backup_folder
            
        except Exception as e:
            logging.error(f"Backup creation failed: {str(e)}")
            raise
    
    async def cleanup_old_files(
        self,
        folder: str,
        days_old: int = 30
    ) -> int:
        """
        Clean up files older than specified days
        """
        try:
            if not self.client or not self.bucket:
                return 0
            
            cutoff_date = datetime.utcnow() - timedelta(days=days_old)
            
            # List all files in folder
            blobs = self.bucket.list_blobs(prefix=f"{folder}/")
            
            deleted_count = 0
            for blob in blobs:
                if blob.time_created < cutoff_date:
                    blob.delete()
                    deleted_count += 1
            
            logging.info(f"Cleaned up {deleted_count} old files from {folder}")
            return deleted_count
            
        except Exception as e:
            logging.error(f"Cleanup failed: {str(e)}")
            return 0