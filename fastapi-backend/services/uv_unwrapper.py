import cv2
import numpy as np
from scipy.spatial import distance_matrix
from skimage import transform
from typing import Tuple
from dataclasses import dataclass
from datetime import datetime
import logging

@dataclass
class UVResult:
    uv_texture: np.ndarray
    normal_map: np.ndarray
    session_id: str
    quality_score: float
    processing_time: float

class UVUnwrapperService:
    """
    UV unwrapping service for converting 3D surfaces to 2D texture coordinates
    """
    
    def __init__(self):
        logging.info("UV Unwrapper Service initialized")
    
    async def health_check(self) -> bool:
        """Check if UV unwrapping service is operational"""
        return True
    
    async def generate_uv_mapping(
        self,
        depth_map_url: str,
        mask_url: str,
        method: str = "cylindrical"
    ) -> UVResult:
        """
        Generate UV texture coordinates from depth map and mask
        """
        start_time = datetime.utcnow()
        session_id = f"uv_{int(start_time.timestamp())}"
        
        try:
            # Load depth map and mask
            depth_map = self._load_image(depth_map_url, cv2.IMREAD_GRAYSCALE)
            mask = self._load_image(mask_url, cv2.IMREAD_GRAYSCALE)
            
            if depth_map is None or mask is None:
                raise ValueError("Could not load depth map or mask")
            
            # Generate UV mapping based on method
            if method == "cylindrical":
                uv_texture, normal_map = self._cylindrical_unwrap(depth_map, mask)
            elif method == "planar":
                uv_texture, normal_map = self._planar_unwrap(depth_map, mask)
            else:
                raise ValueError(f"Unknown unwrap method: {method}")
            
            # Calculate quality score
            quality_score = self._calculate_uv_quality(uv_texture, normal_map)
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            return UVResult(
                uv_texture=uv_texture,
                normal_map=normal_map,
                session_id=session_id,
                quality_score=quality_score,
                processing_time=processing_time
            )
            
        except Exception as e:
            logging.error(f"UV unwrapping failed: {str(e)}")
            raise
    
    def _load_image(self, image_url: str, flags: int) -> np.ndarray:
        """Load image from URL or local path"""
        try:
            if image_url.startswith('http'):
                # Download from URL
                import requests
                response = requests.get(image_url)
                nparr = np.frombuffer(response.content, np.uint8)
                return cv2.imdecode(nparr, flags)
            else:
                # Load from local path
                local_path = image_url.lstrip('/')
                return cv2.imread(local_path, flags)
        except Exception as e:
            logging.error(f"Failed to load image {image_url}: {str(e)}")
            return None
    
    def _cylindrical_unwrap(
        self,
        depth_map: np.ndarray,
        mask: np.ndarray
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Perform cylindrical UV unwrapping for limbs (arms, legs)
        """
        height, width = depth_map.shape
        
        # Find mask boundaries
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            raise ValueError("No contours found in mask")
        
        # Get largest contour (main body part)
        main_contour = max(contours, key=cv2.contourArea)
        
        # Find principal axis of the limb
        principal_axis = self._find_principal_axis(main_contour)
        
        # Create cylindrical coordinate system
        uv_width, uv_height = 512, 512
        uv_texture = np.zeros((uv_height, uv_width, 3), dtype=np.uint8)
        normal_map = np.zeros((uv_height, uv_width, 3), dtype=np.uint8)
        
        # Map pixels from 3D space to UV space
        for y in range(height):
            for x in range(width):
                if mask[y, x] > 0:
                    # Convert to cylindrical coordinates
                    u, v = self._cartesian_to_cylindrical(
                        x, y, depth_map[y, x], principal_axis, width, height
                    )
                    
                    # Map to UV texture space
                    u_pixel = int(u * uv_width)
                    v_pixel = int(v * uv_height)
                    
                    if 0 <= u_pixel < uv_width and 0 <= v_pixel < uv_height:
                        # Sample original image color (for now use depth as color)
                        color_value = depth_map[y, x]
                        uv_texture[v_pixel, u_pixel] = [color_value, color_value, color_value]
                        
                        # Calculate normal vector
                        normal = self._calculate_normal(depth_map, x, y)
                        normal_map[v_pixel, u_pixel] = self._normal_to_color(normal)
        
        # Fill holes and smooth
        uv_texture = self._fill_uv_holes(uv_texture)
        normal_map = self._fill_uv_holes(normal_map)
        
        return uv_texture, normal_map
    
    def _planar_unwrap(
        self,
        depth_map: np.ndarray,
        mask: np.ndarray
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Perform planar UV unwrapping for flat surfaces (hands, torso)
        """
        height, width = depth_map.shape
        
        # Simple planar mapping - just resize and normalize
        uv_width, uv_height = 512, 512
        
        # Resize depth map to UV dimensions
        uv_texture = cv2.resize(depth_map, (uv_width, uv_height))
        uv_texture = cv2.cvtColor(uv_texture, cv2.COLOR_GRAY2RGB)
        
        # Generate normal map from depth
        normal_map = self._depth_to_normal_map(
            cv2.resize(depth_map, (uv_width, uv_height))
        )
        
        return uv_texture, normal_map
    
    def _find_principal_axis(self, contour: np.ndarray) -> Tuple[float, float]:
        """
        Find the principal axis of the contour using PCA
        """
        # Reshape contour for PCA
        points = contour.reshape(-1, 2).astype(np.float32)
        
        # Calculate covariance matrix
        mean = np.mean(points, axis=0)
        centered_points = points - mean
        cov_matrix = np.cov(centered_points.T)
        
        # Get principal component (first eigenvector)
        eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)
        principal_component = eigenvectors[:, np.argmax(eigenvalues)]
        
        # Return as angle
        angle = np.arctan2(principal_component[1], principal_component[0])
        return angle, np.linalg.norm(principal_component)
    
    def _cartesian_to_cylindrical(
        self,
        x: int,
        y: int,
        depth: float,
        principal_axis: Tuple[float, float],
        width: int,
        height: int
    ) -> Tuple[float, float]:
        """
        Convert cartesian coordinates to cylindrical UV coordinates
        """
        angle, _ = principal_axis
        
        # Center coordinates
        center_x, center_y = width // 2, height // 2
        rel_x, rel_y = x - center_x, y - center_y
        
        # Rotate by principal axis
        rotated_x = rel_x * np.cos(-angle) - rel_y * np.sin(-angle)
        rotated_y = rel_x * np.sin(-angle) + rel_y * np.cos(-angle)
        
        # Convert to cylindrical
        radius = np.sqrt(rotated_x**2 + depth**2)
        theta = np.arctan2(rotated_x, depth)
        
        # Normalize to UV coordinates [0, 1]
        u = (theta + np.pi) / (2 * np.pi)  # Angle around cylinder
        v = (rotated_y + height // 2) / height  # Height along cylinder
        
        return np.clip(u, 0, 1), np.clip(v, 0, 1)
    
    def _calculate_normal(
        self,
        depth_map: np.ndarray,
        x: int,
        y: int
    ) -> np.ndarray:
        """
        Calculate surface normal at given point using depth gradient
        """
        height, width = depth_map.shape
        
        # Calculate gradients
        dx = 0
        dy = 0
        
        if x > 0 and x < width - 1:
            dx = float(depth_map[y, x + 1]) - float(depth_map[y, x - 1])
        if y > 0 and y < height - 1:
            dy = float(depth_map[y + 1, x]) - float(depth_map[y - 1, x])
        
        # Normal vector (negative gradients for outward normal)
        normal = np.array([-dx, -dy, 2.0])
        
        # Normalize
        length = np.linalg.norm(normal)
        if length > 0:
            normal = normal / length
        
        return normal
    
    def _normal_to_color(self, normal: np.ndarray) -> np.ndarray:
        """
        Convert normal vector to RGB color for normal map
        """
        # Map from [-1, 1] to [0, 255]
        color = ((normal + 1.0) * 127.5).astype(np.uint8)
        return color
    
    def _depth_to_normal_map(self, depth_map: np.ndarray) -> np.ndarray:
        """
        Generate normal map from depth map using Sobel filters
        """
        # Calculate gradients
        grad_x = cv2.Sobel(depth_map, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(depth_map, cv2.CV_64F, 0, 1, ksize=3)
        
        # Create normal vectors
        height, width = depth_map.shape
        normal_map = np.zeros((height, width, 3), dtype=np.uint8)
        
        for y in range(height):
            for x in range(width):
                # Normal vector
                nx = -grad_x[y, x] / 255.0
                ny = -grad_y[y, x] / 255.0
                nz = 1.0
                
                # Normalize
                length = np.sqrt(nx*nx + ny*ny + nz*nz)
                if length > 0:
                    nx, ny, nz = nx/length, ny/length, nz/length
                
                # Convert to color
                normal_map[y, x] = [
                    int((nx + 1.0) * 127.5),
                    int((ny + 1.0) * 127.5),
                    int((nz + 1.0) * 127.5)
                ]
        
        return normal_map
    
    def _fill_uv_holes(self, uv_image: np.ndarray) -> np.ndarray:
        """
        Fill holes in UV texture using inpainting
        """
        # Create mask of empty pixels
        if len(uv_image.shape) == 3:
            mask = np.all(uv_image == 0, axis=2).astype(np.uint8) * 255
        else:
            mask = (uv_image == 0).astype(np.uint8) * 255
        
        # Inpaint holes
        inpainted = cv2.inpaint(uv_image, mask, 3, cv2.INPAINT_TELEA)
        
        return inpainted
    
    def _calculate_uv_quality(
        self,
        uv_texture: np.ndarray,
        normal_map: np.ndarray
    ) -> float:
        """
        Calculate quality score for UV unwrapping
        """
        quality_factors = []
        
        # Factor 1: Texture coverage
        if len(uv_texture.shape) == 3:
            non_zero_pixels = np.sum(np.any(uv_texture > 0, axis=2))
        else:
            non_zero_pixels = np.sum(uv_texture > 0)
        
        total_pixels = uv_texture.shape[0] * uv_texture.shape[1]
        coverage = non_zero_pixels / total_pixels
        quality_factors.append(coverage)
        
        # Factor 2: Normal map smoothness
        gray_normals = cv2.cvtColor(normal_map, cv2.COLOR_RGB2GRAY)
        laplacian = cv2.Laplacian(gray_normals, cv2.CV_64F)
        smoothness = 1.0 / (1.0 + np.std(laplacian))
        quality_factors.append(smoothness)
        
        # Factor 3: Texture uniformity
        if len(uv_texture.shape) == 3:
            texture_std = np.std(cv2.cvtColor(uv_texture, cv2.COLOR_RGB2GRAY))
        else:
            texture_std = np.std(uv_texture)
        
        uniformity = 1.0 / (1.0 + texture_std / 255.0)
        quality_factors.append(uniformity)
        
        # Calculate weighted average
        weights = [0.5, 0.3, 0.2]
        quality_score = np.average(quality_factors, weights=weights)
        
        return float(quality_score)
    
    async def save_uv_maps(
        self,
        uv_map: np.ndarray,
        normal_map: np.ndarray,
        uv_path: str,
        normal_path: str
    ):
        """
        Save UV maps to local files
        """
        try:
            import os
            
            # Ensure directories exist
            os.makedirs(os.path.dirname(uv_path.lstrip('/')), exist_ok=True)
            os.makedirs(os.path.dirname(normal_path.lstrip('/')), exist_ok=True)
            
            # Save UV texture
            cv2.imwrite(uv_path.lstrip('/'), uv_map)
            
            # Save normal map
            cv2.imwrite(normal_path.lstrip('/'), normal_map)
            
            logging.info(f"Saved UV maps to {uv_path} and {normal_path}")
            
        except Exception as e:
            logging.error(f"Failed to save UV maps: {str(e)}")
            raise