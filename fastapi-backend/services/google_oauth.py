import os
import json
import logging
from typing import Optional, Dict, Any
import httpx
from fastapi import HTT<PERSON>Exception, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from google.auth.transport import requests
from google.oauth2 import id_token
import jwt
from datetime import datetime, timedelta

class GoogleOAuthService:
    """
    Google OAuth 2.0 service for authentication
    """
    
    def __init__(self):
        self.client_id = os.getenv('GOOGLE_CLIENT_ID')
        self.client_secret = os.getenv('GOOGLE_CLIENT_SECRET')
        self.redirect_uri = os.getenv('GOOGLE_REDIRECT_URI', 'http://localhost:8000/auth/google/callback')
        self.jwt_secret = os.getenv('JWT_SECRET', 'your-secret-key')
        
        if not self.client_id or not self.client_secret:
            logging.error("Google OAuth credentials not configured")
            raise ValueError("GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET must be set")
        
        # Google OAuth endpoints
        self.auth_url = "https://accounts.google.com/o/oauth2/auth"
        self.token_url = "https://oauth2.googleapis.com/token"
        self.userinfo_url = "https://www.googleapis.com/oauth2/v2/userinfo"
        
        logging.info("Google OAuth Service initialized")
    
    def get_authorization_url(self, state: Optional[str] = None) -> str:
        """
        Generate Google OAuth authorization URL
        """
        params = {
            'client_id': self.client_id,
            'redirect_uri': self.redirect_uri,
            'scope': 'openid email profile',
            'response_type': 'code',
            'access_type': 'offline',
            'prompt': 'consent'
        }
        
        if state:
            params['state'] = state
        
        query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
        return f"{self.auth_url}?{query_string}"
    
    async def exchange_code_for_token(self, code: str) -> Dict[str, Any]:
        """
        Exchange authorization code for access token
        """
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.token_url,
                    data={
                        'client_id': self.client_id,
                        'client_secret': self.client_secret,
                        'code': code,
                        'grant_type': 'authorization_code',
                        'redirect_uri': self.redirect_uri
                    },
                    headers={'Content-Type': 'application/x-www-form-urlencoded'}
                )
                
                if response.status_code != 200:
                    logging.error(f"Token exchange failed: {response.text}")
                    raise HTTPException(status_code=400, detail="Failed to exchange code for token")
                
                return response.json()
        
        except httpx.RequestError as e:
            logging.error(f"HTTP request failed: {str(e)}")
            raise HTTPException(status_code=500, detail="Authentication service unavailable")
    
    async def get_user_info(self, access_token: str) -> Dict[str, Any]:
        """
        Get user information from Google
        """
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    self.userinfo_url,
                    headers={'Authorization': f'Bearer {access_token}'}
                )
                
                if response.status_code != 200:
                    logging.error(f"User info request failed: {response.text}")
                    raise HTTPException(status_code=400, detail="Failed to get user information")
                
                return response.json()
        
        except httpx.RequestError as e:
            logging.error(f"HTTP request failed: {str(e)}")
            raise HTTPException(status_code=500, detail="Authentication service unavailable")
    
    def verify_google_token(self, token: str) -> Dict[str, Any]:
        """
        Verify Google ID token
        """
        try:
            # Verify the token
            idinfo = id_token.verify_oauth2_token(
                token, 
                requests.Request(), 
                self.client_id
            )
            
            # Verify the issuer
            if idinfo['iss'] not in ['accounts.google.com', 'https://accounts.google.com']:
                raise ValueError('Wrong issuer.')
            
            return idinfo
        
        except ValueError as e:
            logging.error(f"Token verification failed: {str(e)}")
            raise HTTPException(status_code=401, detail="Invalid Google token")
    
    def create_jwt_token(self, user_data: Dict[str, Any]) -> str:
        """
        Create JWT token for authenticated user
        """
        payload = {
            'user_id': user_data.get('sub'),  # Google user ID
            'email': user_data.get('email'),
            'name': user_data.get('name'),
            'picture': user_data.get('picture'),
            'exp': datetime.utcnow() + timedelta(hours=24),
            'iat': datetime.utcnow(),
            'iss': 'tattoo-admin',
            'aud': 'tattoo-admin-users'
        }
        
        return jwt.encode(payload, self.jwt_secret, algorithm='HS256')
    
    def verify_jwt_token(self, token: str) -> Dict[str, Any]:
        """
        Verify JWT token
        """
        try:
            payload = jwt.decode(
                token, 
                self.jwt_secret, 
                algorithms=['HS256'],
                audience='tattoo-admin-users',
                issuer='tattoo-admin'
            )
            return payload
        
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="Token has expired")
        except jwt.InvalidTokenError:
            raise HTTPException(status_code=401, detail="Invalid token")
    
    async def authenticate_user(self, code: str) -> Dict[str, Any]:
        """
        Complete OAuth flow and return user data with JWT
        """
        # Exchange code for tokens
        token_data = await self.exchange_code_for_token(code)
        
        # Get user information
        if 'id_token' in token_data:
            # Verify ID token
            user_info = self.verify_google_token(token_data['id_token'])
        else:
            # Fallback to userinfo endpoint
            user_info = await self.get_user_info(token_data['access_token'])
        
        # Create JWT token
        jwt_token = self.create_jwt_token(user_info)
        
        return {
            'user': {
                'id': user_info.get('sub'),
                'email': user_info.get('email'),
                'name': user_info.get('name'),
                'picture': user_info.get('picture'),
                'verified_email': user_info.get('email_verified', False)
            },
            'token': jwt_token,
            'expires_at': (datetime.utcnow() + timedelta(hours=24)).isoformat()
        }
    
    async def get_current_user(self, token: str) -> Dict[str, Any]:
        """
        Get current user from JWT token
        """
        payload = self.verify_jwt_token(token)
        
        return {
            'id': payload.get('user_id'),
            'email': payload.get('email'),
            'name': payload.get('name'),
            'picture': payload.get('picture')
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Health check for Google OAuth service
        """
        try:
            # Test Google's well-known configuration endpoint
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://accounts.google.com/.well-known/openid_configuration",
                    timeout=5.0
                )
                
                if response.status_code == 200:
                    return {
                        "status": "healthy",
                        "service": "google_oauth",
                        "configured": bool(self.client_id and self.client_secret)
                    }
                else:
                    return {
                        "status": "unhealthy",
                        "service": "google_oauth",
                        "error": f"Google service returned {response.status_code}"
                    }
        
        except Exception as e:
            return {
                "status": "unhealthy",
                "service": "google_oauth",
                "error": str(e)
            }

# Security dependency for FastAPI
security = HTTPBearer()

async def get_current_user_dependency(
    credentials: HTTPAuthorizationCredentials = security
) -> Dict[str, Any]:
    """
    FastAPI dependency to get current authenticated user
    """
    if not credentials or not credentials.credentials:
        raise HTTPException(status_code=401, detail="Authentication required")
    
    google_oauth = GoogleOAuthService()
    return await google_oauth.get_current_user(credentials.credentials)
