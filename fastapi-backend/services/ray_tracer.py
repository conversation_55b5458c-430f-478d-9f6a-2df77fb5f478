import cv2
import numpy as np
from PIL import Image, ImageFilter, ImageEnhance
from typing import Dict, <PERSON><PERSON>
from dataclasses import dataclass
from datetime import datetime
import logging
import asyncio

@dataclass
class RayTraceResult:
    baked_texture: np.ndarray
    session_id: str
    quality_score: float
    lighting_accuracy: float
    processing_time: float

class RayTracerService:
    """
    Ray tracing service for realistic tattoo overlay with proper lighting and skin curvature
    """
    
    def __init__(self):
        self.default_lighting = {
            "ambient": 0.3,
            "diffuse": 0.7,
            "specular": 0.2,
            "shininess": 32,
            "light_direction": [0.5, 0.5, 1.0]
        }
        logging.info("Ray Tracer Service initialized")
    
    async def health_check(self) -> bool:
        """Check if ray tracing service is operational"""
        return True
    
    async def bake_tattoo_to_mesh(
        self,
        tattoo_image_url: str,
        uv_map_url: str,
        normal_map_url: str,
        lighting_config: Dict,
        path_trace: bool = True
    ) -> RayTraceResult:
        """
        Apply tattoo design with ray-traced lighting and skin curvature
        """
        start_time = datetime.utcnow()
        session_id = f"raytrace_{int(start_time.timestamp())}"
        
        try:
            # Load images
            tattoo_image = self._load_image(tattoo_image_url)
            uv_map = self._load_image(uv_map_url)
            normal_map = self._load_image(normal_map_url)
            
            if any(img is None for img in [tattoo_image, uv_map, normal_map]):
                raise ValueError("Could not load required images")
            
            # Merge lighting config with defaults
            lighting = {**self.default_lighting, **lighting_config}
            
            # Perform ray tracing
            if path_trace:
                baked_texture = await self._path_trace_baking(
                    tattoo_image, uv_map, normal_map, lighting
                )
            else:
                baked_texture = await self._standard_baking(
                    tattoo_image, uv_map, normal_map, lighting
                )
            
            # Calculate quality metrics
            quality_score = self._calculate_baking_quality(baked_texture, uv_map)
            lighting_accuracy = self._calculate_lighting_accuracy(
                baked_texture, normal_map, lighting
            )
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            return RayTraceResult(
                baked_texture=baked_texture,
                session_id=session_id,
                quality_score=quality_score,
                lighting_accuracy=lighting_accuracy,
                processing_time=processing_time
            )
            
        except Exception as e:
            logging.error(f"Ray tracing failed: {str(e)}")
            raise
    
    def _load_image(self, image_url: str) -> np.ndarray:
        """Load image from URL or local path"""
        try:
            if image_url.startswith('http'):
                import requests
                response = requests.get(image_url)
                nparr = np.frombuffer(response.content, np.uint8)
                return cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            else:
                local_path = image_url.lstrip('/')
                return cv2.imread(local_path, cv2.IMREAD_COLOR)
        except Exception as e:
            logging.error(f"Failed to load image {image_url}: {str(e)}")
            return None
    
    async def _path_trace_baking(
        self,
        tattoo_image: np.ndarray,
        uv_map: np.ndarray,
        normal_map: np.ndarray,
        lighting: Dict
    ) -> np.ndarray:
        """
        Advanced path tracing for realistic light interaction
        """
        height, width = uv_map.shape[:2]
        baked_texture = np.zeros((height, width, 3), dtype=np.float32)
        
        # Resize tattoo to match UV dimensions
        tattoo_resized = cv2.resize(tattoo_image, (width, height))
        
        # Convert to float for calculations
        tattoo_float = tattoo_resized.astype(np.float32) / 255.0
        normal_float = normal_map.astype(np.float32) / 255.0
        
        # Normalize normal vectors
        normals = (normal_float - 0.5) * 2.0  # Convert from [0,1] to [-1,1]
        
        # Light direction (normalized)
        light_dir = np.array(lighting["light_direction"], dtype=np.float32)
        light_dir = light_dir / np.linalg.norm(light_dir)
        
        # Calculate lighting for each pixel
        for y in range(height):
            for x in range(width):
                if np.any(uv_map[y, x] > 0):  # Only process valid UV coordinates
                    # Get surface normal
                    normal = normals[y, x]
                    normal_length = np.linalg.norm(normal)
                    
                    if normal_length > 0:
                        normal = normal / normal_length
                        
                        # Calculate diffuse lighting (Lambertian)
                        dot_product = np.dot(normal, light_dir)
                        diffuse = max(0, dot_product) * lighting["diffuse"]
                        
                        # Calculate specular reflection (Phong)
                        view_dir = np.array([0, 0, 1], dtype=np.float32)  # Camera looking down Z
                        reflect_dir = 2 * dot_product * normal - light_dir
                        specular_dot = max(0, np.dot(reflect_dir, view_dir))
                        specular = (specular_dot ** lighting["shininess"]) * lighting["specular"]
                        
                        # Combine lighting components
                        total_lighting = lighting["ambient"] + diffuse + specular
                        total_lighting = min(1.0, total_lighting)  # Clamp to avoid over-exposure
                        
                        # Apply lighting to tattoo color
                        tattoo_color = tattoo_float[y, x]
                        lit_color = tattoo_color * total_lighting
                        
                        # Simulate skin subsurface scattering
                        skin_color = np.array([0.8, 0.6, 0.5], dtype=np.float32)  # Average skin tone
                        subsurface = skin_color * 0.1 * (1.0 - total_lighting)
                        
                        # Blend tattoo with skin subsurface
                        final_color = lit_color + subsurface
                        baked_texture[y, x] = np.clip(final_color, 0, 1)
        
        # Convert back to uint8
        baked_texture = (baked_texture * 255).astype(np.uint8)
        
        # Apply post-processing for realism
        baked_texture = self._apply_skin_effects(baked_texture)
        
        return baked_texture
    
    async def _standard_baking(
        self,
        tattoo_image: np.ndarray,
        uv_map: np.ndarray,
        normal_map: np.ndarray,
        lighting: Dict
    ) -> np.ndarray:
        """
        Standard baking without path tracing for faster processing
        """
        height, width = uv_map.shape[:2]
        
        # Resize tattoo to match UV dimensions
        tattoo_resized = cv2.resize(tattoo_image, (width, height))
        
        # Create lighting mask from normal map
        normal_gray = cv2.cvtColor(normal_map, cv2.COLOR_BGR2GRAY)
        lighting_mask = cv2.applyColorMap(normal_gray, cv2.COLORMAP_JET)
        lighting_mask = lighting_mask.astype(np.float32) / 255.0
        
        # Apply basic lighting
        ambient = lighting["ambient"]
        diffuse_strength = lighting["diffuse"]
        
        # Calculate simple diffuse lighting
        diffuse_factor = normal_gray.astype(np.float32) / 255.0
        diffuse_factor = (diffuse_factor - 0.5) * 2.0  # Normalize to [-1, 1]
        diffuse_factor = np.clip(diffuse_factor * diffuse_strength + ambient, 0, 1)
        
        # Apply lighting to tattoo
        baked_texture = tattoo_resized.astype(np.float32)
        for c in range(3):  # Apply to each color channel
            baked_texture[:, :, c] *= diffuse_factor
        
        baked_texture = np.clip(baked_texture, 0, 255).astype(np.uint8)
        
        return baked_texture
    
    def _apply_skin_effects(self, baked_texture: np.ndarray) -> np.ndarray:
        """
        Apply realistic skin effects to baked texture
        """
        # Convert to PIL for advanced filters
        pil_image = Image.fromarray(cv2.cvtColor(baked_texture, cv2.COLOR_BGR2RGB))
        
        # Apply subtle blur for skin softness
        softened = pil_image.filter(ImageFilter.GaussianBlur(radius=0.5))
        
        # Enhance contrast slightly
        enhancer = ImageEnhance.Contrast(softened)
        contrasted = enhancer.enhance(1.1)
        
        # Adjust color balance for skin tone
        enhancer = ImageEnhance.Color(contrasted)
        color_balanced = enhancer.enhance(0.95)
        
        # Convert back to OpenCV format
        result = cv2.cvtColor(np.array(color_balanced), cv2.COLOR_RGB2BGR)
        
        return result
    
    def _calculate_baking_quality(
        self,
        baked_texture: np.ndarray,
        uv_map: np.ndarray
    ) -> float:
        """
        Calculate quality score for baked texture
        """
        quality_factors = []
        
        # Factor 1: Coverage - how much of UV space is filled
        if len(baked_texture.shape) == 3:
            filled_pixels = np.sum(np.any(baked_texture > 0, axis=2))
        else:
            filled_pixels = np.sum(baked_texture > 0)
        
        total_pixels = baked_texture.shape[0] * baked_texture.shape[1]
        coverage = filled_pixels / total_pixels
        quality_factors.append(coverage)
        
        # Factor 2: Color consistency
        if len(baked_texture.shape) == 3:
            color_std = np.std(baked_texture[baked_texture > 0])
        else:
            color_std = np.std(baked_texture[baked_texture > 0])
        
        consistency = 1.0 / (1.0 + color_std / 255.0)
        quality_factors.append(consistency)
        
        # Factor 3: Edge sharpness
        gray_texture = cv2.cvtColor(baked_texture, cv2.COLOR_BGR2GRAY) if len(baked_texture.shape) == 3 else baked_texture
        edges = cv2.Canny(gray_texture, 50, 150)
        edge_density = np.sum(edges > 0) / total_pixels
        sharpness = min(1.0, edge_density * 10)  # Normalize edge density
        quality_factors.append(sharpness)
        
        # Calculate weighted average
        weights = [0.4, 0.4, 0.2]
        quality_score = np.average(quality_factors, weights=weights)
        
        return float(quality_score)
    
    def _calculate_lighting_accuracy(
        self,
        baked_texture: np.ndarray,
        normal_map: np.ndarray,
        lighting: Dict
    ) -> float:
        """
        Calculate how accurately lighting was applied
        """
        # Convert to grayscale for analysis
        texture_gray = cv2.cvtColor(baked_texture, cv2.COLOR_BGR2GRAY)
        normal_gray = cv2.cvtColor(normal_map, cv2.COLOR_BGR2GRAY)
        
        # Calculate correlation between normal map and lighting
        correlation = cv2.matchTemplate(
            texture_gray, normal_gray, cv2.TM_CCOEFF_NORMED
        )
        
        # Lighting accuracy based on correlation
        accuracy = float(np.max(correlation))
        
        return max(0.0, min(1.0, accuracy))
    
    async def save_baked_texture(
        self,
        texture: np.ndarray,
        path: str
    ):
        """
        Save baked texture to local file
        """
        try:
            import os
            
            # Ensure directory exists
            os.makedirs(os.path.dirname(path.lstrip('/')), exist_ok=True)
            
            # Save texture
            cv2.imwrite(path.lstrip('/'), texture)
            
            logging.info(f"Saved baked texture to {path}")
            
        except Exception as e:
            logging.error(f"Failed to save baked texture: {str(e)}")
            raise