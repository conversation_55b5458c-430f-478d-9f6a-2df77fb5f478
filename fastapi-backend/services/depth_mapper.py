import cv2
import numpy as np
import mediapipe as mp
from typing import Dict, List, Tuple
from dataclasses import dataclass
from datetime import datetime
import logging

@dataclass
class DepthResult:
    depth_map: np.ndarray
    binary_mask: np.ndarray
    processing_time: float
    quality_score: float

class DepthMapperService:
    """
    Advanced depth mapping using MediaPipe and OpenCV stereo vision
    """
    
    def __init__(self):
        # Initialize MediaPipe pose for depth estimation
        self.mp_pose = mp.solutions.pose
        self.pose = self.mp_pose.Pose(
            static_image_mode=True,
            model_complexity=2,
            enable_segmentation=True,
            min_detection_confidence=0.8
        )
        
        # Initialize selfie segmentation for masking
        self.mp_segmentation = mp.solutions.selfie_segmentation
        self.segmentation = self.mp_segmentation.SelfieSegmentation(
            model_selection=1
        )
        
        logging.info("Depth Mapper Service initialized")
    
    async def health_check(self) -> bool:
        """Check if depth mapping service is operational"""
        try:
            test_image = np.zeros((100, 100, 3), dtype=np.uint8)
            result = self.pose.process(test_image)
            return True
        except Exception:
            return False
    
    async def generate_depth_map(
        self,
        image_data: bytes,
        cv_landmarks: List[Dict],
        body_part: str
    ) -> DepthResult:
        """
        Generate depth map from image using MediaPipe pose estimation and landmarks
        """
        start_time = datetime.utcnow()
        
        try:
            # Convert bytes to OpenCV image
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Generate segmentation mask
            segmentation_result = self.segmentation.process(rgb_image)
            binary_mask = (segmentation_result.segmentation_mask > 0.5).astype(np.uint8) * 255
            
            # Process pose for depth estimation
            pose_result = self.pose.process(rgb_image)
            
            # Generate depth map based on pose landmarks and body part
            depth_map = self._generate_depth_from_pose(
                image, pose_result, cv_landmarks, body_part
            )
            
            # Apply mask to depth map
            masked_depth = cv2.bitwise_and(depth_map, depth_map, mask=binary_mask)
            
            # Calculate quality score
            quality_score = self._calculate_quality_score(
                masked_depth, binary_mask, cv_landmarks
            )
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            return DepthResult(
                depth_map=masked_depth,
                binary_mask=binary_mask,
                processing_time=processing_time,
                quality_score=quality_score
            )
            
        except Exception as e:
            logging.error(f"Depth mapping failed: {str(e)}")
            raise
    
    def _generate_depth_from_pose(
        self,
        image: np.ndarray,
        pose_result,
        cv_landmarks: List[Dict],
        body_part: str
    ) -> np.ndarray:
        """
        Generate depth map using pose landmarks and geometric assumptions
        """
        height, width = image.shape[:2]
        depth_map = np.zeros((height, width), dtype=np.float32)
        
        if not pose_result.pose_landmarks:
            return depth_map.astype(np.uint8)
        
        # Extract 3D coordinates from pose landmarks
        landmarks_3d = []
        for landmark in pose_result.pose_landmarks.landmark:
            landmarks_3d.append([
                landmark.x * width,
                landmark.y * height,
                landmark.z  # Relative depth from MediaPipe
            ])
        
        landmarks_3d = np.array(landmarks_3d)
        
        # Create depth interpolation based on body part
        if body_part in ["forearm", "calf"]:
            depth_map = self._interpolate_limb_depth(
                depth_map, landmarks_3d, body_part, width, height
            )
        elif body_part == "torso":
            depth_map = self._interpolate_torso_depth(
                depth_map, landmarks_3d, width, height
            )
        elif body_part == "hand":
            depth_map = self._interpolate_hand_depth(
                depth_map, cv_landmarks, width, height
            )
        
        # Normalize and convert to uint8
        depth_map = cv2.normalize(depth_map, None, 0, 255, cv2.NORM_MINMAX)
        return depth_map.astype(np.uint8)
    
    def _interpolate_limb_depth(
        self,
        depth_map: np.ndarray,
        landmarks_3d: np.ndarray,
        body_part: str,
        width: int,
        height: int
    ) -> np.ndarray:
        """
        Interpolate depth for arm or leg using cylindrical model
        """
        # Define relevant landmark indices for limbs
        if body_part == "forearm":
            # Shoulder to wrist landmarks
            key_indices = [11, 13, 15]  # Left arm: shoulder, elbow, wrist
        else:  # calf
            # Hip to ankle landmarks
            key_indices = [23, 25, 27]  # Left leg: hip, knee, ankle
        
        # Extract key points
        key_points = landmarks_3d[key_indices]
        
        # Create cylindrical depth model
        for i in range(len(key_points) - 1):
            start_point = key_points[i]
            end_point = key_points[i + 1]
            
            # Interpolate along the limb
            for t in np.linspace(0, 1, 50):
                interp_point = start_point + t * (end_point - start_point)
                x, y, z = int(interp_point[0]), int(interp_point[1]), interp_point[2]
                
                if 0 <= x < width and 0 <= y < height:
                    # Create cylindrical cross-section
                    radius = 30  # Approximate limb radius in pixels
                    for dx in range(-radius, radius):
                        for dy in range(-radius, radius):
                            if dx*dx + dy*dy <= radius*radius:
                                px, py = x + dx, y + dy
                                if 0 <= px < width and 0 <= py < height:
                                    # Calculate depth based on distance from center
                                    distance_from_center = np.sqrt(dx*dx + dy*dy)
                                    depth_value = 128 + z * 50 - distance_from_center * 2
                                    depth_map[py, px] = max(depth_map[py, px], depth_value)
        
        return depth_map
    
    def _interpolate_torso_depth(
        self,
        depth_map: np.ndarray,
        landmarks_3d: np.ndarray,
        width: int,
        height: int
    ) -> np.ndarray:
        """
        Interpolate depth for torso using anatomical model
        """
        # Use shoulder and hip landmarks for torso
        shoulder_indices = [11, 12]  # Left and right shoulders
        hip_indices = [23, 24]      # Left and right hips
        
        # Get torso bounds
        torso_landmarks = landmarks_3d[shoulder_indices + hip_indices]
        
        # Create torso depth model
        min_x = int(np.min(torso_landmarks[:, 0]))
        max_x = int(np.max(torso_landmarks[:, 0]))
        min_y = int(np.min(torso_landmarks[:, 1]))
        max_y = int(np.max(torso_landmarks[:, 1]))
        
        # Fill torso area with depth gradient
        for y in range(max(0, min_y), min(height, max_y)):
            for x in range(max(0, min_x), min(width, max_x)):
                # Calculate distance from torso center
                center_x = (min_x + max_x) / 2
                center_y = (min_y + max_y) / 2
                distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                
                # Create curved torso depth
                depth_value = 150 - distance * 0.5
                depth_map[y, x] = max(0, depth_value)
        
        return depth_map
    
    def _interpolate_hand_depth(
        self,
        depth_map: np.ndarray,
        cv_landmarks: List[Dict],
        width: int,
        height: int
    ) -> np.ndarray:
        """
        Interpolate depth for hand using finger structure
        """
        if not cv_landmarks:
            return depth_map
        
        # Convert normalized coordinates to pixel coordinates
        hand_points = []
        for landmark in cv_landmarks:
            x = int(landmark['x'] * width)
            y = int(landmark['y'] * height)
            z = landmark.get('z', 0)
            hand_points.append([x, y, z])
        
        hand_points = np.array(hand_points)
        
        # Create hand depth model
        for point in hand_points:
            x, y, z = int(point[0]), int(point[1]), point[2]
            
            if 0 <= x < width and 0 <= y < height:
                # Create small depth region around each finger joint
                radius = 8
                for dx in range(-radius, radius):
                    for dy in range(-radius, radius):
                        if dx*dx + dy*dy <= radius*radius:
                            px, py = x + dx, y + dy
                            if 0 <= px < width and 0 <= py < height:
                                depth_value = 120 + z * 30
                                depth_map[py, px] = max(depth_map[py, px], depth_value)
        
        return depth_map
    
    def _calculate_quality_score(
        self,
        depth_map: np.ndarray,
        binary_mask: np.ndarray,
        cv_landmarks: List[Dict]
    ) -> float:
        """
        Calculate quality score for generated depth map
        """
        quality_factors = []
        
        # Factor 1: Coverage - how much of the mask has depth data
        mask_area = np.sum(binary_mask > 0)
        depth_area = np.sum((depth_map > 0) & (binary_mask > 0))
        coverage = depth_area / max(mask_area, 1)
        quality_factors.append(coverage)
        
        # Factor 2: Landmark confidence
        if cv_landmarks:
            landmark_confidence = np.mean([
                lm.get('visibility', 1.0) for lm in cv_landmarks
            ])
            quality_factors.append(landmark_confidence)
        else:
            quality_factors.append(0.5)
        
        # Factor 3: Depth map smoothness
        depth_gradient = cv2.Laplacian(depth_map, cv2.CV_64F)
        smoothness = 1.0 / (1.0 + np.std(depth_gradient))
        quality_factors.append(smoothness)
        
        # Calculate weighted average
        weights = [0.4, 0.4, 0.2]
        quality_score = np.average(quality_factors, weights=weights)
        
        return float(quality_score)