import firebase_admin
from firebase_admin import credentials, auth, firestore
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import logging
from typing import Optional
import os
import json

class FirebaseAuthService:
    """
    Firebase Authentication service for user management and secure access
    """
    
    def __init__(self):
        self.security = HTTPBearer()
        self.db = None
        self._initialize_firebase()
        logging.info("Firebase Auth Service initialized")
    
    def _initialize_firebase(self):
        """Initialize Firebase Admin SDK"""
        try:
            # Check if Firebase is already initialized
            if not firebase_admin._apps:
                # Get Firebase credentials from environment
                firebase_config = os.getenv('FIREBASE_CONFIG')
                if firebase_config:
                    # Parse JSON config from environment variable
                    cred_dict = json.loads(firebase_config)
                    cred = credentials.Certificate(cred_dict)
                else:
                    # Use service account key file
                    key_path = os.getenv('FIREBASE_KEY_PATH', 'firebase-key.json')
                    if os.path.exists(key_path):
                        cred = credentials.Certificate(key_path)
                    else:
                        # Use default credentials (for GCP deployment)
                        cred = credentials.ApplicationDefault()
                
                firebase_admin.initialize_app(cred, {
                    'projectId': os.getenv('FIREBASE_PROJECT_ID'),
                })
            
            # Initialize Firestore
            self.db = firestore.client()
            
        except Exception as e:
            logging.error(f"Firebase initialization failed: {str(e)}")
            # Fall back to development mode without Firebase
            self.db = None
    
    async def health_check(self) -> bool:
        """Check if Firebase services are operational"""
        try:
            if self.db:
                # Test Firestore connection
                test_doc = self.db.collection('health_check').document('test')
                test_doc.set({'timestamp': firestore.SERVER_TIMESTAMP})
                return True
            return False
        except Exception:
            return False
    
    async def verify_token(self, token: str) -> dict:
        """Verify Firebase ID token and return user claims"""
        try:
            if not token:
                raise HTTPException(status_code=401, detail="No token provided")
            
            # Verify the ID token
            decoded_token = auth.verify_id_token(token)
            
            # Get additional user data from Firestore
            user_doc = self.db.collection('users').document(decoded_token['uid']).get()
            
            user_data = {
                'uid': decoded_token['uid'],
                'email': decoded_token.get('email'),
                'email_verified': decoded_token.get('email_verified', False),
                'name': decoded_token.get('name'),
                'picture': decoded_token.get('picture'),
                'role': 'user'  # Default role
            }
            
            # Merge with Firestore user data if available
            if user_doc.exists:
                firestore_data = user_doc.to_dict()
                user_data.update(firestore_data)
            
            return user_data
            
        except auth.InvalidIdTokenError:
            raise HTTPException(status_code=401, detail="Invalid token")
        except auth.ExpiredIdTokenError:
            raise HTTPException(status_code=401, detail="Token expired")
        except Exception as e:
            logging.error(f"Token verification failed: {str(e)}")
            raise HTTPException(status_code=401, detail="Authentication failed")
    
    async def get_current_user(
        self,
        credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())
    ) -> dict:
        """FastAPI dependency to get current authenticated user"""
        if not credentials or not credentials.credentials:
            raise HTTPException(status_code=401, detail="Authentication required")
        
        return await self.verify_token(credentials.credentials)
    
    async def create_user(
        self,
        email: str,
        password: str,
        display_name: str,
        role: str = "client"
    ) -> dict:
        """Create a new user account"""
        try:
            # Create user in Firebase Auth
            user_record = auth.create_user(
                email=email,
                password=password,
                display_name=display_name,
                email_verified=False
            )
            
            # Store additional user data in Firestore
            user_data = {
                'uid': user_record.uid,
                'email': email,
                'display_name': display_name,
                'role': role,
                'created_at': firestore.SERVER_TIMESTAMP,
                'scan_history': [],
                'preferences': {
                    'privacy_mode': True,
                    'quality_level': 'high',
                    'export_format': ['stencil', 'svg']
                }
            }
            
            if self.db:
                self.db.collection('users').document(user_record.uid).set(user_data)
            
            return {
                'uid': user_record.uid,
                'email': email,
                'display_name': display_name,
                'role': role
            }
            
        except auth.EmailAlreadyExistsError:
            raise HTTPException(status_code=400, detail="Email already exists")
        except Exception as e:
            logging.error(f"User creation failed: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to create user")
    
    async def update_user_role(self, uid: str, role: str) -> bool:
        """Update user role (admin only)"""
        try:
            if self.db:
                self.db.collection('users').document(uid).update({
                    'role': role,
                    'updated_at': firestore.SERVER_TIMESTAMP
                })
            
            # Set custom claims for role-based access
            auth.set_custom_user_claims(uid, {'role': role})
            
            return True
            
        except Exception as e:
            logging.error(f"Role update failed: {str(e)}")
            return False
    
    async def get_user_profile(self, uid: str) -> Optional[dict]:
        """Get user profile from Firestore"""
        try:
            if not self.db:
                return None
            
            user_doc = self.db.collection('users').document(uid).get()
            
            if user_doc.exists:
                return user_doc.to_dict()
            
            return None
            
        except Exception as e:
            logging.error(f"Failed to get user profile: {str(e)}")
            return None
    
    async def update_user_profile(self, uid: str, profile_data: dict) -> bool:
        """Update user profile in Firestore"""
        try:
            if not self.db:
                return False
            
            # Add timestamp
            profile_data['updated_at'] = firestore.SERVER_TIMESTAMP
            
            self.db.collection('users').document(uid).update(profile_data)
            
            return True
            
        except Exception as e:
            logging.error(f"Profile update failed: {str(e)}")
            return False
    
    async def log_scan_session(
        self,
        uid: str,
        scan_data: dict
    ) -> str:
        """Log scan session to user's history"""
        try:
            if not self.db:
                return ""
            
            # Add scan session to user's history
            scan_doc = {
                'user_id': uid,
                'body_part': scan_data.get('body_part'),
                'quality_score': scan_data.get('quality_score'),
                'processing_time': scan_data.get('processing_time'),
                'privacy_mode': scan_data.get('privacy_mode', True),
                'created_at': firestore.SERVER_TIMESTAMP,
                'metadata': scan_data.get('metadata', {})
            }
            
            # Add to scans collection
            scan_ref = self.db.collection('scans').add(scan_doc)[1]
            
            # Update user's scan history
            user_ref = self.db.collection('users').document(uid)
            user_ref.update({
                'scan_history': firestore.ArrayUnion([scan_ref.id])
            })
            
            return scan_ref.id
            
        except Exception as e:
            logging.error(f"Scan logging failed: {str(e)}")
            return ""
    
    async def get_user_scans(
        self,
        uid: str,
        limit: int = 20
    ) -> list:
        """Get user's scan history"""
        try:
            if not self.db:
                return []
            
            # Query user's scans
            scans_ref = self.db.collection('scans').where('user_id', '==', uid)
            scans_ref = scans_ref.order_by('created_at', direction=firestore.Query.DESCENDING)
            scans_ref = scans_ref.limit(limit)
            
            scans = []
            for doc in scans_ref.stream():
                scan_data = doc.to_dict()
                scan_data['id'] = doc.id
                scans.append(scan_data)
            
            return scans
            
        except Exception as e:
            logging.error(f"Failed to get user scans: {str(e)}")
            return []
    
    async def delete_scan(self, uid: str, scan_id: str) -> bool:
        """Delete a scan from user's history"""
        try:
            if not self.db:
                return False
            
            # Verify scan belongs to user
            scan_doc = self.db.collection('scans').document(scan_id).get()
            if not scan_doc.exists or scan_doc.to_dict().get('user_id') != uid:
                return False
            
            # Delete scan document
            self.db.collection('scans').document(scan_id).delete()
            
            # Remove from user's scan history
            user_ref = self.db.collection('users').document(uid)
            user_ref.update({
                'scan_history': firestore.ArrayRemove([scan_id])
            })
            
            return True
            
        except Exception as e:
            logging.error(f"Scan deletion failed: {str(e)}")
            return False
    
    async def get_user_analytics(self, uid: str) -> dict:
        """Get analytics for user's scanning activity"""
        try:
            if not self.db:
                return {}
            
            # Get user's scans
            scans = await self.get_user_scans(uid, limit=100)
            
            if not scans:
                return {
                    'total_scans': 0,
                    'average_quality': 0,
                    'favorite_body_part': None,
                    'total_processing_time': 0
                }
            
            # Calculate analytics
            total_scans = len(scans)
            total_quality = sum(scan.get('quality_score', 0) for scan in scans)
            average_quality = total_quality / total_scans if total_scans > 0 else 0
            
            # Find most scanned body part
            body_parts = [scan.get('body_part') for scan in scans if scan.get('body_part')]
            favorite_body_part = max(set(body_parts), key=body_parts.count) if body_parts else None
            
            # Total processing time
            total_processing_time = sum(scan.get('processing_time', 0) for scan in scans)
            
            return {
                'total_scans': total_scans,
                'average_quality': round(average_quality, 2),
                'favorite_body_part': favorite_body_part,
                'total_processing_time': round(total_processing_time, 2)
            }
            
        except Exception as e:
            logging.error(f"Analytics calculation failed: {str(e)}")
            return {}