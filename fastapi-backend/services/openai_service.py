import openai
import aiohttp
import asyncio
import cv2
import numpy as np
from PIL import Image, ImageFilter, ImageOps
import io
import base64
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import os
import tempfile
from sklearn.cluster import KMeans

@dataclass
class StencilResult:
    stencil_url: str
    svg_url: Optional[str]
    palette: List[str]
    processing_time: float

@dataclass
class ColorPalette:
    colors: List[str]
    ink_recommendations: List[Dict]

class OpenAIService:
    """
    OpenAI service for tattoo design enhancement and stencil generation
    """
    
    def __init__(self):
        self.api_key = os.getenv('OPENAI_API_KEY')
        self.client = openai.OpenAI(api_key=self.api_key) if self.api_key else None
        logging.info("OpenAI service initialized")
    
    async def health_check(self) -> bool:
        """Check if OpenAI API is accessible"""
        try:
            if not self.client:
                return False
            
            # Test with a simple completion
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": "test"}],
                max_tokens=1
            )
            return True
            
        except Exception:
            return False
    
    async def enhance_design(
        self,
        prompt: str,
        style: Dict
    ) -> Dict:
        """
        Enhance tattoo design description using GPT
        """
        try:
            system_prompt = """You are a professional tattoo artist and designer. 
            Enhance tattoo design descriptions to be more detailed, artistic, and suitable for AI image generation.
            Focus on visual elements, composition, style details, and technical aspects that will help create better tattoo art.
            Always include specifications for transparent backgrounds and clean edges suitable for stencil conversion."""
            
            user_prompt = f"""
            Original design request: {prompt}
            Style preferences: {style}
            
            Please enhance this into a detailed, professional tattoo design description that includes:
            1. Specific visual details and composition
            2. Artistic style and technique specifications  
            3. Placement and sizing considerations
            4. Technical requirements for stencil conversion
            """
            
            response = self.client.chat.completions.create(
                model="gpt-4", # the newest OpenAI model is "gpt-5" which was released August 7, 2025. do not change this unless explicitly requested by the user
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=500,
                temperature=0.7
            )
            
            enhanced_description = response.choices[0].message.content
            
            return {
                'enhanced_prompt': enhanced_description,
                'recommendations': [
                    'Consider transparent background for 3D overlay compatibility',
                    'Ensure high contrast for clean stencil conversion',
                    'Include margin space for proper placement'
                ],
                'image_url': None  # Will be generated separately
            }
            
        except Exception as e:
            logging.error(f"Design enhancement failed: {str(e)}")
            raise
    
    async def create_stencil_from_image(
        self,
        image_url: str,
        mode: str = 'stencil',
        mask_url: Optional[str] = None
    ) -> StencilResult:
        """
        Convert artwork to stencil using OpenAI image editing
        """
        start_time = datetime.utcnow()
        
        try:
            # Download source image
            source_image = await self._download_image(image_url)
            if not source_image:
                raise ValueError("Could not download source image")
            
            # Create or download mask
            if mask_url:
                mask_image = await self._download_image(mask_url)
            else:
                # Create full-frame mask (edit entire image)
                mask_image = Image.new('L', source_image.size, 255)
            
            # Prepare images for OpenAI
            source_bytes = self._image_to_bytes(source_image)
            mask_bytes = self._image_to_bytes(mask_image)
            
            # Build prompt based on mode
            if mode == 'stencil':
                edit_prompt = (
                    "Convert to tattoo stencil: keep essential outlines and shading as "
                    "high-contrast black on transparent background; remove all backgrounds; "
                    "smooth gradations into solid fill with crosshatch where necessary; "
                    "preserve edge fidelity; no text or watermark."
                )
            else:  # clean-bg mode
                edit_prompt = (
                    "Remove background completely, make transparent; keep tattoo design "
                    "crisp and clean; enhance contrast and edge definition; maintain "
                    "all design details on transparent background."
                )
            
            # Call OpenAI image edit API
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as source_file:
                source_file.write(source_bytes)
                source_file.flush()
                
                with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as mask_file:
                    mask_file.write(mask_bytes)
                    mask_file.flush()
                    
                    response = self.client.images.edit(
                        model="dall-e-2",  # Use dall-e-2 for edits
                        image=open(source_file.name, "rb"),
                        mask=open(mask_file.name, "rb"),
                        prompt=edit_prompt,
                        n=1,
                        size="1024x1024"
                    )
            
            # Get result URL
            stencil_url = response.data[0].url
            
            # Extract color palette from original image
            palette = await self._extract_color_palette(source_image)
            
            # Generate SVG (simplified for now)
            svg_url = None  # TODO: Implement SVG vectorization
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            return StencilResult(
                stencil_url=stencil_url,
                svg_url=svg_url,
                palette=palette.colors,
                processing_time=processing_time
            )
            
        except Exception as e:
            logging.error(f"Stencil creation failed: {str(e)}")
            raise
    
    async def create_stencil_local(
        self,
        image_path: str,
        output_path: str,
        threshold: int = 180
    ) -> str:
        """
        Create stencil using local image processing (fallback method)
        """
        try:
            # Load image
            image = Image.open(image_path).convert("L")
            
            # Apply median filter for noise reduction
            image = image.filter(ImageFilter.MedianFilter(3))
            
            # Apply threshold to create high contrast
            image = image.point(lambda p: 255 if p > threshold else 0, mode='1')
            
            # Convert back to RGBA for transparency
            image = image.convert('RGBA')
            
            # Make white pixels transparent
            data = image.getdata()
            new_data = []
            for item in data:
                if item[0] > 200 and item[1] > 200 and item[2] > 200:  # White pixels
                    new_data.append((255, 255, 255, 0))  # Transparent
                else:
                    new_data.append((0, 0, 0, 255))  # Black
            
            image.putdata(new_data)
            
            # Save result
            image.save(output_path)
            
            return output_path
            
        except Exception as e:
            logging.error(f"Local stencil creation failed: {str(e)}")
            raise
    
    async def _download_image(self, url: str) -> Optional[Image.Image]:
        """Download image from URL"""
        try:
            if url.startswith('http'):
                async with aiohttp.ClientSession() as session:
                    async with session.get(url) as response:
                        if response.status == 200:
                            image_data = await response.read()
                            return Image.open(io.BytesIO(image_data))
            else:
                # Local file path
                return Image.open(url.lstrip('/'))
                
        except Exception as e:
            logging.error(f"Image download failed: {str(e)}")
            return None
    
    def _image_to_bytes(self, image: Image.Image) -> bytes:
        """Convert PIL Image to bytes"""
        byte_arr = io.BytesIO()
        image.save(byte_arr, format='PNG')
        return byte_arr.getvalue()
    
    async def _extract_color_palette(
        self,
        image: Image.Image,
        n_colors: int = 8
    ) -> ColorPalette:
        """Extract dominant colors from image using K-means clustering"""
        try:
            # Convert to RGB and resize for faster processing
            image_rgb = image.convert('RGB')
            image_rgb = image_rgb.resize((150, 150))
            
            # Convert to numpy array
            image_array = np.array(image_rgb)
            image_array = image_array.reshape(-1, 3)
            
            # Remove white/transparent pixels
            mask = np.sum(image_array, axis=1) < 700  # Exclude very light pixels
            image_array = image_array[mask]
            
            if len(image_array) == 0:
                return ColorPalette(colors=['#000000'], ink_recommendations=[])
            
            # Perform K-means clustering
            kmeans = KMeans(n_clusters=min(n_colors, len(image_array)), random_state=42)
            kmeans.fit(image_array)
            
            # Get cluster centers (dominant colors)
            colors = kmeans.cluster_centers_.astype(int)
            
            # Convert to hex
            hex_colors = [f"#{r:02x}{g:02x}{b:02x}" for r, g, b in colors]
            
            # Generate ink recommendations
            ink_recommendations = []
            for i, (r, g, b) in enumerate(colors):
                ink_recommendations.append({
                    'color': hex_colors[i],
                    'brand': 'Eternal Ink',  # Example brand
                    'code': f'EI-{i+1:03d}',
                    'description': self._describe_color(r, g, b)
                })
            
            return ColorPalette(
                colors=hex_colors,
                ink_recommendations=ink_recommendations
            )
            
        except Exception as e:
            logging.error(f"Color extraction failed: {str(e)}")
            return ColorPalette(
                colors=['#000000'],
                ink_recommendations=[{
                    'color': '#000000',
                    'brand': 'Universal',
                    'code': 'BLACK-001',
                    'description': 'Standard black ink'
                }]
            )
    
    def _describe_color(self, r: int, g: int, b: int) -> str:
        """Generate color description for ink recommendations"""
        if r < 50 and g < 50 and b < 50:
            return "Deep black"
        elif r > 200 and g > 200 and b > 200:
            return "Light grey"
        elif r > g and r > b:
            return "Red tone"
        elif g > r and g > b:
            return "Green tone"
        elif b > r and b > g:
            return "Blue tone"
        else:
            return "Mixed tone"
    
    async def generate_tattoo_image(
        self,
        prompt: str,
        style: str = "realistic",
        size: str = "1024x1024"
    ) -> Dict:
        """
        Generate tattoo image using DALL-E
        """
        try:
            # Enhance prompt for tattoo generation
            tattoo_prompt = f"""
            {prompt}, professional tattoo design, clean linework, 
            high contrast, transparent background, no borders or frames,
            suitable for stencil conversion, {style} style
            """
            
            response = self.client.images.generate(
                model="dall-e-3",
                prompt=tattoo_prompt,
                size=size,
                quality="hd",
                n=1
            )
            
            return {
                'image_url': response.data[0].url,
                'revised_prompt': response.data[0].revised_prompt if hasattr(response.data[0], 'revised_prompt') else None
            }
            
        except Exception as e:
            logging.error(f"DALL-E generation failed: {str(e)}")
            raise