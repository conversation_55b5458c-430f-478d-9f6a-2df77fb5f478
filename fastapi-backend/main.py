from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
from typing import List, Optional
import asyncio
import logging
from datetime import datetime

# Computer Vision Services
from services.cv_scanner import CVScannerService
from services.depth_mapper import DepthMapperService
from services.uv_unwrapper import UVUnwrapperService
from services.ray_tracer import RayTracerService

# AI Services
from services.openai_service import OpenAIService
from services.leonardo_service import LeonardoService

# Authentication & Storage
from services.firebase_auth import FirebaseAuthService
from services.gcp_storage import GCPStorageService
from services.google_oauth import GoogleOAuthService, get_current_user_dependency

# Database
from database.models import User, ScanSession, ProcessingJob
from database.connection import get_db

# Initialize FastAPI
app = FastAPI(
    title="Tattoo AI Buddy - 3D Try-On API",
    description="Revolutionary 3D tattoo scanning with computer vision and AI",
    version="2.0.0"
)

# CORS middleware - Secure configuration
import os

# Get allowed origins from environment
allowed_origins = []
if os.getenv('NODE_ENV') == 'development':
    allowed_origins = [
        "http://localhost:3000",
        "http://localhost:5000",
        "http://localhost:5173",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5000",
        "http://127.0.0.1:5173"
    ]
elif os.getenv('ALLOWED_ORIGINS'):
    allowed_origins = [origin.strip() for origin in os.getenv('ALLOWED_ORIGINS').split(',')]

if os.getenv('HOST_URL'):
    allowed_origins.append(os.getenv('HOST_URL'))

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allow_headers=[
        "Origin",
        "X-Requested-With",
        "Content-Type",
        "Accept",
        "Authorization",
        "Cache-Control",
        "X-API-Key"
    ],
    expose_headers=[
        "X-Total-Count",
        "X-Page-Count",
        "X-Rate-Limit-Remaining",
        "X-Rate-Limit-Reset"
    ],
    max_age=86400
)

# Static files for generated assets
app.mount("/generated", StaticFiles(directory="generated"), name="generated")

# Initialize services
cv_scanner = CVScannerService()
depth_mapper = DepthMapperService()
uv_unwrapper = UVUnwrapperService()
ray_tracer = RayTracerService()
openai_service = OpenAIService()
leonardo_service = LeonardoService()
firebase_auth = FirebaseAuthService()
gcp_storage = GCPStorageService()
google_oauth = GoogleOAuthService()

# Pydantic models
class ScanRequest(BaseModel):
    body_part: str
    privacy_mode: bool = True
    quality_level: str = "high"

class UVUnwrapRequest(BaseModel):
    depth_map_url: str
    mask_url: str
    unwrap_method: str = "cylindrical"

class RayTraceRequest(BaseModel):
    tattoo_image_url: str
    uv_map_url: str
    normal_map_url: str
    lighting_config: dict
    path_trace: bool = True

class ExportRequest(BaseModel):
    baked_texture_url: str
    export_format: List[str] = ["stencil", "svg", "palette"]
    artist_specs: dict = {}

# Health check
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "cv_scanner": await cv_scanner.health_check(),
            "depth_mapper": await depth_mapper.health_check(),
            "openai": await openai_service.health_check(),
            "firebase": await firebase_auth.health_check(),
            "gcp_storage": await gcp_storage.health_check(),
            "google_oauth": await google_oauth.health_check()
        }
    }

# Google OAuth Authentication Endpoints
@app.get("/auth/google")
async def google_login():
    """
    Initiate Google OAuth login
    """
    auth_url = google_oauth.get_authorization_url()
    return {"auth_url": auth_url}

@app.get("/auth/google/callback")
async def google_callback(code: str, state: str = None):
    """
    Handle Google OAuth callback
    """
    try:
        user_data = await google_oauth.authenticate_user(code)
        return user_data
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Authentication failed: {str(e)}")

@app.get("/auth/me")
async def get_current_user(current_user: dict = Depends(get_current_user_dependency)):
    """
    Get current authenticated user information
    """
    return {"user": current_user}

@app.post("/auth/logout")
async def logout():
    """
    Logout endpoint (client should remove token)
    """
    return {"message": "Logged out successfully"}

# 3D Scanning Endpoints
@app.post("/api/v2/scan/upload")
async def upload_scan(
    file: UploadFile = File(...),
    scan_request: ScanRequest = Depends(),
    current_user: User = Depends(firebase_auth.get_current_user),
    db = Depends(get_db)
):
    """
    Upload photo and generate 3D depth maps with computer vision
    """
    try:
        # Validate file type and size
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Create scan session
        scan_session = ScanSession(
            user_id=current_user.id,
            body_part=scan_request.body_part,
            privacy_mode=scan_request.privacy_mode,
            status="processing"
        )
        db.add(scan_session)
        db.commit()
        
        # Read and process image
        image_data = await file.read()
        
        # Computer vision processing
        cv_result = await cv_scanner.process_image(
            image_data=image_data,
            body_part=scan_request.body_part,
            quality_level=scan_request.quality_level
        )
        
        # Generate depth map using MediaPipe and OpenCV
        depth_result = await depth_mapper.generate_depth_map(
            image_data=image_data,
            cv_landmarks=cv_result.landmarks,
            body_part=scan_request.body_part
        )
        
        # Store results in GCP if not in privacy mode
        if not scan_request.privacy_mode:
            depth_url = await gcp_storage.upload_file(
                data=depth_result.depth_map,
                filename=f"depth/{scan_session.id}.png"
            )
            mask_url = await gcp_storage.upload_file(
                data=depth_result.binary_mask,
                filename=f"mask/{scan_session.id}.png"
            )
        else:
            # Store locally for privacy
            depth_url = f"/generated/depth/{scan_session.id}.png"
            mask_url = f"/generated/mask/{scan_session.id}.png"
            
            # Save files locally
            await cv_scanner.save_local_file(depth_result.depth_map, depth_url)
            await cv_scanner.save_local_file(depth_result.binary_mask, mask_url)
        
        # Update scan session
        scan_session.depth_map_url = depth_url
        scan_session.mask_url = mask_url
        scan_session.status = "completed"
        scan_session.metadata = {
            "cv_confidence": cv_result.confidence,
            "landmarks_count": len(cv_result.landmarks),
            "processing_time": depth_result.processing_time
        }
        db.commit()
        
        return {
            "success": True,
            "scan_id": scan_session.id,
            "depth_map_url": depth_url,
            "mask_url": mask_url,
            "confidence": cv_result.confidence,
            "recommendations": cv_result.recommendations
        }
        
    except Exception as e:
        logging.error(f"Scan upload failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Scan processing failed: {str(e)}")

@app.post("/api/v2/scan/unwrap")
async def generate_uv_unwrap(
    request: UVUnwrapRequest,
    current_user: User = Depends(firebase_auth.get_current_user),
    db = Depends(get_db)
):
    """
    Generate UV unwrapping from depth map and mask
    """
    try:
        # Process UV unwrapping
        unwrap_result = await uv_unwrapper.generate_uv_mapping(
            depth_map_url=request.depth_map_url,
            mask_url=request.mask_url,
            method=request.unwrap_method
        )
        
        # Store UV maps
        uv_url = f"/generated/uv/{unwrap_result.session_id}.png"
        normal_url = f"/generated/normal/{unwrap_result.session_id}.png"
        
        await uv_unwrapper.save_uv_maps(
            uv_map=unwrap_result.uv_texture,
            normal_map=unwrap_result.normal_map,
            uv_path=uv_url,
            normal_path=normal_url
        )
        
        return {
            "success": True,
            "uv_map_url": uv_url,
            "normal_map_url": normal_url,
            "unwrap_quality": unwrap_result.quality_score,
            "processing_time": unwrap_result.processing_time
        }
        
    except Exception as e:
        logging.error(f"UV unwrap failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"UV unwrapping failed: {str(e)}")

@app.post("/api/v2/preview/raytrace")
async def raytrace_tattoo(
    request: RayTraceRequest,
    current_user: User = Depends(firebase_auth.get_current_user)
):
    """
    Apply tattoo design with ray-traced lighting and skin curvature
    """
    try:
        # Ray-traced baking
        raytrace_result = await ray_tracer.bake_tattoo_to_mesh(
            tattoo_image_url=request.tattoo_image_url,
            uv_map_url=request.uv_map_url,
            normal_map_url=request.normal_map_url,
            lighting_config=request.lighting_config,
            path_trace=request.path_trace
        )
        
        # Store baked result
        baked_url = f"/generated/baked/{raytrace_result.session_id}.png"
        await ray_tracer.save_baked_texture(
            texture=raytrace_result.baked_texture,
            path=baked_url
        )
        
        return {
            "success": True,
            "baked_texture_url": baked_url,
            "render_quality": raytrace_result.quality_score,
            "lighting_realism": raytrace_result.lighting_accuracy,
            "processing_time": raytrace_result.processing_time
        }
        
    except Exception as e:
        logging.error(f"Ray tracing failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Ray tracing failed: {str(e)}")

@app.post("/api/v2/export/professional")
async def export_professional_package(
    request: ExportRequest,
    current_user: User = Depends(firebase_auth.get_current_user)
):
    """
    Generate professional artist package with stencils, vectors, and color specs
    """
    try:
        export_results = {}
        
        # Generate stencil
        if "stencil" in request.export_format:
            stencil_result = await cv_scanner.generate_stencil(
                baked_texture_url=request.baked_texture_url,
                artist_specs=request.artist_specs
            )
            stencil_url = f"/generated/stencil/{stencil_result.session_id}.png"
            await cv_scanner.save_local_file(stencil_result.stencil_image, stencil_url)
            export_results["stencil_url"] = stencil_url
        
        # Generate SVG vector
        if "svg" in request.export_format:
            svg_result = await cv_scanner.generate_svg_vector(
                baked_texture_url=request.baked_texture_url
            )
            svg_url = f"/generated/svg/{svg_result.session_id}.svg"
            await cv_scanner.save_local_file(svg_result.svg_data, svg_url)
            export_results["svg_url"] = svg_url
        
        # Extract color palette
        if "palette" in request.export_format:
            palette_result = await cv_scanner.extract_color_palette(
                baked_texture_url=request.baked_texture_url
            )
            export_results["color_palette"] = palette_result.colors
            export_results["ink_recommendations"] = palette_result.ink_brands
        
        # Create complete package
        package_url = f"/generated/packages/{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.zip"
        await cv_scanner.create_artist_package(
            export_results=export_results,
            package_path=package_url
        )
        
        return {
            "success": True,
            "package_url": package_url,
            **export_results
        }
        
    except Exception as e:
        logging.error(f"Export failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Export failed: {str(e)}")

# FLUX.1 Kontext API Endpoints
@app.post("/api/leonardo/flux/generate")
async def flux_generate_tattoo(
    subject: str,
    body_part: str = "forearm",
    style: str = "realistic_bw",
    width: Optional[int] = None,
    height: Optional[int] = None,
    context_images: Optional[List[Dict]] = None,
    seed: Optional[int] = None,
    additional_instructions: str = "",
    enhance_prompt: bool = False,
    current_user: User = Depends(firebase_auth.get_current_user)
):
    """
    Generate tattoo design using FLUX.1 Kontext
    """
    try:
        result = await leonardo_service.generate_tattoo(
            subject=subject,
            body_part=body_part,
            style=style,
            width=width,
            height=height,
            context_images=context_images,
            seed=seed,
            additional_instructions=additional_instructions,
            enhance_prompt=enhance_prompt
        )
        
        return {
            "success": True,
            "generation_id": result.generation_id,
            "trace_id": result.trace_id,
            "processing_time": result.processing_time,
            "status": "processing"
        }
        
    except Exception as e:
        logging.error(f"FLUX generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"FLUX generation failed: {str(e)}")

@app.post("/api/leonardo/flux/variation")
async def flux_create_variation(
    base_prompt: str,
    base_image_id: str,
    image_type: str = "GENERATED",
    body_part: str = "forearm", 
    style: str = "realistic_bw",
    seed: Optional[int] = None,
    variation_instructions: str = "",
    current_user: User = Depends(firebase_auth.get_current_user)
):
    """
    Create variation from existing FLUX generation
    """
    try:
        result = await leonardo_service.create_variation(
            base_prompt=base_prompt,
            base_image_id=base_image_id,
            image_type=image_type,
            body_part=body_part,
            style=style,
            seed=seed,
            variation_instructions=variation_instructions
        )
        
        return {
            "success": True,
            "generation_id": result.generation_id,
            "trace_id": result.trace_id,
            "processing_time": result.processing_time,
            "status": "processing"
        }
        
    except Exception as e:
        logging.error(f"Variation creation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Variation creation failed: {str(e)}")

@app.get("/api/leonardo/generation/{generation_id}")
async def get_generation_status(
    generation_id: str,
    current_user: User = Depends(firebase_auth.get_current_user)
):
    """
    Check FLUX generation status and get results
    """
    try:
        status = await leonardo_service.get_generation_status(generation_id)
        return {
            "success": True,
            "status": status
        }
        
    except Exception as e:
        logging.error(f"Status check failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Status check failed: {str(e)}")

@app.get("/api/leonardo/styles")
async def get_style_presets():
    """
    Get available FLUX style presets
    """
    try:
        presets = leonardo_service.get_style_presets()
        return {
            "success": True,
            "styles": {
                key: {
                    "id": preset.id,
                    "name": preset.name,
                    "description": preset.description,
                    "category": preset.category
                }
                for key, preset in presets.items()
            }
        }
        
    except Exception as e:
        logging.error(f"Style preset retrieval failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Style preset retrieval failed: {str(e)}")

@app.get("/api/leonardo/body-parts")
async def get_body_part_sizes():
    """
    Get optimal sizes for each body part
    """
    try:
        sizes = leonardo_service.get_body_part_sizes()
        return {
            "success": True,
            "body_parts": {
                part: {"width": size[0], "height": size[1]}
                for part, size in sizes.items()
            }
        }
        
    except Exception as e:
        logging.error(f"Body part sizes retrieval failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Body part sizes retrieval failed: {str(e)}")

@app.post("/api/openai/stencil")
async def create_stencil(
    image_url: str,
    mode: str = "stencil",
    mask_url: Optional[str] = None,
    current_user: User = Depends(firebase_auth.get_current_user)
):
    """
    Convert artwork to stencil using OpenAI image editing
    """
    try:
        result = await openai_service.create_stencil_from_image(
            image_url=image_url,
            mode=mode,
            mask_url=mask_url
        )
        
        return {
            "success": True,
            "stencil_url": result.stencil_url,
            "svg_url": result.svg_url,
            "palette": result.palette,
            "processing_time": result.processing_time
        }
        
    except Exception as e:
        logging.error(f"Stencil creation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Stencil creation failed: {str(e)}")

# AI Integration Endpoints
@app.post("/api/v2/ai/enhance")
async def ai_enhance_design(
    tattoo_prompt: str,
    style_preferences: dict,
    current_user: User = Depends(firebase_auth.get_current_user)
):
    """
    Use AI to enhance tattoo design with multiple providers
    """
    try:
        # OpenAI enhancement
        openai_result = await openai_service.enhance_design(
            prompt=tattoo_prompt,
            style=style_preferences
        )
        
        # Leonardo AI generation using FLUX
        leonardo_result = await leonardo_service.generate_tattoo(
            subject=tattoo_prompt,
            style=style_preferences.get("leonardo_style", "realistic_bw"),
            body_part=style_preferences.get("body_part", "forearm")
        )
        
        return {
            "success": True,
            "openai_enhanced": openai_result.get("enhanced_prompt"),
            "leonardo_generation_id": leonardo_result.generation_id,
            "recommendations": openai_result.get("recommendations", [])
        }
        
    except Exception as e:
        logging.error(f"AI enhancement failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"AI enhancement failed: {str(e)}")

# WebSocket for real-time processing updates
@app.websocket("/ws/scan-progress")
async def websocket_scan_progress(websocket):
    """
    WebSocket endpoint for real-time scan processing updates
    """
    await websocket.accept()
    try:
        while True:
            # Send processing updates
            data = await websocket.receive_text()
            # Process and send back updates
            await websocket.send_json({
                "type": "progress",
                "stage": "depth_mapping",
                "progress": 75,
                "message": "Generating 3D depth map..."
            })
    except Exception as e:
        logging.error(f"WebSocket error: {str(e)}")
    finally:
        await websocket.close()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )