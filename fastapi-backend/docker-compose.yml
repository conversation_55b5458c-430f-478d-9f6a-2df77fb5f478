version: '3.8'

services:
  tattoo-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LEONARDO_API_KEY=${LEONARDO_API_KEY}
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - GCP_STORAGE_BUCKET=${GCP_STORAGE_BUCKET}
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    volumes:
      - ./generated:/app/generated
      - ./logs:/app/logs
    depends_on:
      - redis
      - postgres
    networks:
      - tattoo-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - tattoo-network

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=tattoo_db
      - POSTGRES_USER=tattoo_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - tattoo-network

  celery-worker:
    build: .
    command: celery -A services.celery_app worker --loglevel=info
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LEONARDO_API_KEY=${LEONARDO_API_KEY}
      - REDIS_URL=${REDIS_URL}
      - DATABASE_URL=${DATABASE_URL}
    volumes:
      - ./generated:/app/generated
    depends_on:
      - redis
      - postgres
    networks:
      - tattoo-network

  celery-beat:
    build: .
    command: celery -A services.celery_app beat --loglevel=info
    environment:
      - REDIS_URL=${REDIS_URL}
      - DATABASE_URL=${DATABASE_URL}
    depends_on:
      - redis
      - postgres
    networks:
      - tattoo-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - tattoo-api
    networks:
      - tattoo-network

volumes:
  redis_data:
  postgres_data:

networks:
  tattoo-network:
    driver: bridge