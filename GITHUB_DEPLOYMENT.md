# GitHub Deployment Guide

## 🚀 Push to GitHub

Your TattooAdmin project is ready to be pushed to GitHub! Follow these steps:

### 1. Create GitHub Repository

1. **Go to GitHub**: https://github.com/new
2. **Repository name**: `TattooAdmin` (or your preferred name)
3. **Description**: `AI-Powered Tattoo Management System with Firebase & FastAPI`
4. **Visibility**: Choose Public or Private
5. **DO NOT** initialize with README, .gitignore, or license (we already have these)
6. **Click "Create repository"**

### 2. Add Remote and Push

After creating the repository, run these commands in your terminal:

```bash
# Add the remote repository (replace YOUR_USERNAME with your GitHub username)
git remote add origin https://github.com/YOUR_USERNAME/TattooAdmin.git

# Push to GitHub
git branch -M main
git push -u origin main
```

### 3. Verify Upload

Your repository should now contain:
- ✅ Complete source code
- ✅ Documentation (README.md, setup guides)
- ✅ Security implementations
- ✅ Firebase integration
- ✅ FastAPI backend
- ✅ Environment templates

## 🔧 Repository Setup

### Branch Protection (Recommended)

1. Go to your repository on GitHub
2. Click **Settings** → **Branches**
3. Add rule for `main` branch:
   - ✅ Require pull request reviews
   - ✅ Require status checks to pass
   - ✅ Restrict pushes to matching branches

### Secrets Configuration

For GitHub Actions or deployment, add these secrets:

1. Go to **Settings** → **Secrets and variables** → **Actions**
2. Add repository secrets:

```bash
# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Firebase
FIREBASE_PROJECT_ID=hardcore-tattoo-admin
FIREBASE_API_KEY=your-firebase-api-key
FIREBASE_AUTH_DOMAIN=hardcore-tattoo-admin.firebaseapp.com

# API Keys
OPENAI_API_KEY=sk-your-openai-key
LEONARDO_API_KEY=your-leonardo-key

# Security
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters
```

## 🌐 Deployment Options

### 1. Vercel (Recommended for Frontend)

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# Set environment variables in Vercel dashboard
```

### 2. Railway (Full-Stack)

```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway init
railway up
```

### 3. Heroku (Traditional)

```bash
# Install Heroku CLI
# Create Heroku app
heroku create your-app-name

# Set environment variables
heroku config:set GOOGLE_CLIENT_ID=your-client-id
heroku config:set FIREBASE_PROJECT_ID=your-project-id

# Deploy
git push heroku main
```

### 4. Docker Deployment

```bash
# Build Docker image
docker build -t tattoo-admin .

# Run container
docker run -p 8000:8000 --env-file .env tattoo-admin
```

## 📋 Pre-Deployment Checklist

### Security
- [ ] All secrets moved to environment variables
- [ ] No hardcoded credentials in code
- [ ] CORS origins updated for production domain
- [ ] Rate limits configured appropriately
- [ ] Security headers enabled

### Configuration
- [ ] Environment variables set in deployment platform
- [ ] Database connection configured (if using PostgreSQL)
- [ ] Firebase project configured for production
- [ ] Google OAuth redirect URIs updated

### Testing
- [ ] Application runs locally
- [ ] Authentication flow works
- [ ] API endpoints respond correctly
- [ ] File uploads function properly
- [ ] Database operations work (if enabled)

## 🔄 Continuous Deployment

### GitHub Actions Workflow

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy TattooAdmin

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Build application
      run: npm run build
      env:
        VITE_API_URL: ${{ secrets.VITE_API_URL }}
        VITE_FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY }}
    
    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v20
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.ORG_ID }}
        vercel-project-id: ${{ secrets.PROJECT_ID }}
```

## 📊 Monitoring Setup

### 1. Firebase Analytics
- Already configured in the application
- View analytics in Firebase Console

### 2. Error Tracking (Sentry)
```bash
# Install Sentry
npm install @sentry/react @sentry/tracing

# Add to environment
VITE_SENTRY_DSN=your-sentry-dsn
```

### 3. Performance Monitoring
- Use Firebase Performance Monitoring
- Add custom metrics for API response times

## 🔐 Production Security

### Environment Variables for Production

```bash
# Application
NODE_ENV=production
PORT=8000
HOST_URL=https://yourdomain.com

# CORS
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Database
DATABASE_URL=***************************************/tattoo_db
USE_DATABASE=true

# Security
JWT_SECRET=your-production-jwt-secret-minimum-64-characters-long
BCRYPT_ROUNDS=14

# Rate Limiting
RATE_LIMIT_MAX_REQUESTS=50
AUTH_RATE_LIMIT_MAX=3
```

### SSL/TLS Configuration

Most deployment platforms handle SSL automatically, but ensure:
- [ ] HTTPS enforced
- [ ] HTTP redirects to HTTPS
- [ ] Secure cookies enabled
- [ ] HSTS headers set

## 📞 Support

After deployment:

1. **Test all functionality** in production environment
2. **Monitor logs** for any errors
3. **Verify authentication** works with production URLs
4. **Check Firebase console** for user activity
5. **Test API endpoints** using production domain

### Common Issues

1. **CORS Errors**: Update `ALLOWED_ORIGINS` environment variable
2. **OAuth Redirect**: Update redirect URIs in Google Console
3. **Firebase Errors**: Verify project configuration
4. **Database Connection**: Check connection string and credentials

Your TattooAdmin application is now ready for production deployment! 🎉
