#!/bin/bash

# TattooAdmin Docker Setup Script
# Complete containerized deployment with Docker Compose

echo "🐳 TattooAdmin Docker Setup"
echo "=========================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    echo "Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    echo "Visit: https://docs.docker.com/compose/install/"
    exit 1
fi

print_status "Docker and Docker Compose are installed"

# Check if .env file exists
if [ ! -f .env ]; then
    print_error ".env file not found!"
    echo "Please create a .env file with your configuration."
    echo "You can copy from .env.example and update the values."
    exit 1
fi

print_status ".env file found"

# Load environment variables
export $(cat .env | grep -v '^#' | xargs)

# Validate required environment variables
REQUIRED_VARS=(
    "GOOGLE_CLIENT_ID"
    "GOOGLE_CLIENT_SECRET"
    "JWT_SECRET"
)

MISSING_VARS=()
for var in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        MISSING_VARS+=("$var")
    fi
done

if [ ${#MISSING_VARS[@]} -ne 0 ]; then
    print_error "Missing required environment variables:"
    for var in "${MISSING_VARS[@]}"; do
        echo "  - $var"
    done
    echo ""
    echo "Please update your .env file with these values."
    exit 1
fi

print_status "Required environment variables are set"

# Function to clean up Docker resources
cleanup_docker() {
    print_info "Cleaning up existing Docker resources..."
    
    # Stop and remove containers
    docker-compose down --remove-orphans 2>/dev/null || true
    
    # Remove unused images (optional)
    if [ "$1" = "--clean-all" ]; then
        print_info "Removing unused Docker images..."
        docker image prune -f
        docker system prune -f
    fi
    
    print_status "Docker cleanup completed"
}

# Function to build and start services
start_services() {
    print_info "Building Docker images..."
    
    # Build the main application
    if ! docker-compose build tattoo-admin; then
        print_error "Failed to build main application"
        exit 1
    fi
    
    # Build FastAPI backend (optional)
    if [ -f "fastapi-backend/requirements.txt" ]; then
        print_info "Building FastAPI backend..."
        if ! docker-compose build fastapi-backend; then
            print_warning "FastAPI backend build failed, continuing without it"
        else
            print_status "FastAPI backend built successfully"
        fi
    fi
    
    print_status "Docker images built successfully"
    
    print_info "Starting services..."
    
    # Start core services (postgres, redis)
    docker-compose up -d postgres redis
    
    # Wait for database to be ready
    print_info "Waiting for database to be ready..."
    sleep 10
    
    # Start main application
    docker-compose up -d tattoo-admin
    
    # Start FastAPI backend if available
    if docker-compose ps fastapi-backend &>/dev/null; then
        docker-compose up -d fastapi-backend
    fi
    
    print_status "Services started successfully"
}

# Function to show service status
show_status() {
    echo ""
    print_info "Service Status:"
    docker-compose ps
    
    echo ""
    print_info "Service URLs:"
    echo "  🌐 Main Application: http://localhost:8000"
    echo "  📚 API Documentation: http://localhost:8000/api/docs"
    
    if docker-compose ps fastapi-backend | grep -q "Up"; then
        echo "  🐍 FastAPI Backend: http://localhost:8001"
        echo "  📖 FastAPI Docs: http://localhost:8001/docs"
    fi
    
    echo "  🐘 PostgreSQL: localhost:5432"
    echo "  🔴 Redis: localhost:6379"
}

# Function to show logs
show_logs() {
    echo ""
    print_info "Recent logs:"
    docker-compose logs --tail=50 tattoo-admin
}

# Function to run health checks
health_check() {
    print_info "Running health checks..."
    
    # Check main application
    if curl -f http://localhost:8000/health &>/dev/null; then
        print_status "Main application is healthy"
    else
        print_error "Main application health check failed"
    fi
    
    # Check FastAPI backend
    if curl -f http://localhost:8001/health &>/dev/null; then
        print_status "FastAPI backend is healthy"
    else
        print_warning "FastAPI backend is not responding"
    fi
    
    # Check database
    if docker-compose exec -T postgres pg_isready -U tattoo_user -d tattoo_db &>/dev/null; then
        print_status "Database is healthy"
    else
        print_error "Database health check failed"
    fi
}

# Main script logic
case "${1:-start}" in
    "start")
        cleanup_docker
        start_services
        show_status
        echo ""
        print_status "🎉 TattooAdmin is now running in Docker!"
        echo ""
        print_info "Next steps:"
        echo "  1. Open http://localhost:8000 in your browser"
        echo "  2. Test the Google OAuth login"
        echo "  3. Check logs with: docker-compose logs -f"
        echo "  4. Stop with: docker-compose down"
        ;;
    
    "stop")
        print_info "Stopping TattooAdmin services..."
        docker-compose down
        print_status "Services stopped"
        ;;
    
    "restart")
        print_info "Restarting TattooAdmin services..."
        docker-compose restart
        show_status
        ;;
    
    "logs")
        docker-compose logs -f "${2:-tattoo-admin}"
        ;;
    
    "status")
        show_status
        ;;
    
    "health")
        health_check
        ;;
    
    "clean")
        cleanup_docker --clean-all
        ;;
    
    "build")
        print_info "Rebuilding Docker images..."
        docker-compose build --no-cache
        print_status "Images rebuilt"
        ;;
    
    *)
        echo "Usage: $0 {start|stop|restart|logs|status|health|clean|build}"
        echo ""
        echo "Commands:"
        echo "  start   - Start all services (default)"
        echo "  stop    - Stop all services"
        echo "  restart - Restart all services"
        echo "  logs    - Show logs (optionally specify service name)"
        echo "  status  - Show service status"
        echo "  health  - Run health checks"
        echo "  clean   - Clean up Docker resources"
        echo "  build   - Rebuild Docker images"
        exit 1
        ;;
esac
