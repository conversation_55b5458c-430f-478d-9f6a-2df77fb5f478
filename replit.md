# Tattoo Chatbot Management Panel

## Overview

This is a React-based full-stack web application for managing a tattoo chatbot system. The application provides a comprehensive management panel for admins and artists to configure chatbot agents, manage users, review conversation logs, maintain a knowledge base, and analyze system performance. The system features a unified chat interface with dual voice capabilities: WebRTC real-time voice conversations and voice-assisted text messaging, all accessible through a single clean interface with mode toggle functionality.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
The frontend uses React with TypeScript and follows a component-based architecture. Key architectural decisions include:

- **Routing**: Uses Wouter for client-side routing with URL-based navigation
- **State Management**: React Query (TanStack Query) for server state management and caching
- **UI Framework**: Radix UI components with Tailwind CSS for styling and theming
- **Form Handling**: React Hook Form for form validation and management
- **Authentication**: Custom auth context with JWT token storage in localStorage
- **Real-time Communication**: Socket.io for WebSocket connections and real-time chat

### Backend Architecture
The backend is built with Express.js and follows a RESTful API design:

- **Server Framework**: Express.js with TypeScript
- **API Design**: RESTful endpoints with consistent error handling and logging
- **Authentication**: JWT-based authentication with role-based access control
- **File Structure**: Modular routing system with separate route handlers
- **Development**: Vite integration for hot module replacement during development

### Data Storage Solutions
The application uses PostgreSQL with Drizzle ORM for data persistence:

- **Database**: PostgreSQL (Neon Database serverless)
- **ORM**: Drizzle ORM for type-safe database operations
- **Schema Management**: Drizzle Kit for migrations and schema management
- **Storage Interface**: Abstract storage layer for CRUD operations across all entities

### Authentication and Authorization
Multi-role authentication system with granular permissions:

- **Roles**: Admin (full access), Artist (limited access), Client (basic access)
- **Authentication Flow**: Username/password login with JWT tokens
- **Session Management**: localStorage-based token persistence
- **Route Protection**: Private route wrapper component for protected pages
- **Development Mode**: Mock admin user for development purposes

### Real-time Features
Socket.io integration for interactive chat capabilities:

- **Unified Chat System**: Real-time messaging with multiple AI agents and voice modes
- **Dual Voice Options**: WebRTC real-time voice chat and voice-assisted text messaging
- **Agent Switching**: Dynamic agent selection during conversations
- **Chat Logging & Webhooks**: Automated conversation tracing with webhook API for external analytics
- **Voice Mode Toggle**: Switch between real-time voice and text chat in unified interface

### AI Integration
Multi-provider AI ecosystem for comprehensive tattoo intelligence:

- **Chat Agents**: 6 specialized tattoo consultation agents with distinct personalities
- **OpenAI Integration**: GPT-5, DALL-E, Vision API, Whisper, and embeddings
- **Leonardo AI FLUX.1 Kontext**: Professional contextual tattoo generation with style continuity
- **Body-Part Optimization**: Intelligent sizing (832×1248 forearm, 1024×1456 upper arm, 1248×1568 back)
- **Style Presets**: Realistic B&W, Portrait, Ray Traced, Graphic Design, Illustration
- **Variation System**: Context-aware design refinement using previous generations
- **OpenAI Stencil Creation**: Professional stencil conversion with background removal
- **WebRTC Real-time Voice**: OpenAI Realtime API for continuous voice conversations
- **Vector Database**: Semantic knowledge search with contextual AI responses
- **3D Computer Vision**: Depth mapping, UV unwrapping, and ray-traced overlay
- **Agent Types**: Manager, Guidance, Designer, Analyst, Aftercare, and Booking specialists

### UI/UX Design System
Comprehensive design system with dark mode support:

- **Component Library**: Radix UI primitives with custom styling
- **Theming**: CSS custom properties with light/dark mode toggle
- **Responsive Design**: Mobile-first approach with collapsible navigation
- **Accessibility**: ARIA-compliant components and keyboard navigation
- **Brand Customization**: Configurable theme colors and styling

## SaaS Platform Architecture

### Phase 1: API Foundation (✅ COMPLETED & VALIDATED)
- **Chat Logs & Webhooks**: Real-time conversation tracing with external webhook integration ✅ Tested
- **Vector Database**: OpenAI embeddings for semantic knowledge base search ✅ Tested  
- **API Key Management**: Multi-provider API settings for OpenAI, Leonardo AI, Flux, Ideogram, Airtable ✅ Tested
- **Settings Service**: Encrypted credential storage with validation and usage tracking ✅ Tested
- **Analytics API**: Usage metrics and conversation insights ✅ Tested
- **Knowledge Management**: Full CRUD operations with vector search ✅ Tested

### Phase 1.5: 3D Try-On System (🚀 REVOLUTIONARY ADDITION)
- **True 3D Scanning**: Phone camera → depth maps + UV wraps with computer vision
- **Ray-Traced Overlay**: Realistic tattoo preview respecting skin curvature and lighting
- **Privacy-First Processing**: Local/edge computing - no cloud photo leaks
- **Professional Export**: Auto-stencil generation + color palette + artist packages
- **Complete Workflow**: Brainstorm → Generate → Analyze → 3D Preview → Export → Book Artist
- **CRM Integration**: Seamless tattoo shop workflow automation

### Phase 2: FastAPI Migration (✅ COMPLETED)
- **FastAPI Backend**: Python backend with OpenCV/MediaPipe computer vision ✅ Complete
- **Advanced CV Pipeline**: Real depth mapping, pose estimation, and segmentation ✅ Complete
- **Ray Tracing Engine**: Path-traced lighting with skin subsurface scattering ✅ Complete
- **Firebase Authentication**: User auth and scan history management ✅ Complete
- **GCP Integration**: Cloud storage for non-privacy mode processing ✅ Complete
- **Docker Deployment**: Production-ready containerization with Redis/PostgreSQL ✅ Complete

### Phase 3.5: Leonardo AI FLUX.1 Kontext Integration (✅ COMPLETED)
- **FLUX.1 Kontext API**: Professional tattoo generation with contextual continuity ✅ Complete
- **Body-Part Optimization**: Automatic sizing for forearm, upper arm, back, chest, etc. ✅ Complete
- **Style Presets**: Realistic B&W, Portrait, Ray Traced, Graphic Design presets ✅ Complete
- **Variation System**: Context-aware design refinement and iteration ✅ Complete
- **OpenAI Stencil Creation**: Professional stencil conversion and background removal ✅ Complete
- **Full UI Integration**: FLUX tab in Design Generator with comprehensive controls ✅ Complete

### Phase 3: Production Enhancement (🚀 IN PROGRESS)
- **Real-time WebSocket**: Live processing updates and streaming preview ⚡ Started
- **Mobile SDK Integration**: Native iOS/Android computer vision ⚡ Started
- **Advanced CV Models**: Custom trained models for tattoo-specific scanning ⚡ Started
- **Enterprise Multi-tenant**: Shop management and billing system ⚡ Started

### Phase 4: SaaS Features (PLANNED)
- **Multi-tenant Architecture**: User isolation and billing management
- **Advanced Analytics**: Conversation insights and usage metrics
- **AI Service Hub**: Complete integration with all AI providers for comprehensive creative tools

## External Dependencies

### Database and Storage
- **@neondatabase/serverless**: Serverless PostgreSQL database connection
- **drizzle-orm**: TypeScript ORM for database operations
- **drizzle-kit**: Database schema management and migrations
- **connect-pg-simple**: PostgreSQL session store for Express

### UI and Styling
- **@radix-ui/react-***: Comprehensive UI component library
- **@tailwindcss/vite**: Tailwind CSS integration with Vite
- **tailwindcss**: Utility-first CSS framework
- **class-variance-authority**: Type-safe component variants
- **clsx**: Conditional className utility

### Development and Build Tools
- **vite**: Fast build tool and development server
- **@vitejs/plugin-react**: React support for Vite
- **esbuild**: JavaScript bundler for production builds
- **tsx**: TypeScript execution environment
- **@replit/vite-plugin-***: Replit-specific development plugins

### Form and Data Management
- **react-hook-form**: Form validation and management
- **@hookform/resolvers**: Validation resolvers for React Hook Form
- **@tanstack/react-query**: Server state management and caching
- **zod**: TypeScript-first schema validation

### Real-time and Communication
- **socket.io**: WebSocket library for real-time communication
- **openai**: Official OpenAI API client
- **ws**: WebSocket implementation

### Date and Utility Libraries
- **date-fns**: Modern date utility library
- **nanoid**: URL-safe unique ID generator
- **cmdk**: Command palette component

### Charts and Visualization
- **recharts**: React charting library for dashboard analytics

### Audio and Media
- **react-audio-voice-recorder**: Audio recording functionality for voice chat