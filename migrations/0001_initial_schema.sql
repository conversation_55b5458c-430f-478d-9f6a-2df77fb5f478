
-- Initial schema migration
-- Created: 2025-09-02T02:52:48.312Z

-- Create users table
CREATE TABLE IF NOT EXISTS "users" (
  "id" SERIAL PRIMARY KEY,
  "username" TEXT NOT NULL UNIQUE,
  "password" TEXT NOT NULL,
  "email" TEXT NOT NULL UNIQUE,
  "first_name" TEXT NOT NULL,
  "last_name" TEXT NOT NULL,
  "role" TEXT NOT NULL DEFAULT 'client' CHECK ("role" IN ('admin', 'artist', 'client')),
  "status" TEXT NOT NULL DEFAULT 'active' CHECK ("status" IN ('active', 'inactive', 'pending')),
  "created_at" TIMESTAMP DEFAULT NOW()
);

-- Create agents table
CREATE TABLE IF NOT EXISTS "agents" (
  "id" SERIAL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "system_prompt" TEXT NOT NULL,
  "is_active" BOOLEAN DEFAULT true,
  "created_at" TIMESTAMP DEFAULT NOW(),
  "updated_at" TIMESTAMP DEFAULT NOW()
);

-- <PERSON><PERSON> chat_logs table
CREATE TABLE IF NOT EXISTS "chat_logs" (
  "id" SERIAL PRIMARY KEY,
  "user_id" INTEGER REFERENCES "users"("id"),
  "agent_id" INTEGER REFERENCES "agents"("id"),
  "conversation" TEXT NOT NULL,
  "status" TEXT NOT NULL DEFAULT 'completed' CHECK ("status" IN ('active', 'completed', 'pending', 'failed')),
  "created_at" TIMESTAMP DEFAULT NOW()
);

-- Create knowledge_base table
CREATE TABLE IF NOT EXISTS "knowledge_base" (
  "id" SERIAL PRIMARY KEY,
  "title" TEXT NOT NULL,
  "content" TEXT NOT NULL,
  "category" TEXT NOT NULL,
  "created_by_id" INTEGER REFERENCES "users"("id"),
  "created_at" TIMESTAMP DEFAULT NOW(),
  "updated_at" TIMESTAMP DEFAULT NOW()
);

-- Create settings table
CREATE TABLE IF NOT EXISTS "settings" (
  "id" SERIAL PRIMARY KEY,
  "key" TEXT NOT NULL UNIQUE,
  "value" TEXT NOT NULL,
  "updated_at" TIMESTAMP DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "idx_users_username" ON "users"("username");
CREATE INDEX IF NOT EXISTS "idx_users_email" ON "users"("email");
CREATE INDEX IF NOT EXISTS "idx_users_role" ON "users"("role");
CREATE INDEX IF NOT EXISTS "idx_users_status" ON "users"("status");

CREATE INDEX IF NOT EXISTS "idx_agents_active" ON "agents"("is_active");
CREATE INDEX IF NOT EXISTS "idx_agents_name" ON "agents"("name");

CREATE INDEX IF NOT EXISTS "idx_chat_logs_user" ON "chat_logs"("user_id");
CREATE INDEX IF NOT EXISTS "idx_chat_logs_agent" ON "chat_logs"("agent_id");
CREATE INDEX IF NOT EXISTS "idx_chat_logs_status" ON "chat_logs"("status");
CREATE INDEX IF NOT EXISTS "idx_chat_logs_created" ON "chat_logs"("created_at");

CREATE INDEX IF NOT EXISTS "idx_knowledge_category" ON "knowledge_base"("category");
CREATE INDEX IF NOT EXISTS "idx_knowledge_created_by" ON "knowledge_base"("created_by_id");
CREATE INDEX IF NOT EXISTS "idx_knowledge_title" ON "knowledge_base"("title");

CREATE INDEX IF NOT EXISTS "idx_settings_key" ON "settings"("key");

-- Insert default admin user (password: admin123 - hashed with bcrypt)
INSERT INTO "users" ("username", "password", "email", "first_name", "last_name", "role", "status")
VALUES (
  'admin',
  '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJL9.KeF.',
  '<EMAIL>',
  'Admin',
  'User',
  'admin',
  'active'
) ON CONFLICT (username) DO NOTHING;

-- Insert default artist user (password: artist123 - hashed with bcrypt)
INSERT INTO "users" ("username", "password", "email", "first_name", "last_name", "role", "status")
VALUES (
  'artist',
  '$2b$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
  '<EMAIL>',
  'Artist',
  'User',
  'artist',
  'active'
) ON CONFLICT (username) DO NOTHING;

-- Insert default agents
INSERT INTO "agents" ("name", "description", "system_prompt", "is_active")
VALUES 
  (
    'Designer Agent',
    'Specialized in tattoo design creation and artistic guidance',
    'You are a specialized tattoo design assistant. Help users create detailed design concepts, understand artistic composition, and translate ideas into visual descriptions for AI generation.',
    true
  ),
  (
    'Manager Agent',
    'General tattoo consultation and coordination',
    'You are a tattoo consultation manager. Help coordinate between different specialists, provide general guidance, and ensure users get comprehensive assistance.',
    true
  ),
  (
    'Aftercare Agent',
    'Expert in tattoo healing and maintenance',
    'You are a tattoo aftercare specialist. Provide expert guidance on proper healing procedures, care products, timeline expectations, and when to seek professional help.',
    true
  ),
  (
    'Booking Agent',
    'Assists with appointment scheduling and preparation',
    'You are a tattoo booking specialist. Help users understand the consultation process, prepare for appointments, connect with appropriate artists, and plan their tattoo journey.',
    true
  )
ON CONFLICT DO NOTHING;

-- Insert default knowledge base entries
INSERT INTO "knowledge_base" ("title", "content", "category", "created_by_id")
VALUES 
  (
    'Tattoo Styles Overview',
    'Comprehensive guide to different tattoo styles including traditional, neo-traditional, realism, geometric, watercolor, and minimalist approaches.',
    'tattoo_styles',
    1
  ),
  (
    'Aftercare Best Practices',
    'Complete aftercare instructions including cleaning procedures, healing timeline, products to use and avoid, and warning signs to watch for.',
    'aftercare',
    1
  ),
  (
    'Safety Guidelines',
    'Important safety information including sterile procedures, equipment standards, health considerations, and regulatory compliance.',
    'safety',
    1
  )
ON CONFLICT DO NOTHING;

-- Insert default settings
INSERT INTO "settings" ("key", "value")
VALUES 
  ('app_version', '1.0.0'),
  ('maintenance_mode', 'false'),
  ('registration_enabled', 'true'),
  ('max_file_size', '10485760'),
  ('session_timeout', '86400')
ON CONFLICT (key) DO NOTHING;
