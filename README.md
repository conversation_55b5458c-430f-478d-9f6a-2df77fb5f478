# TattooAdmin - AI-Powered Tattoo Management System

A comprehensive tattoo administration system with AI-powered design generation, 3D body scanning, and hybrid authentication using Firebase and Google OAuth.

## 🚀 Features

### 🎨 AI-Powered Design Generation
- **OpenAI Integration** - Generate custom tattoo designs using GPT-4
- **Leonardo AI** - Advanced artistic style generation
- **Style Customization** - Traditional, neo-traditional, realism, geometric styles
- **Design Refinement** - Iterative improvement with AI feedback

### 📱 3D Body Scanning & Mapping
- **Computer Vision** - Advanced body part detection and mapping
- **Depth Mapping** - 3D surface analysis for tattoo placement
- **UV Unwrapping** - Surface texture mapping for design application
- **Ray Tracing** - Realistic tattoo preview rendering

### 🔐 Hybrid Authentication System
- **Firebase Authentication** - Real-time user state management
- **Google OAuth 2.0** - Secure social login
- **JWT Tokens** - Stateless API authentication
- **Role-Based Access** - Admin, Artist, and Client permissions

### 📊 Real-Time Analytics
- **Firebase Analytics** - User behavior tracking
- **Performance Monitoring** - Application health metrics
- **Custom Events** - Design generation and user interaction tracking

### 🗄️ Flexible Data Storage
- **Firebase Firestore** - Real-time NoSQL database
- **Cloud Storage** - Secure file and image storage
- **Memory Storage** - Development mode fallback
- **PostgreSQL Support** - Production-ready relational database

## 🏗️ Architecture

### Frontend (React + TypeScript + Vite)
- **Modern React** with hooks and context
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **Radix UI** components
- **Wouter** for routing

### Backend Options
- **Express.js** - Full-stack Node.js server (Primary)
- **FastAPI** - Python-based AI services (Secondary)
- **Hybrid Architecture** - Best of both worlds

### Authentication Flow
```
User Login → Firebase Auth → JWT Token → API Access
     ↓
Google OAuth → Token Exchange → User Session
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Python 3.8+
- Firebase Project
- Google Cloud Console access

### 1. Clone Repository
```bash
git clone https://github.com/yourusername/TattooAdmin.git
cd TattooAdmin
```

### 2. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your credentials
# - Google OAuth credentials
# - Firebase configuration
# - API keys
```

### 3. Install Dependencies
```bash
# Install Node.js dependencies
npm install

# Install Firebase
npm install firebase
```

### 4. Start Development Server
```bash
# Start the full-stack application
npm run dev
```

The application will be available at:
- **Frontend & API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/api/docs

### 5. Optional: Start FastAPI Backend
```bash
# For advanced AI features
./start-fastapi.sh
```

FastAPI will be available at:
- **FastAPI API**: http://localhost:8001
- **API Documentation**: http://localhost:8001/docs

## 🔧 Configuration

### Environment Variables

#### Google OAuth
```bash
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8000/auth/callback
```

#### Firebase
```bash
FIREBASE_PROJECT_ID=your-firebase-project
FIREBASE_API_KEY=your-firebase-api-key
FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
```

#### Authentication Strategy
```bash
VITE_AUTH_STRATEGY=hybrid  # Options: firebase, fastapi, hybrid
```

#### API Keys
```bash
OPENAI_API_KEY=sk-your-openai-key
LEONARDO_API_KEY=your-leonardo-key
```

## 📚 API Documentation

### Authentication Endpoints
- `GET /auth/google` - Initiate Google OAuth
- `GET /auth/google/callback` - Handle OAuth callback
- `GET /auth/me` - Get current user
- `POST /auth/logout` - Logout user

### Design Generation
- `POST /api/ai/generate` - Generate tattoo design
- `POST /api/ai/enhance` - Enhance existing design
- `GET /api/designs` - List user designs

### 3D Scanning (FastAPI)
- `POST /api/v2/scan/ingest` - Upload body scan
- `POST /api/v2/scan/depth-map` - Generate depth map
- `POST /api/v2/scan/uv-unwrap` - UV unwrap surface

## 🔐 Security Features

### Implemented Security Measures
- ✅ **Password Hashing** - bcrypt with 12 rounds
- ✅ **JWT Authentication** - Secure token-based auth
- ✅ **CORS Protection** - Environment-based origin whitelist
- ✅ **Rate Limiting** - API and authentication limits
- ✅ **Input Validation** - Comprehensive request validation
- ✅ **File Upload Security** - Magic number validation
- ✅ **Secrets Management** - Encrypted secrets storage
- ✅ **Security Headers** - Helmet.js protection

### Security Headers Applied
- Content Security Policy
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- Strict-Transport-Security
- X-XSS-Protection

## 🧪 Testing

### Run Tests
```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage
```

### Test Authentication
1. Open http://localhost:8000
2. Click "Continue with Google"
3. Complete OAuth flow
4. Verify dashboard access

## 📱 Mobile Support

The Firebase configuration supports mobile app integration:

### React Native
```javascript
import { initializeApp } from 'firebase/app';
const app = initializeApp(firebaseConfig);
```

### Flutter
```dart
import 'package:firebase_core/firebase_core.dart';
await Firebase.initializeApp();
```

## 🚀 Deployment

### Production Environment Variables
```bash
NODE_ENV=production
ALLOWED_ORIGINS=https://yourdomain.com
DATABASE_URL=***********************************/tattoo_db
```

### Docker Deployment
```bash
# Build and run
docker-compose up -d
```

## 📊 Monitoring & Analytics

### Firebase Analytics Events
- User authentication
- Design generation
- Page views
- Error tracking

### Performance Monitoring
- API response times
- Database query performance
- File upload metrics

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Documentation
- [Setup Guide](FASTAPI_SETUP.md)
- [Firebase Integration](FIREBASE_INTEGRATION.md)
- [Security Guide](SECURITY.md)

### Troubleshooting
- Check the logs in both frontend and backend terminals
- Verify environment variables are set correctly
- Ensure Google OAuth is configured properly
- Test individual endpoints using API docs

### Contact
- Create an issue for bugs
- Start a discussion for questions
- Email: <EMAIL>

---

**Built with ❤️ using React, TypeScript, Firebase, and FastAPI**
