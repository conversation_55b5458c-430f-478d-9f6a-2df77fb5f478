"use client"

import { useAppStore } from "@/lib/store"
import { TraceChip } from "./trace-chip"

export function TraceChips() {
  const { traces } = useAppStore()

  if (traces.length === 0) return null

  const removeTrace = (id: string) => {
    // Remove trace logic would go here
    console.log("Remove trace:", id)
  }

  return (
    <div className="p-4 border-b border-border bg-muted/20">
      <div className="flex flex-wrap gap-2">
        {traces.map((trace) => (
          <TraceChip key={trace.id} trace={trace} onRemove={removeTrace} />
        ))}
      </div>
    </div>
  )
}
