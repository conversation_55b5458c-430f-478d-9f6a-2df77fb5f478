"use client"

import { useRef } from "react"
import { use<PERSON><PERSON><PERSON>, useLoader } from "@react-three/fiber"
import { TextureLoader } from "three"
import type { Mesh } from "three"
import type { PreviewState } from "@/lib/types"

interface TattooMeshProps {
  transform: PreviewState["transform"]
  selectedImage: string | null
  pathTrace: boolean
}

export function TattooMesh({ transform, selectedImage, pathTrace }: TattooMeshProps) {
  const meshRef = useRef<Mesh>(null)

  // Load texture if image is selected
  const texture = useLoader(TextureLoader, selectedImage || "/tattoo-texture.png")

  // Animate the mesh
  useFrame((state, delta) => {
    if (meshRef.current) {
      // Apply transform
      meshRef.current.position.set(transform.offset.x, transform.offset.y, transform.offset.z)
      meshRef.current.scale.set(transform.scale.x, transform.scale.y, transform.scale.z)
      meshRef.current.rotation.set(transform.rotate.x, transform.rotate.y, transform.rotate.z)

      // Add subtle animation if path tracing is enabled
      if (pathTrace) {
        meshRef.current.rotation.y += delta * 0.1
      }
    }
  })

  return (
    <group>
      {/* Main tattoo surface - cylinder representing arm/body part */}
      <mesh ref={meshRef} position={[0, 0, 0]}>
        <cylinderGeometry args={[1, 1, 2, 32]} />
        <meshStandardMaterial
          map={texture}
          transparent={true}
          opacity={0.9}
          roughness={pathTrace ? 0.1 : 0.3}
          metalness={pathTrace ? 0.2 : 0.0}
        />
      </mesh>

      {/* Wireframe overlay for better visualization */}
      <mesh position={[0, 0, 0]}>
        <cylinderGeometry args={[1.01, 1.01, 2.01, 32]} />
        <meshBasicMaterial wireframe={true} color="#444444" transparent={true} opacity={0.1} />
      </mesh>
    </group>
  )
}
