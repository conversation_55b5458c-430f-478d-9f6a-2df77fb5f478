"use client"

import type React from "react"

import { useState } from "react"
import { useAppStore } from "@/lib/store"
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Send, User, Mail, MessageSquare, Paperclip, CheckCircle, AlertCircle, Loader2 } from "lucide-react"

interface BookingFormData {
  name: string
  email: string
  phone: string
  message: string
  preferredDate: string
  bodyPart: string
  attachExportZip: boolean
}

export function BookingPanel() {
  const { imaging, export: exportState, selectedBodyPart } = useAppStore()
  const [formData, setFormData] = useState<BookingFormData>({
    name: "",
    email: "",
    phone: "",
    message: "",
    preferredDate: "",
    bodyPart: selectedBodyPart,
    attachExportZip: true,
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [errors, setErrors] = useState<Partial<BookingFormData>>({})

  const validateForm = (): boolean => {
    const newErrors: Partial<BookingFormData> = {}

    if (!formData.name.trim()) {
      newErrors.name = "Name is required"
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required"
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address"
    }

    if (!formData.message.trim()) {
      newErrors.message = "Please describe your tattoo vision"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsSubmitting(true)
    try {
      // Prepare submission data
      const submissionData = {
        ...formData,
        selectedImage: imaging.selectedImage,
        exportFiles: {
          stencilUrl: exportState.stencilUrl,
          svgUrl: exportState.svgUrl,
          palette: exportState.palette,
        },
        timestamp: new Date().toISOString(),
      }

      // Submit to CRM API
      const response = await fetch("/api/booking/submit", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(submissionData),
      })

      if (response.ok) {
        setIsSubmitted(true)
      } else {
        throw new Error("Submission failed")
      }
    } catch (error) {
      console.error("Booking submission failed:", error)
      // Handle error (could show toast notification)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange =
    (field: keyof BookingFormData) => (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setFormData((prev) => ({ ...prev, [field]: e.target.value }))
      // Clear error when user starts typing
      if (errors[field]) {
        setErrors((prev) => ({ ...prev, [field]: undefined }))
      }
    }

  const hasDesignAssets = !!(imaging.selectedImage || exportState.stencilUrl || exportState.svgUrl)

  if (isSubmitted) {
    return (
      <div className="h-full p-6 flex items-center justify-center">
        <Card className="p-8 max-w-md w-full text-center">
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-foreground mb-2">Booking Submitted!</h2>
          <p className="text-muted-foreground mb-4">
            Thank you for your submission. We'll review your design and get back to you within 24 hours.
          </p>
          <Button
            onClick={() => {
              setIsSubmitted(false)
              setFormData({
                name: "",
                email: "",
                phone: "",
                message: "",
                preferredDate: "",
                bodyPart: selectedBodyPart,
                attachExportZip: true,
              })
            }}
            variant="outline"
            className="w-full"
          >
            Submit Another Request
          </Button>
        </Card>
      </div>
    )
  }

  return (
    <div className="h-full p-6 overflow-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-foreground">Booking</h1>
        <p className="text-muted-foreground">Submit your design and contact information</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Booking Form */}
        <div className="lg:col-span-2">
          <Card className="p-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Contact Information */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <Label className="text-base font-medium">Contact Information</Label>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={handleInputChange("name")}
                      placeholder="Enter your full name"
                      className={errors.name ? "border-red-500" : ""}
                    />
                    {errors.name && (
                      <p className="text-xs text-red-500 flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.name}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange("email")}
                      placeholder="<EMAIL>"
                      className={errors.email ? "border-red-500" : ""}
                    />
                    {errors.email && (
                      <p className="text-xs text-red-500 flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.email}
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handleInputChange("phone")}
                      placeholder="(*************"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="preferredDate">Preferred Date</Label>
                    <Input
                      id="preferredDate"
                      type="date"
                      value={formData.preferredDate}
                      onChange={handleInputChange("preferredDate")}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* Tattoo Details */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  <Label className="text-base font-medium">Tattoo Details</Label>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bodyPart">Body Part</Label>
                  <Input
                    id="bodyPart"
                    value={formData.bodyPart}
                    onChange={handleInputChange("bodyPart")}
                    placeholder="e.g., forearm, shoulder, back"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message">Describe Your Vision *</Label>
                  <Textarea
                    id="message"
                    value={formData.message}
                    onChange={handleInputChange("message")}
                    placeholder="Tell us about your tattoo vision, size preferences, style notes, or any special requests..."
                    rows={4}
                    className={errors.message ? "border-red-500" : ""}
                  />
                  {errors.message && (
                    <p className="text-xs text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.message}
                    </p>
                  )}
                </div>
              </div>

              <Separator />

              {/* Attachment Options */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Paperclip className="h-4 w-4" />
                  <Label className="text-base font-medium">Attachments</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="attachExportZip"
                    checked={formData.attachExportZip}
                    onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, attachExportZip: !!checked }))}
                    disabled={!hasDesignAssets}
                  />
                  <Label htmlFor="attachExportZip" className="text-sm">
                    Attach design files (stencil, SVG, palette)
                  </Label>
                </div>

                {!hasDesignAssets && (
                  <p className="text-xs text-muted-foreground">Complete the design process to attach export files</p>
                )}
              </div>

              {/* Submit Button */}
              <Button type="submit" disabled={isSubmitting} className="w-full" size="lg">
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Submit Booking Request
                  </>
                )}
              </Button>
            </form>
          </Card>
        </div>

        {/* Summary Sidebar */}
        <div className="space-y-4">
          {/* Design Summary */}
          <Card className="p-4">
            <Label className="text-sm font-medium mb-3 block">Design Summary</Label>

            {imaging.selectedImage ? (
              <div className="space-y-3">
                <div className="aspect-square rounded-lg overflow-hidden bg-muted">
                  <img
                    src={imaging.selectedImage || "/placeholder.svg"}
                    alt="Selected design"
                    className="w-full h-full object-cover"
                  />
                </div>
                <Badge variant="secondary" className="w-full justify-center">
                  Design Ready
                </Badge>
              </div>
            ) : (
              <div className="text-center py-6 text-muted-foreground">
                <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-xs">No design selected</p>
              </div>
            )}
          </Card>

          {/* Export Files Status */}
          <Card className="p-4">
            <Label className="text-sm font-medium mb-3 block">Export Files</Label>

            <div className="space-y-2">
              <div className="flex items-center justify-between text-xs">
                <span>Stencil PNG</span>
                {exportState.stencilUrl ? (
                  <CheckCircle className="h-3 w-3 text-green-500" />
                ) : (
                  <div className="h-3 w-3 rounded-full bg-muted" />
                )}
              </div>

              <div className="flex items-center justify-between text-xs">
                <span>Vector SVG</span>
                {exportState.svgUrl ? (
                  <CheckCircle className="h-3 w-3 text-green-500" />
                ) : (
                  <div className="h-3 w-3 rounded-full bg-muted" />
                )}
              </div>

              <div className="flex items-center justify-between text-xs">
                <span>Color Palette</span>
                {exportState.palette ? (
                  <CheckCircle className="h-3 w-3 text-green-500" />
                ) : (
                  <div className="h-3 w-3 rounded-full bg-muted" />
                )}
              </div>
            </div>

            {hasDesignAssets && (
              <Badge variant="outline" className="w-full justify-center mt-3">
                Files will be attached
              </Badge>
            )}
          </Card>

          {/* Contact Info */}
          <Card className="p-4">
            <Label className="text-sm font-medium mb-3 block">Need Help?</Label>
            <div className="space-y-2 text-xs text-muted-foreground">
              <div className="flex items-center gap-2">
                <Mail className="h-3 w-3" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-2">
                <User className="h-3 w-3" />
                <span>(555) 123-TATT</span>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}
