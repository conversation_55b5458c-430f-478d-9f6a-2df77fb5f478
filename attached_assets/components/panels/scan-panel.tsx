"use client"

import type React from "react"

import { useState, useRef } from "react"
import { useAppStore } from "@/lib/store"
import { api } from "@/lib/api"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Upload, Camera, Loader2, ImageIcon } from "lucide-react"
import { cn } from "@/lib/utils"

export function ScanPanel() {
  const { scan, setScanPhoto, setScanResults } = useAppStore()
  const [isProcessing, setIsProcessing] = useState(false)
  const [isComputing, setIsComputing] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Convert file to base64 for display
    const reader = new FileReader()
    reader.onload = async (e) => {
      const photoUrl = e.target?.result as string
      setScanPhoto(photoUrl)

      // Process the scan
      setIsProcessing(true)
      try {
        const result = await api.scan.ingest(file)
        setScanResults({
          depthPng: result.depthPng,
          maskPng: result.maskPng,
        })
      } catch (error) {
        console.error("Scan processing failed:", error)
      } finally {
        setIsProcessing(false)
      }
    }
    reader.readAsDataURL(file)
  }

  const handleComputeUV = async () => {
    if (!scan.depthPng || !scan.maskPng) return

    setIsComputing(true)
    try {
      const result = await api.scan.unwrap(scan.depthPng, scan.maskPng)
      setScanResults({
        uvPng: result.uvPng,
        normalPng: result.normalPng,
      })
    } catch (error) {
      console.error("UV computation failed:", error)
    } finally {
      setIsComputing(false)
    }
  }

  const triggerFileInput = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="h-full p-6 overflow-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-foreground">Scan</h1>
        <p className="text-muted-foreground">Capture or import a photo to begin the design process</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Photo Upload Section */}
        <Card className="p-6">
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Camera className="h-5 w-5" />
              <Label className="text-base font-medium">Photo Input</Label>
            </div>

            {!scan.photo ? (
              <div
                onClick={triggerFileInput}
                className={cn(
                  "border-2 border-dashed border-border rounded-lg p-8 text-center cursor-pointer transition-colors",
                  "hover:border-primary hover:bg-accent/50",
                  isProcessing && "pointer-events-none opacity-50",
                )}
              >
                {isProcessing ? (
                  <div className="flex flex-col items-center gap-2">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <p className="text-sm text-muted-foreground">Processing scan...</p>
                  </div>
                ) : (
                  <div className="flex flex-col items-center gap-2">
                    <Upload className="h-8 w-8 text-muted-foreground" />
                    <p className="text-sm font-medium">Click to upload photo</p>
                    <p className="text-xs text-muted-foreground">PNG, JPG up to 10MB</p>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                <div className="relative rounded-lg overflow-hidden bg-muted">
                  <img
                    src={scan.photo || "/placeholder.svg"}
                    alt="Uploaded scan"
                    className="w-full h-48 object-cover"
                  />
                </div>
                <Button
                  variant="outline"
                  onClick={triggerFileInput}
                  disabled={isProcessing}
                  className="w-full bg-transparent"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Upload New Photo
                </Button>
              </div>
            )}

            <Input ref={fileInputRef} type="file" accept="image/*" onChange={handleFileUpload} className="hidden" />
          </div>
        </Card>

        {/* Results Section */}
        <Card className="p-6">
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <ImageIcon className="h-5 w-5" />
              <Label className="text-base font-medium">Scan Results</Label>
            </div>

            {scan.depthPng || scan.maskPng ? (
              <div className="grid grid-cols-2 gap-4">
                {/* Depth Thumbnail */}
                <div className="space-y-2">
                  <Label className="text-sm text-muted-foreground">Depth Map</Label>
                  {scan.depthPng ? (
                    <div className="aspect-square rounded-lg overflow-hidden bg-muted">
                      <img
                        src={scan.depthPng || "/placeholder.svg"}
                        alt="Depth map"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ) : (
                    <div className="aspect-square rounded-lg bg-muted/50 flex items-center justify-center">
                      <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                    </div>
                  )}
                </div>

                {/* Mask Thumbnail */}
                <div className="space-y-2">
                  <Label className="text-sm text-muted-foreground">Mask</Label>
                  {scan.maskPng ? (
                    <div className="aspect-square rounded-lg overflow-hidden bg-muted">
                      <img src={scan.maskPng || "/placeholder.svg"} alt="Mask" className="w-full h-full object-cover" />
                    </div>
                  ) : (
                    <div className="aspect-square rounded-lg bg-muted/50 flex items-center justify-center">
                      <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <ImageIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Upload a photo to see scan results</p>
              </div>
            )}

            {/* Compute UV Button */}
            <Button
              onClick={handleComputeUV}
              disabled={!scan.depthPng || !scan.maskPng || isComputing}
              className="w-full"
            >
              {isComputing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Computing UV...
                </>
              ) : (
                "Compute UV"
              )}
            </Button>

            {/* UV Results */}
            {(scan.uvPng || scan.normalPng) && (
              <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                <div className="space-y-2">
                  <Label className="text-sm text-muted-foreground">UV Map</Label>
                  {scan.uvPng && (
                    <div className="aspect-square rounded-lg overflow-hidden bg-muted">
                      <img src={scan.uvPng || "/placeholder.svg"} alt="UV map" className="w-full h-full object-cover" />
                    </div>
                  )}
                </div>
                <div className="space-y-2">
                  <Label className="text-sm text-muted-foreground">Normal Map</Label>
                  {scan.normalPng && (
                    <div className="aspect-square rounded-lg overflow-hidden bg-muted">
                      <img
                        src={scan.normalPng || "/placeholder.svg"}
                        alt="Normal map"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </Card>
      </div>
    </div>
  )
}
