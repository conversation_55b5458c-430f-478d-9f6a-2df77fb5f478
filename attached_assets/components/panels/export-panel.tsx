"use client"

import { useState } from "react"
import { useAppStore } from "@/lib/store"
import { api } from "@/lib/api"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Download, FileImage, FileText, Palette, Archive, Loader2, CheckCircle } from "lucide-react"

export function ExportPanel() {
  const { export: exportState, imaging, setExportResults } = useAppStore()
  const [isGenerating, setIsGenerating] = useState(false)
  const [isCreatingZip, setIsCreatingZip] = useState(false)
  const [generatedFiles, setGeneratedFiles] = useState<{
    stencil: boolean
    svg: boolean
    palette: boolean
  }>({
    stencil: false,
    svg: false,
    palette: false,
  })

  const generateStencil = async () => {
    if (!imaging.selectedImage) return

    setIsGenerating(true)
    try {
      const result = await api.export.stencil()
      setExportResults({ stencilUrl: result.stencilUrl })
      setGeneratedFiles((prev) => ({ ...prev, stencil: true }))
    } catch (error) {
      console.error("Stencil generation failed:", error)
    } finally {
      setIsGenerating(false)
    }
  }

  const generateSVG = async () => {
    if (!imaging.selectedImage) return

    setIsGenerating(true)
    try {
      const result = await api.export.svg()
      setExportResults({ svgUrl: result.svgUrl })
      setGeneratedFiles((prev) => ({ ...prev, svg: true }))
    } catch (error) {
      console.error("SVG generation failed:", error)
    } finally {
      setIsGenerating(false)
    }
  }

  const generatePalette = async () => {
    if (!imaging.selectedImage) return

    setIsGenerating(true)
    try {
      const result = await api.export.palette()
      setExportResults({ palette: result.palette })
      setGeneratedFiles((prev) => ({ ...prev, palette: true }))
    } catch (error) {
      console.error("Palette generation failed:", error)
    } finally {
      setIsGenerating(false)
    }
  }

  const createZip = async () => {
    setIsCreatingZip(true)
    try {
      const result = await api.export.createZip()
      // Trigger download
      const link = document.createElement("a")
      link.href = result.zipUrl
      link.download = "tattoo-design-export.zip"
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } catch (error) {
      console.error("ZIP creation failed:", error)
    } finally {
      setIsCreatingZip(false)
    }
  }

  const downloadFile = (url: string, filename: string) => {
    const link = document.createElement("a")
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const hasSelectedImage = !!imaging.selectedImage
  const allFilesGenerated = generatedFiles.stencil && generatedFiles.svg && generatedFiles.palette

  return (
    <div className="h-full p-6 overflow-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-foreground">Export</h1>
        <p className="text-muted-foreground">Download your design files and assets</p>
      </div>

      {!hasSelectedImage && (
        <Card className="p-6 mb-6 border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/20">
          <div className="flex items-center gap-2 text-amber-800 dark:text-amber-200">
            <FileImage className="h-5 w-5" />
            <p className="text-sm font-medium">No design selected</p>
          </div>
          <p className="text-xs text-amber-700 dark:text-amber-300 mt-1">
            Please select an image from the Imaging panel to enable export options.
          </p>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Export Options */}
        <div className="space-y-4">
          {/* Stencil Export */}
          <Card className="p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <FileImage className="h-4 w-4" />
                <Label className="text-sm font-medium">Stencil PNG</Label>
                {generatedFiles.stencil && (
                  <Badge variant="secondary" className="text-xs">
                    Ready
                  </Badge>
                )}
              </div>
              {exportState.stencilUrl && <CheckCircle className="h-4 w-4 text-green-500" />}
            </div>
            <p className="text-xs text-muted-foreground mb-3">
              High-contrast black and white stencil for tattoo application
            </p>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={generateStencil}
                disabled={!hasSelectedImage || isGenerating}
                className="flex-1 bg-transparent"
              >
                {isGenerating ? (
                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                ) : (
                  <FileImage className="h-3 w-3 mr-1" />
                )}
                Generate
              </Button>
              {exportState.stencilUrl && (
                <Button size="sm" onClick={() => downloadFile(exportState.stencilUrl!, "tattoo-stencil.png")}>
                  <Download className="h-3 w-3 mr-1" />
                  Download
                </Button>
              )}
            </div>
          </Card>

          {/* SVG Export */}
          <Card className="p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <Label className="text-sm font-medium">Vector SVG</Label>
                {generatedFiles.svg && (
                  <Badge variant="secondary" className="text-xs">
                    Ready
                  </Badge>
                )}
              </div>
              {exportState.svgUrl && <CheckCircle className="h-4 w-4 text-green-500" />}
            </div>
            <p className="text-xs text-muted-foreground mb-3">Scalable vector format for precise tattoo outlines</p>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={generateSVG}
                disabled={!hasSelectedImage || isGenerating}
                className="flex-1 bg-transparent"
              >
                {isGenerating ? (
                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                ) : (
                  <FileText className="h-3 w-3 mr-1" />
                )}
                Generate
              </Button>
              {exportState.svgUrl && (
                <Button size="sm" onClick={() => downloadFile(exportState.svgUrl!, "tattoo-design.svg")}>
                  <Download className="h-3 w-3 mr-1" />
                  Download
                </Button>
              )}
            </div>
          </Card>

          {/* Palette Export */}
          <Card className="p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                <Label className="text-sm font-medium">Color Palette</Label>
                {generatedFiles.palette && (
                  <Badge variant="secondary" className="text-xs">
                    Ready
                  </Badge>
                )}
              </div>
              {exportState.palette && <CheckCircle className="h-4 w-4 text-green-500" />}
            </div>
            <p className="text-xs text-muted-foreground mb-3">Extracted color palette for ink selection reference</p>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={generatePalette}
                disabled={!hasSelectedImage || isGenerating}
                className="flex-1 bg-transparent"
              >
                {isGenerating ? (
                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                ) : (
                  <Palette className="h-3 w-3 mr-1" />
                )}
                Generate
              </Button>
              {exportState.palette && (
                <Button
                  size="sm"
                  onClick={() => {
                    const paletteData = JSON.stringify(exportState.palette, null, 2)
                    const blob = new Blob([paletteData], { type: "application/json" })
                    const url = URL.createObjectURL(blob)
                    downloadFile(url, "color-palette.json")
                    URL.revokeObjectURL(url)
                  }}
                >
                  <Download className="h-3 w-3 mr-1" />
                  Download
                </Button>
              )}
            </div>
          </Card>
        </div>

        {/* Preview and ZIP Creation */}
        <div className="space-y-4">
          {/* Preview Section */}
          <Card className="p-4">
            <Label className="text-sm font-medium mb-3 block">Export Preview</Label>

            {hasSelectedImage ? (
              <div className="space-y-4">
                {/* Original Design */}
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Original Design</Label>
                  <div className="aspect-square rounded-lg overflow-hidden bg-muted">
                    <img
                      src={imaging.selectedImage || "/placeholder.svg"}
                      alt="Original design"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>

                {/* Generated Files Preview */}
                {(exportState.stencilUrl || exportState.svgUrl) && (
                  <>
                    <Separator />
                    <div className="grid grid-cols-2 gap-2">
                      {exportState.stencilUrl && (
                        <div className="space-y-1">
                          <Label className="text-xs text-muted-foreground">Stencil</Label>
                          <div className="aspect-square rounded-lg overflow-hidden bg-muted">
                            <img
                              src={exportState.stencilUrl || "/placeholder.svg"}
                              alt="Stencil preview"
                              className="w-full h-full object-cover"
                            />
                          </div>
                        </div>
                      )}

                      {exportState.svgUrl && (
                        <div className="space-y-1">
                          <Label className="text-xs text-muted-foreground">Vector</Label>
                          <div className="aspect-square rounded-lg overflow-hidden bg-muted">
                            <img
                              src={exportState.svgUrl || "/placeholder.svg"}
                              alt="SVG preview"
                              className="w-full h-full object-cover"
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </>
                )}

                {/* Color Palette Preview */}
                {exportState.palette && (
                  <>
                    <Separator />
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">Color Palette</Label>
                      <div className="flex flex-wrap gap-2">
                        {exportState.palette.map((color, index) => (
                          <div
                            key={index}
                            className="w-8 h-8 rounded-md border border-border"
                            style={{ backgroundColor: color }}
                            title={color}
                          />
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <FileImage className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Select a design to see export preview</p>
              </div>
            )}
          </Card>

          {/* ZIP Creation */}
          <Card className="p-4">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Archive className="h-4 w-4" />
                <Label className="text-sm font-medium">Complete Package</Label>
              </div>

              <p className="text-xs text-muted-foreground">
                Create a ZIP file containing all generated assets for easy sharing and backup.
              </p>

              <Button onClick={createZip} disabled={!allFilesGenerated || isCreatingZip} className="w-full">
                {isCreatingZip ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Creating ZIP...
                  </>
                ) : (
                  <>
                    <Archive className="h-4 w-4 mr-2" />
                    Create ZIP Package
                  </>
                )}
              </Button>

              {!allFilesGenerated && (
                <p className="text-xs text-muted-foreground text-center">
                  Generate all export files first to create ZIP package
                </p>
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}
