"use client"

import { Suspense } from "react"
import { Canvas } from "@react-three/fiber"
import { OrbitControls, Environment, ContactShadows } from "@react-three/drei"
import { useAppStore } from "@/lib/store"
import { api } from "@/lib/api"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Loader2, Lightbulb, Zap, Layers } from "lucide-react"
import { TattooMesh } from "@/components/3d/tattoo-mesh"
import { useState } from "react"

export function PreviewPanel() {
  const { preview, imaging, updateTransform, updateLighting } = useAppStore()
  const [isBaking, setIsBaking] = useState(false)

  const handleBakeOverlay = async () => {
    setIsBaking(true)
    try {
      await api.preview.bake(preview.transform, preview.lighting)
    } catch (error) {
      console.error("Bake overlay failed:", error)
    } finally {
      setIsBaking(false)
    }
  }

  return (
    <div className="h-full p-6 overflow-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-foreground">3D Preview</h1>
        <p className="text-muted-foreground">Preview your design in 3D with realistic lighting</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
        {/* 3D Canvas */}
        <div className="lg:col-span-2">
          <Card className="p-4 h-full">
            <div className="h-full rounded-lg overflow-hidden bg-gradient-to-br from-slate-900 to-slate-800">
              <Canvas
                camera={{ position: [0, 0, 5], fov: 50 }}
                gl={{ antialias: true, alpha: true }}
                className="w-full h-full"
              >
                <Suspense fallback={null}>
                  {/* Lighting */}
                  <ambientLight intensity={0.4} />
                  <directionalLight position={[10, 10, 5]} intensity={1} />

                  {/* Environment */}
                  {preview.lighting.ibl && <Environment preset="studio" />}

                  {/* 3D Model */}
                  <TattooMesh
                    transform={preview.transform}
                    selectedImage={imaging.selectedImage}
                    pathTrace={preview.lighting.pathTrace}
                  />

                  {/* Controls */}
                  <OrbitControls
                    enablePan={true}
                    enableZoom={true}
                    enableRotate={true}
                    minDistance={2}
                    maxDistance={10}
                  />

                  {/* Ground shadow */}
                  <ContactShadows position={[0, -1.4, 0]} opacity={0.4} scale={10} blur={2.5} far={4} />
                </Suspense>
              </Canvas>
            </div>
          </Card>
        </div>

        {/* Controls Panel */}
        <div className="space-y-6">
          {/* Transform Controls */}
          <Card className="p-4">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Layers className="h-4 w-4" />
                <Label className="text-sm font-medium">Transform</Label>
              </div>

              {/* Offset Controls */}
              <div className="space-y-3">
                <Label className="text-xs text-muted-foreground">Offset</Label>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">X: {preview.transform.offset.x.toFixed(2)}</Label>
                  </div>
                  <Slider
                    value={[preview.transform.offset.x]}
                    onValueChange={([x]) => updateTransform({ offset: { ...preview.transform.offset, x } })}
                    min={-2}
                    max={2}
                    step={0.1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">Y: {preview.transform.offset.y.toFixed(2)}</Label>
                  </div>
                  <Slider
                    value={[preview.transform.offset.y]}
                    onValueChange={([y]) => updateTransform({ offset: { ...preview.transform.offset, y } })}
                    min={-2}
                    max={2}
                    step={0.1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">Z: {preview.transform.offset.z.toFixed(2)}</Label>
                  </div>
                  <Slider
                    value={[preview.transform.offset.z]}
                    onValueChange={([z]) => updateTransform({ offset: { ...preview.transform.offset, z } })}
                    min={-2}
                    max={2}
                    step={0.1}
                    className="w-full"
                  />
                </div>
              </div>

              <Separator />

              {/* Scale Controls */}
              <div className="space-y-3">
                <Label className="text-xs text-muted-foreground">Scale</Label>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">X: {preview.transform.scale.x.toFixed(2)}</Label>
                  </div>
                  <Slider
                    value={[preview.transform.scale.x]}
                    onValueChange={([x]) => updateTransform({ scale: { ...preview.transform.scale, x } })}
                    min={0.1}
                    max={3}
                    step={0.1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">Y: {preview.transform.scale.y.toFixed(2)}</Label>
                  </div>
                  <Slider
                    value={[preview.transform.scale.y]}
                    onValueChange={([y]) => updateTransform({ scale: { ...preview.transform.scale, y } })}
                    min={0.1}
                    max={3}
                    step={0.1}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">Z: {preview.transform.scale.z.toFixed(2)}</Label>
                  </div>
                  <Slider
                    value={[preview.transform.scale.z]}
                    onValueChange={([z]) => updateTransform({ scale: { ...preview.transform.scale, z } })}
                    min={0.1}
                    max={3}
                    step={0.1}
                    className="w-full"
                  />
                </div>
              </div>

              <Separator />

              {/* Rotation Controls */}
              <div className="space-y-3">
                <Label className="text-xs text-muted-foreground">Rotation (degrees)</Label>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">X: {Math.round((preview.transform.rotate.x * 180) / Math.PI)}°</Label>
                  </div>
                  <Slider
                    value={[(preview.transform.rotate.x * 180) / Math.PI]}
                    onValueChange={([degrees]) =>
                      updateTransform({ rotate: { ...preview.transform.rotate, x: (degrees * Math.PI) / 180 } })
                    }
                    min={-180}
                    max={180}
                    step={5}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">Y: {Math.round((preview.transform.rotate.y * 180) / Math.PI)}°</Label>
                  </div>
                  <Slider
                    value={[(preview.transform.rotate.y * 180) / Math.PI]}
                    onValueChange={([degrees]) =>
                      updateTransform({ rotate: { ...preview.transform.rotate, y: (degrees * Math.PI) / 180 } })
                    }
                    min={-180}
                    max={180}
                    step={5}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">Z: {Math.round((preview.transform.rotate.z * 180) / Math.PI)}°</Label>
                  </div>
                  <Slider
                    value={[(preview.transform.rotate.z * 180) / Math.PI]}
                    onValueChange={([degrees]) =>
                      updateTransform({ rotate: { ...preview.transform.rotate, z: (degrees * Math.PI) / 180 } })
                    }
                    min={-180}
                    max={180}
                    step={5}
                    className="w-full"
                  />
                </div>
              </div>
            </div>
          </Card>

          {/* Lighting Controls */}
          <Card className="p-4">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Lightbulb className="h-4 w-4" />
                <Label className="text-sm font-medium">Lighting</Label>
              </div>

              <div className="flex items-center justify-between">
                <Label className="text-sm">IBL Environment</Label>
                <Switch checked={preview.lighting.ibl} onCheckedChange={(ibl) => updateLighting({ ibl })} />
              </div>

              <div className="flex items-center justify-between">
                <Label className="text-sm">Path Tracing</Label>
                <Switch
                  checked={preview.lighting.pathTrace}
                  onCheckedChange={(pathTrace) => updateLighting({ pathTrace })}
                />
              </div>
            </div>
          </Card>

          {/* Bake Controls */}
          <Card className="p-4">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                <Label className="text-sm font-medium">Render</Label>
              </div>

              <Button onClick={handleBakeOverlay} disabled={isBaking || !imaging.selectedImage} className="w-full">
                {isBaking ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Baking...
                  </>
                ) : (
                  <>
                    <Zap className="h-4 w-4 mr-2" />
                    Bake Overlay
                  </>
                )}
              </Button>

              {!imaging.selectedImage && (
                <p className="text-xs text-muted-foreground text-center">
                  Select an image from the Imaging panel first
                </p>
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}
