"use client"

import type React from "react"

import { useState, useRef } from "react"
import { useAppStore } from "@/lib/store"
import { api } from "@/lib/api"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Loader2, Wand2, Edit3, Upload, Check } from "lucide-react"

const leonardoStyles = ["Realistic", "Anime", "Digital Art", "Oil Painting", "Watercolor", "Sketch", "3D Render"]

export function ImagingPanel() {
  const { imaging, setSelectedImage, addEdit } = useAppStore()
  const [activeTab, setActiveTab] = useState("leonardo")

  // Leonardo state
  const [leonardoForm, setLeonardoForm] = useState({
    prompt: "",
    style: "Realistic",
    seed: Math.floor(Math.random() * 1000000),
    count: 4,
  })
  const [leonardoResults, setLeonardoResults] = useState<string[]>([])
  const [isGenerating, setIsGenerating] = useState(false)

  // OpenAI Edit state
  const [openaiForm, setOpenaiForm] = useState({
    image: "",
    mask: "",
    prompt: "",
  })
  const [openaiResult, setOpenaiResult] = useState<string | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const imageInputRef = useRef<HTMLInputElement>(null)
  const maskInputRef = useRef<HTMLInputElement>(null)

  const handleLeonardoGenerate = async () => {
    if (!leonardoForm.prompt.trim()) return

    setIsGenerating(true)
    try {
      const result = await api.ai.leonardo.generate(
        leonardoForm.prompt,
        leonardoForm.style,
        leonardoForm.seed,
        leonardoForm.count,
      )
      setLeonardoResults(result.images || [])
    } catch (error) {
      console.error("Leonardo generation failed:", error)
    } finally {
      setIsGenerating(false)
    }
  }

  const handleOpenAIEdit = async () => {
    if (!openaiForm.image || !openaiForm.mask || !openaiForm.prompt.trim()) return

    setIsEditing(true)
    try {
      const result = await api.ai.openai.edit(openaiForm.image, openaiForm.mask, openaiForm.prompt)
      setOpenaiResult(result.image)
    } catch (error) {
      console.error("OpenAI edit failed:", error)
    } finally {
      setIsEditing(false)
    }
  }

  const handleFileUpload = (type: "image" | "mask") => (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const result = e.target?.result as string
      setOpenaiForm((prev) => ({
        ...prev,
        [type]: result,
      }))
    }
    reader.readAsDataURL(file)
  }

  const handleUseInPreview = (imageUrl: string, type: "leonardo" | "openai") => {
    setSelectedImage(imageUrl)
    addEdit({
      type,
      image: imageUrl,
      prompt: type === "leonardo" ? leonardoForm.prompt : openaiForm.prompt,
    })
  }

  return (
    <div className="h-full p-6 overflow-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-foreground">Imaging</h1>
        <p className="text-muted-foreground">Generate and edit designs using AI</p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="leonardo" className="flex items-center gap-2">
            <Wand2 className="h-4 w-4" />
            Leonardo AI
          </TabsTrigger>
          <TabsTrigger value="openai" className="flex items-center gap-2">
            <Edit3 className="h-4 w-4" />
            OpenAI Edit
          </TabsTrigger>
        </TabsList>

        {/* Leonardo Tab */}
        <TabsContent value="leonardo" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Leonardo Controls */}
            <Card className="p-6">
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Wand2 className="h-5 w-5" />
                  <Label className="text-base font-medium">Generate Design</Label>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="leonardo-prompt">Prompt</Label>
                  <Textarea
                    id="leonardo-prompt"
                    placeholder="Describe the tattoo design you want to generate..."
                    value={leonardoForm.prompt}
                    onChange={(e) => setLeonardoForm((prev) => ({ ...prev, prompt: e.target.value }))}
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="leonardo-style">Style</Label>
                    <Select
                      value={leonardoForm.style}
                      onValueChange={(value) => setLeonardoForm((prev) => ({ ...prev, style: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {leonardoStyles.map((style) => (
                          <SelectItem key={style} value={style}>
                            {style}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="leonardo-seed">Seed</Label>
                    <Input
                      id="leonardo-seed"
                      type="number"
                      value={leonardoForm.seed}
                      onChange={(e) =>
                        setLeonardoForm((prev) => ({ ...prev, seed: Number.parseInt(e.target.value) || 0 }))
                      }
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="leonardo-count">Count: {leonardoForm.count}</Label>
                  <Input
                    id="leonardo-count"
                    type="range"
                    min="1"
                    max="8"
                    value={leonardoForm.count}
                    onChange={(e) => setLeonardoForm((prev) => ({ ...prev, count: Number.parseInt(e.target.value) }))}
                    className="w-full"
                  />
                </div>

                <Button
                  onClick={handleLeonardoGenerate}
                  disabled={!leonardoForm.prompt.trim() || isGenerating}
                  className="w-full"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Wand2 className="h-4 w-4 mr-2" />
                      Generate
                    </>
                  )}
                </Button>
              </div>
            </Card>

            {/* Leonardo Results */}
            <Card className="p-6">
              <div className="space-y-4">
                <Label className="text-base font-medium">Generated Results</Label>

                {leonardoResults.length > 0 ? (
                  <div className="grid grid-cols-2 gap-4">
                    {leonardoResults.map((imageUrl, index) => (
                      <div key={index} className="space-y-2">
                        <div className="aspect-square rounded-lg overflow-hidden bg-muted">
                          <img
                            src={imageUrl || "/placeholder.svg"}
                            alt={`Generated design ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <Button size="sm" onClick={() => handleUseInPreview(imageUrl, "leonardo")} className="w-full">
                          <Check className="h-3 w-3 mr-1" />
                          Use in Preview
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Wand2 className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">Generate designs to see results here</p>
                  </div>
                )}
              </div>
            </Card>
          </div>
        </TabsContent>

        {/* OpenAI Edit Tab */}
        <TabsContent value="openai" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* OpenAI Controls */}
            <Card className="p-6">
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Edit3 className="h-5 w-5" />
                  <Label className="text-base font-medium">Edit Image</Label>
                </div>

                {/* Image Upload */}
                <div className="space-y-2">
                  <Label>Base Image</Label>
                  {openaiForm.image ? (
                    <div className="space-y-2">
                      <div className="aspect-video rounded-lg overflow-hidden bg-muted">
                        <img
                          src={openaiForm.image || "/placeholder.svg"}
                          alt="Base image"
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => imageInputRef.current?.click()}
                        className="w-full"
                      >
                        <Upload className="h-3 w-3 mr-1" />
                        Change Image
                      </Button>
                    </div>
                  ) : (
                    <div
                      onClick={() => imageInputRef.current?.click()}
                      className="border-2 border-dashed border-border rounded-lg p-6 text-center cursor-pointer hover:border-primary hover:bg-accent/50 transition-colors"
                    >
                      <Upload className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
                      <p className="text-sm">Click to upload base image</p>
                    </div>
                  )}
                  <input
                    ref={imageInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleFileUpload("image")}
                    className="hidden"
                  />
                </div>

                {/* Mask Upload */}
                <div className="space-y-2">
                  <Label>Mask Image</Label>
                  {openaiForm.mask ? (
                    <div className="space-y-2">
                      <div className="aspect-video rounded-lg overflow-hidden bg-muted">
                        <img
                          src={openaiForm.mask || "/placeholder.svg"}
                          alt="Mask image"
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => maskInputRef.current?.click()}
                        className="w-full"
                      >
                        <Upload className="h-3 w-3 mr-1" />
                        Change Mask
                      </Button>
                    </div>
                  ) : (
                    <div
                      onClick={() => maskInputRef.current?.click()}
                      className="border-2 border-dashed border-border rounded-lg p-6 text-center cursor-pointer hover:border-primary hover:bg-accent/50 transition-colors"
                    >
                      <Upload className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
                      <p className="text-sm">Click to upload mask image</p>
                    </div>
                  )}
                  <input
                    ref={maskInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleFileUpload("mask")}
                    className="hidden"
                  />
                </div>

                {/* Edit Prompt */}
                <div className="space-y-2">
                  <Label htmlFor="openai-prompt">Edit Prompt</Label>
                  <Textarea
                    id="openai-prompt"
                    placeholder="Describe how you want to edit the image..."
                    value={openaiForm.prompt}
                    onChange={(e) => setOpenaiForm((prev) => ({ ...prev, prompt: e.target.value }))}
                    rows={3}
                  />
                </div>

                <Button
                  onClick={handleOpenAIEdit}
                  disabled={!openaiForm.image || !openaiForm.mask || !openaiForm.prompt.trim() || isEditing}
                  className="w-full"
                >
                  {isEditing ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Editing...
                    </>
                  ) : (
                    <>
                      <Edit3 className="h-4 w-4 mr-2" />
                      Edit Image
                    </>
                  )}
                </Button>
              </div>
            </Card>

            {/* OpenAI Result */}
            <Card className="p-6">
              <div className="space-y-4">
                <Label className="text-base font-medium">Edited Result</Label>

                {openaiResult ? (
                  <div className="space-y-4">
                    <div className="aspect-video rounded-lg overflow-hidden bg-muted">
                      <img
                        src={openaiResult || "/placeholder.svg"}
                        alt="Edited image"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <Button onClick={() => handleUseInPreview(openaiResult, "openai")} className="w-full">
                      <Check className="h-4 w-4 mr-2" />
                      Use in Preview
                    </Button>
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Edit3 className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">Upload images and edit to see results here</p>
                  </div>
                )}
              </div>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Selected Image Preview */}
      {imaging.selectedImage && (
        <Card className="p-4 mt-6">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 rounded-lg overflow-hidden bg-muted">
              <img
                src={imaging.selectedImage || "/placeholder.svg"}
                alt="Selected for preview"
                className="w-full h-full object-cover"
              />
            </div>
            <div>
              <Label className="text-sm font-medium">Selected for Preview</Label>
              <p className="text-xs text-muted-foreground">This image will be used in the 3D preview</p>
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}
