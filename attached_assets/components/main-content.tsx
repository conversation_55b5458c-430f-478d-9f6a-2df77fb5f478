"use client"

import { useAppStore } from "@/lib/store"
import { ScanPanel } from "./panels/scan-panel"
import { ImagingPanel } from "./panels/imaging-panel"
import { PreviewPanel } from "./panels/preview-panel"
import { ExportPanel } from "./panels/export-panel"
import { BookingPanel } from "./panels/booking-panel"

export function MainContent() {
  const { currentStep } = useAppStore()

  const renderPanel = () => {
    switch (currentStep) {
      case "scan":
        return <ScanPanel />
      case "imaging":
        return <ImagingPanel />
      case "preview":
        return <PreviewPanel />
      case "export":
        return <ExportPanel />
      case "booking":
        return <BookingPanel />
      default:
        return <ScanPanel />
    }
  }

  return <main className="flex-1 overflow-hidden">{renderPanel()}</main>
}
