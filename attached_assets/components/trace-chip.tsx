"use client"

import { X } from "lucide-react"
import type { <PERSON><PERSON><PERSON> as Trace<PERSON>hipType } from "@/lib/types"

interface TraceChipProps {
  trace: TraceChipType
  onRemove: (id: string) => void
}

export function TraceChip({ trace, onRemove }: TraceChipProps) {
  return (
    <div className="inline-flex items-center gap-2 px-3 py-1 bg-muted rounded-full text-sm">
      <span className="text-muted-foreground">{trace.operation}</span>
      <span className="font-mono text-xs">{trace.traceId}</span>
      <button onClick={() => onRemove(trace.id)} className="text-muted-foreground hover:text-foreground">
        <X className="h-3 w-3" />
      </button>
    </div>
  )
}
