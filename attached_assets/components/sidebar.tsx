"use client"

import { useAppStore } from "@/lib/store"
import type { WorkflowStep, BodyPart } from "@/lib/types"
import { cn } from "@/lib/utils"
import { Camera, Palette, Eye, Download, Calendar, User, Hand, Zap } from "lucide-react"

const steps: { key: WorkflowStep; label: string; icon: any }[] = [
  { key: "scan", label: "Scan", icon: Camera },
  { key: "imaging", label: "Imaging", icon: Palette },
  { key: "preview", label: "Preview", icon: Eye },
  { key: "export", label: "Export", icon: Download },
  { key: "booking", label: "Booking", icon: Calendar },
]

const bodyParts: { key: BodyPart; label: string }[] = [
  { key: "forearm", label: "Forearm" },
  { key: "calf", label: "Calf" },
  { key: "torso", label: "Torso" },
  { key: "hand", label: "Hand" },
]

export function Sidebar() {
  const { currentStep, selectedBodyPart, setCurrentStep, setSelectedBodyPart } = useAppStore()

  return (
    <div className="w-64 bg-sidebar border-r border-sidebar-border flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-sidebar-border">
        <div className="flex items-center gap-2">
          <Zap className="h-6 w-6 text-sidebar-primary" />
          <h1 className="text-lg font-semibold text-sidebar-foreground">Tattoo Studio</h1>
        </div>
      </div>

      {/* Workflow Steps */}
      <div className="p-4">
        <h2 className="text-sm font-medium text-sidebar-foreground mb-3">Workflow</h2>
        <nav className="space-y-1">
          {steps.map((step) => {
            const Icon = step.icon
            const isActive = currentStep === step.key

            return (
              <button
                key={step.key}
                onClick={() => setCurrentStep(step.key)}
                className={cn(
                  "w-full flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors",
                  isActive
                    ? "bg-sidebar-primary text-sidebar-primary-foreground"
                    : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
                )}
              >
                <Icon className="h-4 w-4" />
                {step.label}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Body Part Selector */}
      <div className="p-4 border-t border-sidebar-border">
        <h2 className="text-sm font-medium text-sidebar-foreground mb-3">Body Part</h2>
        <div className="space-y-1">
          {bodyParts.map((part) => (
            <button
              key={part.key}
              onClick={() => setSelectedBodyPart(part.key)}
              className={cn(
                "w-full flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors",
                selectedBodyPart === part.key
                  ? "bg-sidebar-accent text-sidebar-accent-foreground"
                  : "text-sidebar-foreground hover:bg-sidebar-accent/50",
              )}
            >
              <Hand className="h-4 w-4" />
              {part.label}
            </button>
          ))}
        </div>
      </div>

      {/* Footer */}
      <div className="mt-auto p-4 border-t border-sidebar-border">
        <div className="flex items-center gap-2 text-xs text-sidebar-foreground/60">
          <User className="h-3 w-3" />
          <span>AI-Powered Design</span>
        </div>
      </div>
    </div>
  )
}
