Frontend Application Development: Tattoo Chatbot Management Panel
Objective: Develop a React-based frontend application for managing the Tattoo Chatbot, including user management, agent configuration, data review, and analytics. The application should be fully functional end-to-end using a mock API store.
Pages Required:
Login Page (/login): Authentication for admins. Allows access to the dashboard and admin functionality. Requires username/password input and displays login error messages.
Dashboard Page (/dashboard): Overview of key system metrics (number of chats, agent usage, active users). Displayed after successful login. Shows summarized data.
User Management (/users): List all users (artists, clients, admins) with details. Provides options to add, edit, and delete users. Includes pagination for large user lists.
Agent Management (/agents): List of all chatbot agents with their configurations. Allows editing of agent system prompts, available tools, and other settings.
Chat Logs (/logs): Displays conversation logs, filterable by date, user, and agent. Includes pagination.
Knowledge Base (/knowledge): Allows viewing, adding, editing, and deleting entries in the chatbot's knowledge base (e.g., aftercare instructions, style descriptions).
Settings (/settings): General system settings (e.g., theme selection, API endpoints).
User Roles and Permissions:
Admin: Full access to all pages and functionalities, including user management, agent configuration, data review, and analytics. Can add, edit, and delete users, agents and knowledge base entries.
Artist (if applicable in this context - adjust if not): Access to chat logs related to their conversations, limited knowledge base editing (e.g., adding FAQs based on their experiences). Can only view users and not edit or delete them.
Shared Components:
Navigation System: Sidebar navigation on the left-hand side of the screen, providing links to each page. Collapsible sidebar for smaller screens.
Header/Top Bar: Displays logged-in user information (username), a theme toggle (light/dark mode), and a logout button. Persistent across all pages.
Breadcrumbs: Used within pages that have a nested structure, example Knowledge Base -> Adding Entry
Modals/Popups:
Add/Edit User Modal: For creating or modifying user accounts. Includes input validation.
Add/Edit Agent Modal: For configuring chatbot agents.
Add/Edit Knowledge Base Entry Modal: For creating or modifying knowledge base content.
Confirmation Dialog: Used before deleting users, agents, or knowledge base entries.
Technical Requirements:
CSS Framework: Use Tailwind CSS for styling. Configure Tailwind with a custom theme to match the Tattoo Chatbot's branding.
Component Reusability: Prioritize the creation of reusable components (e.g., table, form input, button).
URL-Based Routing: Implement routing using react-router-dom for all pages.
API Data Handling: Create React Hooks (e.g., useUsers, useAgents, useLogs, useKnowledge) to fetch and manage data from the mock API store. Implement services (e.g., userService, agentService, logService, knowledgeService) to encapsulate API calls. Handle loading and error states gracefully.
Mock API Store: Develop a mock API store (e.g., using a simple JavaScript object or the json-server package) with realistic data, including unique IDs for all data entities (users, agents, logs, knowledge base entries).
End-to-End Functionality: Ensure the application is fully functional from login to data display and manipulation. Mock API should simulate create, read, update, and delete (CRUD) operations.
