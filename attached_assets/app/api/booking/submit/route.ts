import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const bookingData = await request.json()

    // Simulate CRM submission delay
    await new Promise((resolve) => setTimeout(resolve, 2000))

    const traceId = `booking_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Log booking data (in a real app, this would go to your CRM)
    console.log("[BOOKING SUBMISSION]", {
      traceId,
      name: bookingData.name,
      email: bookingData.email,
      bodyPart: bookingData.bodyPart,
      hasDesignFiles: !!(bookingData.selectedImage || bookingData.exportFiles.stencilUrl),
      timestamp: bookingData.timestamp,
    })

    // Emit webhook for booking
    fetch("/api/webhook", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        traceId,
        operation: "booking/submit",
        timestamp: Date.now(),
      }),
    }).catch(console.error)

    return NextResponse.json({
      traceId,
      success: true,
      message: "Booking submitted successfully",
    })
  } catch (error) {
    return NextResponse.json({ error: "Failed to submit booking" }, { status: 500 })
  }
}
