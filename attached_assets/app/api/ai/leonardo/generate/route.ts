import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const { prompt, style, seed, count } = await request.json()

    // Simulate processing delay
    await new Promise((resolve) => setTimeout(resolve, 3000))

    const traceId = `leonardo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Generate mock image URLs based on count
    const images = Array.from(
      { length: count },
      (_, i) => `/placeholder.svg?height=400&width=400&query=tattoo design ${style.toLowerCase()} style ${i + 1}`,
    )

    return NextResponse.json({
      traceId,
      images,
      prompt,
      style,
      seed,
      success: true,
    })
  } catch (error) {
    return NextResponse.json({ error: "Failed to generate images" }, { status: 500 })
  }
}
