import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const { traceId, operation, timestamp } = await request.json()

    // Log the webhook (in a real app, this would go to your logging service)
    console.log(`[WEBHOOK] ${operation} - ${traceId} at ${new Date(timestamp).toISOString()}`)

    return NextResponse.json({ success: true })
  } catch (error) {
    return NextResponse.json({ error: "Webhook processing failed" }, { status: 500 })
  }
}
