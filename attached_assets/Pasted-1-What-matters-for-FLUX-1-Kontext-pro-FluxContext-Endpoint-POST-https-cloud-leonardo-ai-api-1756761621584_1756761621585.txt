1) What matters for FLUX.1 Kontext [pro] (FluxContext)

Endpoint: POST https://cloud.leonardo.ai/api/rest/v1/generations

Auth: Authorization: Bearer <LEONARDO_API_KEY>

Hard constraints

num_images must be 1 (FLUX.1 Kontext only generates a single image).

Allowed sizes only:

Width/Height: 672, 720, 752, 832, 880, 944, 1024, 1104, 1184, 1248, 1392, 1456, 1568

Context images: contextImages: [{ type: "GENERATED"|"UPLOADED", id: "<uuid>" }]

Useful params

prompt (required)

modelId: 28aeddf8-bd19-4803-80fc-79602d1a9989 (FLUX.1 Kontext [pro])

styleUUID (preset style)

enhancePrompt: true|false (+ optional enhancePromptInstruction string)

seed (max 2147483638) for reproducibility

2) Best-practice defaults for a Tattoo Generator (your use-case)
A. Output strategy (two artifacts every time)

Artwork PNG (no background, high detail) for the 3-D overlay.

Stencil (hi-contrast monochrome + clean edges) for print/transfer.
→ Generate art via Flux, then create stencil via server post-process (threshold + edge-preserve) or an OpenAI gpt-image-1 edit with a mask that removes background and enforces black/white.

B. Prompts that actually work for tattoos

Drive style + placement + contrast up front, ban backgrounds, and enforce transparency:

SUBJECT: {black-and-grey biomechanical forearm tattoo}
STYLE: ultra-realistic skin-safe tattoo design, crisp linework, high micro-contrast,
PLACEMENT: {forearm, inner}, orientation {vertical}, margin 5–8% all sides
RENDER: flat artwork, NO background, NO border, NO frame, NO watermark
OUTPUT: transparent PNG look, clean edges ready for stencil conversion


Negative cues: “background, photo border, watermark, text, logo, frame, poster, paper texture”.

C. Sizes & aspect ratios (map to body parts)

Forearm/calf: 832×1248 (portrait) or 944×1392 for more detail (keeps perf reasonable).

Upper arm: 1024×1456.

Back/chest panel: 1248×1568 (max detail; heavier cost).
Always stick to allowed sizes (see list) and keep seed fixed for reproducible revisions.

D. Styles that behave well

For realism/stencil conversion:

Pro B&W photography → 22a9a7d2-...

Portrait → 8e2bc543-... (for realistic lighting reference)

Ray Traced → b504f83c-... (when you need stronger shading queues)

For flash/linework:

Graphic Design 2D → 703d6fe5-...

Illustration → 645e4195-...
(Use these styleUUIDs as starting points, adjust via prompt notches.)

E. Context image rules (for design continuity)

GENERATED with id of your last output → “do the same design but add X”.

UPLOADED with id of user sketch/reference → keep composition, re-render as tattoo.

F. EnhancePrompt

Start off while you dial your house prompt. Turn on later if you want extra descriptive padding.

For AI Edit mode: enhancePrompt: true + enhancePromptInstruction:
“Keep composition, push line clarity, tighten shading, remove backgrounds.”

G. Performance & cost control

Fix seed during revision cycles.

Cap size at 832×1248 for most previews; upscale only when exporting final.

Queue and poll jobs; don’t block the UI. Always create a traceId and emit a webhook on completion.

3) Request Templates (ready to paste)
A) Base Generation (FluxContext)
curl --request POST \
  --url https://cloud.leonardo.ai/api/rest/v1/generations \
  --header 'accept: application/json' \
  --header "authorization: Bearer $LEONARDO_API_KEY" \
  --header 'content-type: application/json' \
  --data '{
    "prompt": "ultra-realistic black-and-grey forearm tattoo of biomechanical tendons and plates; flat artwork, no background, clean edges, margin 6%; crisp linework, high micro-contrast; transparent PNG look; ready for stencil conversion",
    "modelId": "28aeddf8-bd19-4803-80fc-79602d1a9989",
    "styleUUID": "22a9a7d2-2166-4d86-80ff-22e2643adbcf",
    "num_images": 1,
    "width": 832,
    "height": 1248,
    "seed": 1234567,
    "enhancePrompt": false
  }'

B) Variation using a previous (GENERATED) image
--data '{
  "prompt": "add subtle cable bundle near wrist; keep composition, no background",
  "modelId": "28aeddf8-bd19-4803-80fc-79602d1a9989",
  "styleUUID": "22a9a7d2-2166-4d86-80ff-22e2643adbcf",
  "num_images": 1,
  "width": 832,
  "height": 1248,
  "contextImages": [{ "type":"GENERATED", "id":"<GEN_IMG_ID>" }],
  "seed": 1234567
}'

C) Variation from an uploaded sketch (UPLOADED)
--data '{
  "prompt": "refine into crisp linework with realistic shading; flat artwork; no background",
  "modelId": "28aeddf8-bd19-4803-80fc-79602d1a9989",
  "styleUUID": "703d6fe5-7f1c-4a9e-8da0-5331f214d5cf",
  "num_images": 1,
  "width": 832,
  "height": 1248,
  "contextImages": [{ "type":"UPLOADED", "id":"<UPLOAD_IMG_ID>" }],
  "seed": 987654
}'


For uploaded images, use Leonardo’s upload flow to get <UPLOAD_IMG_ID> first, then reference it in contextImages.

4) OpenAI gpt-image-1 (Edit / Inpaint) — post-process to stencil

Goal: turn Flux artwork into clean stencil & transparent PNG without backgrounds.

curl https://api.openai.com/v1/images/edits \
  -H "Authorization: Bearer $OPENAI_API_KEY" \
  -F model=gpt-image-1 \
  -F "image=@art.png" \
  -F "mask=@mask.png" \
  -F 'prompt=Convert to tattoo stencil: keep essential outlines and shading as high-contrast black on transparent background; remove all backgrounds; smooth gradations into solid fill with crosshatch where necessary; preserve edge fidelity; no text or watermark.' \
  -F size=1024x1024


Mask should be white where you want edits and black where you want to preserve (use all-white if you want full-frame processing).

For color palettes: run a server k-means/median-cut on art.png to generate swatches.

5) Backend Contracts (FastAPI) — paste into a README for Replit/Lovable/Base44
Env
LEONARDO_API_KEY=
OPENAI_API_KEY=

Routes
POST /api/flux/generate
BODY: { prompt, width, height, styleUUID?, contextImages?, seed? }
RETURNS: { generationId, imageId?, url?, traceId }

POST /api/flux/variation
BODY: { prompt, baseImage: {type:'GENERATED'|'UPLOADED', id}, width, height, styleUUID?, seed? }
RETURNS: { generationId, imageId?, url?, traceId }

POST /api/openai/stencil
BODY: { imageUrl|imageId, maskUrl?, mode:'stencil'|'clean-bg' }
RETURNS: { pngUrl, svgUrl?, palette }

GET  /api/traces/{traceId}
RETURNS: { timeline, model, params, usage }


Behavior

Generate a traceId at request start; attach to all logs/webhooks.

Size validation: reject any width/height not in the allowed set.

Force num_images: 1 for FluxContext calls.

Expose style presets in Settings; store defaults per body zone (forearm, calf, etc.).

6) Frontend UX spec (module)

Size dropdown locked to allowed values.

Style preset dropdown (map human labels → styleUUID).

Seed field (number) with “Lock seed” toggle.

Reference image section → upload or choose last generated (for GENERATED id).

Output panel shows:

“Artwork PNG” (transparent).

“Make Stencil” button → calls /api/openai/stencil.

“Copy Prompt” and “Re-run with seed” quick actions.

7) Prompt Pack (ready to paste into your app)
Base (Realism / Black & Grey)
{subject}, black-and-grey tattoo design for {bodyPart}, ultra-realistic texture and form, crisp linework, high micro-contrast, flat artwork, NO background, NO border, NO text, margins 6–8% all sides, transparent PNG look, ready for stencil conversion

Base (Neo-traditional color)
{subject}, neo-traditional tattoo design for {bodyPart}, bold outlines, controlled color palette, flat artwork on transparent background, no background, no posterization, margin 6%, clean edges for stencil

Edit (Refine existing)
Keep composition; tighten linework; deepen shadows; remove any background artifacts; enhance readability on skin; output clean edges, transparent background look.


Negative list (append):
(background, frame, watermark, poster, paper texture, border, text, logo)

8) Validation & Error Patterns (copy to your docs)

HTTP 400 if num_images != 1 for FluxContext.

HTTP 400 if width/height not in allowed set.

HTTP 422 if contextImages[].id missing when type provided.

HTTP 429/5xx: exponential backoff (250ms, 500ms, 1s, 2s, 4s; jitter).

Persist {prompt, params, seed, styleUUID, bodyPart} with traceId for reproducibility.

9) Drop-in code (tiny, production-safe patterns)
TypeScript server call (Node/fetch)
export async function fluxGenerate(req: {
  prompt: string; width: number; height: number;
  styleUUID?: string; contextImages?: {type:'GENERATED'|'UPLOADED'; id:string}[];
  seed?: number;
}) {
  const ALLOWED = [672,720,752,832,880,944,1024,1104,1184,1248,1392,1456,1568];
  if (!ALLOWED.includes(req.width) || !ALLOWED.includes(req.height)) {
    throw new Error('Invalid size for FLUX.1 Kontext');
  }
  const body = {
    prompt: req.prompt,
    modelId: '28aeddf8-bd19-4803-80fc-79602d1a9989',
    styleUUID: req.styleUUID,
    num_images: 1,
    width: req.width,
    height: req.height,
    contextImages: req.contextImages,
    seed: req.seed,
    enhancePrompt: false
  };
  const r = await fetch('https://cloud.leonardo.ai/api/rest/v1/generations', {
    method: 'POST',
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
      authorization: `Bearer ${process.env.LEONARDO_API_KEY!}`
    },
    body: JSON.stringify(body)
  });
  if (!r.ok) throw new Error(`Leonardo error ${r.status}`);
  return r.json();
}

Python stencil post-process (threshold + smooth) — fallback when you don’t want to burn OpenAI calls
from PIL import Image, ImageFilter, ImageOps
def to_stencil(png_path, out_path, thresh=180):
    im = Image.open(png_path).convert("L")
    im = im.filter(ImageFilter.MedianFilter(3))
    im = im.point(lambda p: 255 if p > thresh else 0, mode='1')
    im = im.convert('RGBA')
    # make white transparent
    datas = im.getdata()
    im.putdata([(r,g,b,0) if (r,g,b)==(255,255,255) else (0,0,0,255) for (r,g,b,a) in datas])
    im.save(out_path)

10) Builder Prompts (v0 / 21stDev) — copy-paste

Title: FluxContext Tattoo Generator Module

Build a Next.js + TS + Tailwind component that wraps Leonardo FLUX.1 Kontext [pro] with:

Size dropdown limited to: 672,720,752,832,880,944,1024,1104,1184,1248,1392,1456,1568.

Style preset dropdown (map labels → the UUIDs).

Seed field + “Lock seed”.

Reference image block supporting GENERATED and UPLOADED ids.

Buttons: Generate, Make Stencil (calls our /api/openai/stencil), Download PNG/SVG.

Validation: force num_images:1. Reject invalid sizes.

Show traceId chip on each result. Fire a webhook event image.generated with params.

Acceptance: can generate, vary from generated/uploaded, produce stencil, and export — all with allowed sizes and single-image rule.