# Tattoo AI Buddy – Realtime Orchestrator

*Aggressive, modular, production‑minded. Drop‑in orchestrator for realtime voice → intent → keywords → TGA handoff.*

---

## 1) High‑Level Overview

* **Purpose:** This module packages your realtime Chat/Voice orchestrator so you can plug it into any app (Next.js, React, or custom front‑end). It provides:

  * Realtime connect helpers (WebRTC) + Push‑to‑Talk flow
  * Intent detection + keyword extraction (function‑calling contract)
  * Strict JSON handoff to **TGA** (TattooGeneratorAgent) for `design` or `body_preview`
  * Minimal UI widgets for Mode/Model + PTT
  * Docs: **SPEC.md**, **PDR.md**, **MVP.md**

* **Key Principles:** ultra‑fast feedback, strict types, clean separation (UI ↔ Realtime ↔ Router ↔ TGA), safety on by default.

---

## 2) Folder Tree (portable package)

```
tab-realtime-orchestrator/
├─ README.md
├─ .env.example
├─ package.json
├─ tsconfig.json
├─ .gitignore
├─ docs/
│  ├─ SPEC.md
│  ├─ PDR.md
│  └─ MVP.md
└─ src/
   ├─ index.ts
   ├─ shared/
   │  └─ types.ts
   ├─ config/
   │  └─ systemInstructions.ts
   ├─ client/
   │  ├─ realtime.ts            # WebRTC connect + session.update
   │  ├─ pushToTalk.ts          # PTT event sequence (WS fallback)
   │  └─ tools.ts               # extract_keywords tool schema
   ├─ ui/
   │  ├─ ModeModelDropdown.tsx  # minimal UIKit widgets
   │  └─ PushToTalkButton.tsx
   └─ server/
      ├─ realtimeRouter.ts      # parse function_call → TGA handoff
      └─ next/
         └─ ephemeral.route.ts  # /api/realtime/ephemeral (Next.js App Router)
```

---

## 3) Quick Install & Use

1. **Install** (local or monorepo): `npm i @hardcore/tab-realtime-orchestrator`
2. Copy **server/next/** routes into your app (or adapt to Express/FastAPI).
3. Add UI widgets to your top bar and chat panel.
4. On connect, call `createRealtimePeerConnection()` & pass `TAB_SYSTEM_INSTRUCTIONS` and `EXTRACT_KEYWORDS_TOOL` via `session.update`.
5. When the model calls `extract_keywords`, validate → **POST** to `TGA_URL`.

**Env:** set `OPENAI_API_KEY`, `TGA_URL` (see `.env.example`).

---

## 4) System Instructions (ready to paste)

```
You are **Tattoo AI Buddy** – an upbeat, knowledgeable guide who:
• Detects user intent across {info, style_talk, design_preview, body_preview, free_chat}.
• Calls `extract_keywords` whenever design-related language appears.
• Replies in concise, tattoo-savvy tone; avoid filler, respect user’s comfort.
• Never mention internal function names or JSON; keep UI clean.
• Once keywords are confirmed, tell the user you’re “sending specs to Design Lab”.
• If asked for aftercare or artist history, answer directly without calling functions.
• If uncertain about details (e.g., body zone, color), ask clarifying questions.
• Avoid explicit gore; follow safety policies.
```

---

## 5) Handoff Contract → TGA

```
{
  "operation": "design | body_preview",
  "keywords": {
    "subject": "wolf",
    "theme": "forest",
    "style": "neo-traditional",
    "color_palette": "reds, blacks",
    "technique": "bold linework",
    "artist_refs": ["Sailor Jerry"],
    "body_zone": "full arm"
  },
  "model_choice": "flux"
}
```

---

## 6) Tech Spec (summary)

* **Model:** `gpt-4o-realtime-preview` (`modalities: ["audio","text"]`, voice `alloy`).
* **Session:** 30‑min cap; refresh at 28m. VAD on for hands‑free; PTT sets `turn_detection: null`.
* **Tool:** `extract_keywords` auto‑invoked on design chatter.
* **Validation:** Zod schemas enforce **strict JSON** before POST → **TGA**.
* **Rate Limits:** watch `rate_limits.updated`; pause mic if remaining tokens < threshold.
* **Security:** ephemeral token server‑side; never leak keys to client.

---

## 7) PDR (summary)

* **Goal:** blister‑fast talk‑to‑design with clean UX.
* **UX:** Mode + Model dropdown, chat feed, status pill, PTT.
* **Perf:** <700ms perceived turn in hands‑free mode.
* **Reliability:** reconnect, token refresh, retry TGA.

---

## 8) MVP (summary)

* **Phase 1:** WebRTC connect, VAD on, function‑call → TGA → render image\_url.
* **Phase 2:** Push‑to‑Talk, manual audio flow.
* **Phase 3:** Memory adapters (Supabase/Airtable).
* **Phase 4:** Prod hardening: rate limits, logs, analytics.

---

## 9) Integration Snippets

**WebRTC Connect**

```ts
const { pc, dataChannel } = await createRealtimePeerConnection({
  instructions: TAB_SYSTEM_INSTRUCTIONS,
  tools: [EXTRACT_KEYWORDS_TOOL],
  vad: true,
});
```

**Push‑to‑Talk (WS fallback)**

```ts
ws.send(JSON.stringify({ type: "input_audio_buffer.append", audio: b64 }));
ws.send(JSON.stringify({ type: "input_audio_buffer.commit" }));
ws.send(JSON.stringify({ type: "response.create" }));
ws.send(JSON.stringify({ type: "input_audio_buffer.clear" }));
```

**TGA Handoff (server)**

```ts
const payload = HandOffPayload.parse({ operation, keywords, model_choice });
await fetch(`${process.env.TGA_URL}/generate`, { method: "POST", body: JSON.stringify(payload) });
```

---

## 10) Ready‑to‑Ship Artifacts (included)

* **/docs/SPEC.md:** full technical spec
* **/docs/PDR.md:** product/design requirements
* **/docs/MVP.md:** rollout plan
* **Typed contracts & helpers** under `src/`

---

## 11) Next Iterations

* Add **BodyPreviewAgent** client and image‑on‑body compositor.
* Expand **artist\_refs** from Airtable lookup.
* Optional **RAG** injection for style guides and aftercare.

---

**Done.** This is your baseline orchestrator module—portable, typed, ruthless about structure. Plug it into Hardcore Tattoo Buddy, Hardcore Clothing, Hardcore BMX—same orchestration, different downstream agents.
