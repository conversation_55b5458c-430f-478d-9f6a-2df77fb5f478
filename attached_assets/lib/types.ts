export interface ScanState {
  photo: string | null
  depthPng: string | null
  maskPng: string | null
  uvPng: string | null
  normalPng: string | null
}

export interface ImagingState {
  selectedImage: string | null
  edits: EditResult[]
}

export interface EditResult {
  id: string
  type: "leonardo" | "openai"
  image: string
  prompt: string
  timestamp: number
}

export interface PreviewState {
  transform: {
    offset: { x: number; y: number; z: number }
    scale: { x: number; y: number; z: number }
    rotate: { x: number; y: number; z: number }
  }
  lighting: {
    ibl: boolean
    pathTrace: boolean
  }
}

export interface ExportState {
  stencilUrl: string | null
  svgUrl: string | null
  palette: string[] | null
}

export type BodyPart = "forearm" | "calf" | "torso" | "hand"
export type WorkflowStep = "scan" | "imaging" | "preview" | "export" | "booking"

export interface TraceChip {
  id: string
  traceId: string
  operation: string
  timestamp: number
}
