import { useAppStore } from "./store"

const API_BASE = "/api"

// Helper to add trace and emit webhook
const addTraceAndWebhook = (operation: string, traceId: string) => {
  const { addTrace } = useAppStore.getState()
  addTrace({ operation, traceId })

  // Emit webhook (stub)
  fetch("/api/webhook", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ traceId, operation, timestamp: Date.now() }),
  }).catch(console.error)
}

export const api = {
  scan: {
    ingest: async (photo: File) => {
      const formData = new FormData()
      formData.append("photo", photo)

      const response = await fetch(`${API_BASE}/scan/ingest`, {
        method: "POST",
        body: formData,
      })

      const result = await response.json()
      addTraceAndWebhook("scan/ingest", result.traceId)
      return result
    },

    unwrap: async (depthPng: string, maskPng: string) => {
      const response = await fetch(`${API_BASE}/scan/unwrap`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ depthPng, maskPng }),
      })

      const result = await response.json()
      addTraceAndWebhook("scan/unwrap", result.traceId)
      return result
    },
  },

  ai: {
    leonardo: {
      generate: async (prompt: string, style: string, seed: number, count: number) => {
        const response = await fetch(`${API_BASE}/ai/leonardo/generate`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ prompt, style, seed, count }),
        })

        const result = await response.json()
        addTraceAndWebhook("ai/leonardo/generate", result.traceId)
        return result
      },
    },

    openai: {
      edit: async (image: string, mask: string, prompt: string) => {
        const response = await fetch(`${API_BASE}/ai/openai/edit`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ image, mask, prompt }),
        })

        const result = await response.json()
        addTraceAndWebhook("ai/openai/edit", result.traceId)
        return result
      },
    },
  },

  preview: {
    bake: async (transform: any, lighting: any) => {
      const response = await fetch(`${API_BASE}/preview/bake`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ transform, lighting }),
      })

      const result = await response.json()
      addTraceAndWebhook("preview/bake", result.traceId)
      return result
    },
  },

  export: {
    stencil: async () => {
      const response = await fetch(`${API_BASE}/export/stencil`, {
        method: "POST",
      })

      const result = await response.json()
      addTraceAndWebhook("export/stencil", result.traceId)
      return result
    },

    svg: async () => {
      const response = await fetch(`${API_BASE}/export/svg`, {
        method: "POST",
      })

      const result = await response.json()
      addTraceAndWebhook("export/svg", result.traceId)
      return result
    },

    palette: async () => {
      const response = await fetch(`${API_BASE}/export/palette`, {
        method: "POST",
      })

      const result = await response.json()
      addTraceAndWebhook("export/palette", result.traceId)
      return result
    },

    createZip: async () => {
      const response = await fetch(`${API_BASE}/export/create-zip`, {
        method: "POST",
      })

      const result = await response.json()
      addTraceAndWebhook("export/create-zip", result.traceId)
      return result
    },
  },
}
