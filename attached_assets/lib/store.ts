import { create } from "zustand"
import type { ScanState, ImagingState, PreviewState, ExportState, BodyPart, WorkflowStep, TraceChip } from "./types"

interface AppState {
  // Current state
  currentStep: WorkflowStep
  selectedBodyPart: BodyPart

  // Feature states
  scan: ScanState
  imaging: ImagingState
  preview: PreviewState
  export: ExportState

  // Trace chips
  traces: TraceChip[]

  // Actions
  setCurrentStep: (step: WorkflowStep) => void
  setSelectedBodyPart: (part: BodyPart) => void

  // Scan actions
  setScanPhoto: (photo: string) => void
  setScanResults: (results: Partial<ScanState>) => void

  // Imaging actions
  setSelectedImage: (image: string) => void
  addEdit: (edit: any) => void

  // Preview actions
  updateTransform: (transform: Partial<PreviewState["transform"]>) => void
  updateLighting: (lighting: Partial<PreviewState["lighting"]>) => void

  // Export actions
  setExportResults: (results: Partial<ExportState>) => void

  // Trace actions
  addTrace: (trace: Omit<TraceChip, "id" | "timestamp">) => void
}

export const useAppStore = create<AppState>((set, get) => ({
  // Initial state
  currentStep: "scan",
  selectedBodyPart: "forearm",

  scan: {
    photo: null,
    depthPng: null,
    maskPng: null,
    uvPng: null,
    normalPng: null,
  },

  imaging: {
    selectedImage: null,
    edits: [],
  },

  preview: {
    transform: {
      offset: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 },
      rotate: { x: 0, y: 0, z: 0 },
    },
    lighting: {
      ibl: true,
      pathTrace: false,
    },
  },

  export: {
    stencilUrl: null,
    svgUrl: null,
    palette: null,
  },

  traces: [],

  // Actions
  setCurrentStep: (step) => set({ currentStep: step }),
  setSelectedBodyPart: (part) => set({ selectedBodyPart: part }),

  setScanPhoto: (photo) =>
    set((state) => ({
      scan: { ...state.scan, photo },
    })),

  setScanResults: (results) =>
    set((state) => ({
      scan: { ...state.scan, ...results },
    })),

  setSelectedImage: (image) =>
    set((state) => ({
      imaging: { ...state.imaging, selectedImage: image },
    })),

  addEdit: (edit) =>
    set((state) => ({
      imaging: {
        ...state.imaging,
        edits: [
          ...state.imaging.edits,
          {
            ...edit,
            id: Date.now().toString(),
            timestamp: Date.now(),
          },
        ],
      },
    })),

  updateTransform: (transform) =>
    set((state) => ({
      preview: {
        ...state.preview,
        transform: { ...state.preview.transform, ...transform },
      },
    })),

  updateLighting: (lighting) =>
    set((state) => ({
      preview: {
        ...state.preview,
        lighting: { ...state.preview.lighting, ...lighting },
      },
    })),

  setExportResults: (results) =>
    set((state) => ({
      export: { ...state.export, ...results },
    })),

  addTrace: (trace) =>
    set((state) => ({
      traces: [
        ...state.traces,
        {
          ...trace,
          id: Date.now().toString(),
          timestamp: Date.now(),
        },
      ],
    })),
}))
