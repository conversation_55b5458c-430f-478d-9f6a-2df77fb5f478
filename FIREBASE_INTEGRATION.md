# Firebase Integration Guide

## 🔥 Firebase + FastAPI Hybrid Authentication

Your TattooAdmin application now supports hybrid authentication using both Firebase and FastAPI backends, giving you the best of both worlds.

## 📋 Firebase Configuration

Your Firebase project is configured with these credentials:

```javascript
const firebaseConfig = {
  apiKey: "AIzaSyDeNiKkzEVduF8Pk1CxbAlBLR_knaewB4M",
  authDomain: "hardcore-tattoo-admin.firebaseapp.com",
  projectId: "hardcore-tattoo-admin",
  storageBucket: "hardcore-tattoo-admin.firebasestorage.app",
  messagingSenderId: "177472563492",
  appId: "1:177472563492:web:ff5152d26f18a19244ebb1",
  measurementId: "G-GS71H7K04F"
};
```

## 🔧 Authentication Strategies

The application supports three authentication strategies:

### 1. **Hybrid (Recommended)**
- Primary: Firebase Authentication
- Fallback: FastAPI Google OAuth
- Best reliability and feature set

### 2. **Firebase Only**
- Uses Firebase Authentication exclusively
- Direct integration with Google services
- Real-time user state management

### 3. **FastAPI Only**
- Uses FastAPI backend OAuth exclusively
- Custom JWT token management
- Full backend control

## 🚀 Setup Instructions

### 1. Environment Configuration

Your `.env` file is already configured with:

```bash
# Authentication Strategy
VITE_AUTH_STRATEGY=hybrid  # Options: firebase, fastapi, hybrid

# Firebase Configuration
FIREBASE_PROJECT_ID=hardcore-tattoo-admin
FIREBASE_API_KEY=AIzaSyDeNiKkzEVduF8Pk1CxbAlBLR_knaewB4M
# ... other Firebase config
```

### 2. Firebase Console Setup

1. **Go to [Firebase Console](https://console.firebase.google.com/)**
2. **Select your project**: `hardcore-tattoo-admin`
3. **Enable Authentication**:
   - Go to Authentication → Sign-in method
   - Enable Google provider
   - Add your domain to authorized domains

4. **Configure Authorized Domains**:
   - `localhost` (for development)
   - Your production domain

### 3. Google Cloud Console Setup

1. **Go to [Google Cloud Console](https://console.cloud.google.com/)**
2. **Select project**: `hardcore-tattoo-admin`
3. **APIs & Services → Credentials**
4. **Add authorized redirect URIs**:
   - `http://localhost:5173/auth/callback`
   - `https://yourdomain.com/auth/callback`

## 🔐 Authentication Flow

### Hybrid Strategy Flow

1. **User clicks "Continue with Google"**
2. **System tries Firebase Auth first**:
   ```javascript
   const user = await signInWithGoogle(); // Firebase
   ```
3. **If Firebase fails, fallback to FastAPI**:
   ```javascript
   const authUrl = await fastApiLoginWithGoogle(); // FastAPI
   window.location.href = authUrl;
   ```
4. **User data is normalized** to consistent format
5. **Analytics events are tracked** automatically

### Firebase-Only Flow

1. **Direct Firebase Authentication**
2. **Real-time auth state changes**
3. **Automatic token refresh**
4. **Integrated analytics**

### FastAPI-Only Flow

1. **OAuth redirect to Google**
2. **Backend token exchange**
3. **JWT token management**
4. **Manual token refresh**

## 📊 Firebase Services Available

### 1. **Authentication**
```javascript
import { signInWithGoogle, signOutUser, onAuthStateChange } from './lib/firebase';

// Sign in
const user = await signInWithGoogle();

// Listen to auth changes
const unsubscribe = onAuthStateChange((user) => {
  console.log('Auth state changed:', user);
});
```

### 2. **Firestore Database**
```javascript
import { db, collection, addDoc, getDocs } from './lib/firebase';

// Add document
await addDoc(collection(db, 'users'), {
  name: 'John Doe',
  email: '<EMAIL>'
});

// Get documents
const snapshot = await getDocs(collection(db, 'users'));
```

### 3. **Cloud Storage**
```javascript
import { storage, ref, uploadBytes, getDownloadURL } from './lib/firebase';

// Upload file
const storageRef = ref(storage, 'images/photo.jpg');
await uploadBytes(storageRef, file);
const url = await getDownloadURL(storageRef);
```

### 4. **Analytics**
```javascript
import { trackUserAction, trackPageView, trackError } from './lib/firebase';

// Track user actions
trackUserAction('button_click', { button: 'generate_design' });

// Track page views
trackPageView('Dashboard');

// Track errors
trackError('API call failed', 'design_generation');
```

## 🔄 Switching Authentication Strategies

### Change Strategy
Update your `.env` file:

```bash
# Use Firebase only
VITE_AUTH_STRATEGY=firebase

# Use FastAPI only
VITE_AUTH_STRATEGY=fastapi

# Use hybrid (recommended)
VITE_AUTH_STRATEGY=hybrid
```

### Strategy Comparison

| Feature | Firebase | FastAPI | Hybrid |
|---------|----------|---------|--------|
| Reliability | High | Medium | Highest |
| Real-time Auth | ✅ | ❌ | ✅ |
| Custom Backend | ❌ | ✅ | ✅ |
| Analytics | ✅ | ❌ | ✅ |
| Offline Support | ✅ | ❌ | ✅ |
| Token Management | Auto | Manual | Auto |

## 🧪 Testing the Integration

### 1. Test Firebase Authentication

```bash
# Start the application
npm run dev

# Open browser to http://localhost:5173
# Click "Continue with Google"
# Check browser console for Firebase logs
```

### 2. Test Analytics

```javascript
// Open browser console
// Check for analytics events:
console.log('Firebase Analytics initialized');
```

### 3. Test Firestore (Optional)

```javascript
// In browser console
import { db, collection, addDoc } from './lib/firebase';

await addDoc(collection(db, 'test'), {
  message: 'Hello Firebase!',
  timestamp: new Date()
});
```

## 🔧 Advanced Configuration

### Custom Claims (Firebase)

```javascript
// In Firebase Admin SDK (backend)
await admin.auth().setCustomUserClaims(uid, {
  role: 'admin',
  permissions: ['read', 'write', 'delete']
});
```

### Security Rules (Firestore)

```javascript
// firestore.rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    match /admin/{document} {
      allow read, write: if request.auth != null && 
        request.auth.token.role == 'admin';
    }
  }
}
```

### Storage Rules

```javascript
// storage.rules
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /users/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

## 📱 Mobile App Integration

The Firebase configuration is ready for mobile app integration:

### React Native
```javascript
import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
```

### Flutter
```dart
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';

await Firebase.initializeApp();
final auth = FirebaseAuth.instance;
```

## 🚀 Production Deployment

### Environment Variables for Production

```bash
VITE_AUTH_STRATEGY=hybrid
FIREBASE_PROJECT_ID=hardcore-tattoo-admin
# ... other Firebase config

# Update authorized domains in Firebase Console
# Update redirect URIs in Google Console
```

### Security Checklist

- [ ] Firebase Security Rules configured
- [ ] Authorized domains updated
- [ ] API keys restricted (optional)
- [ ] Analytics configured
- [ ] Backup strategy in place

## 📞 Troubleshooting

### Common Issues

1. **Firebase not initializing**:
   - Check API key and project ID
   - Verify domain is authorized

2. **Google Sign-in fails**:
   - Check redirect URIs
   - Verify OAuth consent screen

3. **Analytics not working**:
   - Check measurement ID
   - Verify domain in Firebase Console

### Debug Mode

Enable debug logging:

```javascript
// In browser console
localStorage.setItem('debug', 'firebase:*');
```

Your Firebase integration provides enterprise-grade authentication, real-time database, file storage, and analytics capabilities alongside your existing FastAPI backend.
