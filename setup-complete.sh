#!/bin/bash

# TattooAdmin Complete Setup Script
# Firebase + FastAPI + Google OAuth Integration

echo "🚀 TattooAdmin Complete Setup"
echo "=============================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if .env file exists
if [ ! -f .env ]; then
    print_error ".env file not found!"
    echo "Please make sure the .env file is in the root directory."
    exit 1
fi

print_status ".env file found"

# Load environment variables
export $(cat .env | grep -v '^#' | xargs)

# Check Node.js version
NODE_VERSION=$(node --version 2>/dev/null)
if [ $? -eq 0 ]; then
    print_status "Node.js version: $NODE_VERSION"
else
    print_error "Node.js not found. Please install Node.js 18+ first."
    exit 1
fi

# Check Python version
PYTHON_VERSION=$(python3 --version 2>/dev/null)
if [ $? -eq 0 ]; then
    print_status "Python version: $PYTHON_VERSION"
else
    print_error "Python3 not found. Please install Python 3.8+ first."
    exit 1
fi

echo ""
echo "🔧 Installing Dependencies"
echo "========================="

# Install Node.js dependencies
print_info "Installing Node.js dependencies..."
npm install
if [ $? -eq 0 ]; then
    print_status "Node.js dependencies installed"
else
    print_error "Failed to install Node.js dependencies"
    exit 1
fi

# Install Firebase
print_info "Installing Firebase..."
npm install firebase
if [ $? -eq 0 ]; then
    print_status "Firebase installed"
else
    print_error "Failed to install Firebase"
    exit 1
fi

# Setup FastAPI backend
echo ""
echo "🐍 Setting up FastAPI Backend"
echo "============================="

cd fastapi-backend

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    print_info "Creating Python virtual environment..."
    python3 -m venv venv
    print_status "Virtual environment created"
fi

# Activate virtual environment
print_info "Activating virtual environment..."
source venv/bin/activate

# Install Python dependencies
print_info "Installing Python dependencies..."
pip install -r requirements.txt
if [ $? -eq 0 ]; then
    print_status "Python dependencies installed"
else
    print_error "Failed to install Python dependencies"
    exit 1
fi

# Create necessary directories
print_info "Creating directories..."
mkdir -p generated/depth generated/mask generated/uv generated/normal generated/baked generated/stencil generated/svg generated/packages
mkdir -p logs uploads temp
print_status "Directories created"

cd ..

# Database setup (optional)
echo ""
echo "🗄️  Database Setup"
echo "=================="

if [ "$USE_DATABASE" = "true" ]; then
    print_info "Database mode enabled"
    
    # Check if PostgreSQL is available
    if command -v psql &> /dev/null; then
        print_status "PostgreSQL found"
        
        # Check if PostgreSQL is running
        if pg_isready -h localhost -p 5432 > /dev/null 2>&1; then
            print_status "PostgreSQL is running"
            
            # Try to create database (will fail silently if exists)
            createdb tattoo_db 2>/dev/null && print_status "Database 'tattoo_db' created" || print_info "Database 'tattoo_db' already exists"
        else
            print_warning "PostgreSQL is not running"
            print_info "To start PostgreSQL:"
            print_info "  macOS: brew services start postgresql"
            print_info "  Ubuntu: sudo systemctl start postgresql"
        fi
    else
        print_warning "PostgreSQL not found"
        print_info "Install PostgreSQL:"
        print_info "  macOS: brew install postgresql"
        print_info "  Ubuntu: sudo apt-get install postgresql postgresql-contrib"
    fi
else
    print_info "Using in-memory storage (development mode)"
fi

# Configuration validation
echo ""
echo "🔐 Configuration Validation"
echo "==========================="

# Check Google OAuth credentials
if [ -n "$GOOGLE_CLIENT_ID" ] && [ -n "$GOOGLE_CLIENT_SECRET" ]; then
    print_status "Google OAuth credentials configured"
else
    print_warning "Google OAuth credentials missing"
fi

# Check Firebase configuration
if [ -n "$FIREBASE_PROJECT_ID" ] && [ -n "$FIREBASE_API_KEY" ]; then
    print_status "Firebase configuration found"
else
    print_warning "Firebase configuration incomplete"
fi

# Check JWT secret
if [ -n "$JWT_SECRET" ] && [ ${#JWT_SECRET} -ge 32 ]; then
    print_status "JWT secret configured (${#JWT_SECRET} characters)"
else
    print_warning "JWT secret should be at least 32 characters long"
fi

echo ""
echo "🌐 Setup Summary"
echo "================"
echo ""
print_info "Frontend (React + Vite): http://localhost:5173"
print_info "Backend (FastAPI): http://localhost:8000"
print_info "API Documentation: http://localhost:8000/docs"
print_info "Authentication Strategy: ${VITE_AUTH_STRATEGY:-hybrid}"
echo ""

echo "🚀 Starting Services"
echo "===================="
echo ""

# Function to start services
start_services() {
    print_info "Starting FastAPI backend..."
    
    # Start FastAPI in background
    cd fastapi-backend
    source venv/bin/activate
    uvicorn main:app --host 0.0.0.0 --port 8000 --reload &
    FASTAPI_PID=$!
    cd ..
    
    sleep 3
    
    # Check if FastAPI started successfully
    if curl -s http://localhost:8000/health > /dev/null; then
        print_status "FastAPI backend started (PID: $FASTAPI_PID)"
    else
        print_error "Failed to start FastAPI backend"
        kill $FASTAPI_PID 2>/dev/null
        exit 1
    fi
    
    print_info "Starting React frontend..."
    
    # Start React frontend
    npm run dev &
    REACT_PID=$!
    
    sleep 5
    
    # Check if React started successfully
    if curl -s http://localhost:5173 > /dev/null; then
        print_status "React frontend started (PID: $REACT_PID)"
    else
        print_error "Failed to start React frontend"
        kill $FASTAPI_PID $REACT_PID 2>/dev/null
        exit 1
    fi
    
    echo ""
    print_status "🎉 Setup Complete!"
    echo ""
    echo "📱 Application URLs:"
    echo "   Frontend: http://localhost:5173"
    echo "   Backend:  http://localhost:8000"
    echo "   API Docs: http://localhost:8000/docs"
    echo ""
    echo "🔐 Authentication:"
    echo "   Strategy: ${VITE_AUTH_STRATEGY:-hybrid}"
    echo "   Google OAuth: Configured"
    echo "   Firebase: Configured"
    echo ""
    echo "🛑 To stop services:"
    echo "   kill $FASTAPI_PID $REACT_PID"
    echo ""
    echo "📚 Documentation:"
    echo "   Setup Guide: FASTAPI_SETUP.md"
    echo "   Firebase Guide: FIREBASE_INTEGRATION.md"
    echo "   Security Guide: SECURITY.md"
    echo ""
    
    # Wait for user input to stop
    read -p "Press Enter to stop all services..."
    
    print_info "Stopping services..."
    kill $FASTAPI_PID $REACT_PID 2>/dev/null
    print_status "Services stopped"
}

# Ask user if they want to start services
echo "Would you like to start the services now? (y/n)"
read -r response
if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    start_services
else
    echo ""
    print_status "Setup completed successfully!"
    echo ""
    echo "To start services manually:"
    echo "  Backend:  ./start-fastapi.sh"
    echo "  Frontend: npm run dev"
    echo ""
fi
