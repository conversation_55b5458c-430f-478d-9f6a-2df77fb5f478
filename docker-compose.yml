version: '3.8'

services:
  # Main TattooAdmin Application (Express + React)
  tattoo-admin:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: tattoo-admin-app
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=production
      - PORT=8000
      - HOST_URL=http://localhost:8000
      - USE_DATABASE=true
      - DATABASE_URL=******************************************************/tattoo_db
      - VITE_DEV_MODE=false
      - VITE_API_URL=http://localhost:8000
      # Google OAuth (set these in .env file)
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - GOOGLE_REDIRECT_URI=${GOOGLE_REDIRECT_URI:-http://localhost:8000/auth/callback}
      # Firebase
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_API_KEY=${FIREBASE_API_KEY}
      - FIREBASE_AUTH_DOMAIN=${FIREBASE_AUTH_DOMAIN}
      - FIREBASE_STORAGE_BUCKET=${FIREBASE_STORAGE_BUCKET}
      - FIREBASE_MESSAGING_SENDER_ID=${FIREBASE_MESSAGING_SENDER_ID}
      - FIREBASE_APP_ID=${FIREBASE_APP_ID}
      - FIREBASE_MEASUREMENT_ID=${FIREBASE_MEASUREMENT_ID}
      # API Keys
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LEONARDO_API_KEY=${LEONARDO_API_KEY}
      # Security
      - JWT_SECRET=${JWT_SECRET}
      - BCRYPT_ROUNDS=12
      # CORS
      - ALLOWED_ORIGINS=http://localhost:8000,http://localhost:3000
    volumes:
      - tattoo_uploads:/app/uploads
      - tattoo_generated:/app/generated
      - tattoo_logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - tattoo-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # FastAPI Backend (Optional - for advanced AI features)
  fastapi-backend:
    build:
      context: ./fastapi-backend
      dockerfile: Dockerfile
    container_name: tattoo-fastapi
    ports:
      - "8001:8001"
    environment:
      - FASTAPI_PORT=8001
      - FASTAPI_HOST=0.0.0.0
      - DATABASE_URL=******************************************************/tattoo_db
      # API Keys
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LEONARDO_API_KEY=${LEONARDO_API_KEY}
      # Google OAuth
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - GOOGLE_REDIRECT_URI=${GOOGLE_REDIRECT_URI:-http://localhost:8001/auth/google/callback}
      # Security
      - JWT_SECRET=${JWT_SECRET}
    volumes:
      - tattoo_uploads:/app/uploads
      - tattoo_generated:/app/generated
      - tattoo_logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - tattoo-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: tattoo-postgres
    environment:
      - POSTGRES_DB=tattoo_db
      - POSTGRES_USER=tattoo_user
      - POSTGRES_PASSWORD=tattoo_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - tattoo_postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    networks:
      - tattoo-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U tattoo_user -d tattoo_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache (Optional - for session storage and caching)
  redis:
    image: redis:7-alpine
    container_name: tattoo-redis
    ports:
      - "6379:6379"
    volumes:
      - tattoo_redis_data:/data
    networks:
      - tattoo-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-tattoo_redis_pass}

  # Nginx Reverse Proxy (Optional - for production)
  nginx:
    image: nginx:alpine
    container_name: tattoo-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - tattoo_logs:/var/log/nginx
    depends_on:
      - tattoo-admin
      - fastapi-backend
    networks:
      - tattoo-network
    restart: unless-stopped
    profiles:
      - production

# Named volumes for data persistence
volumes:
  tattoo_postgres_data:
    driver: local
  tattoo_redis_data:
    driver: local
  tattoo_uploads:
    driver: local
  tattoo_generated:
    driver: local
  tattoo_logs:
    driver: local

# Network for service communication
networks:
  tattoo-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
