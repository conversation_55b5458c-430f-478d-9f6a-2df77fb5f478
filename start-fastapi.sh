#!/bin/bash

# TattooAdmin FastAPI Startup Script
echo "🚀 Starting TattooAdmin FastAPI Backend..."

# Load environment variables
if [ -f .env ]; then
    echo "📋 Loading environment variables from .env"
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "⚠️  No .env file found, using default values"
fi

# Check if Python virtual environment exists
if [ ! -d "fastapi-backend/venv" ]; then
    echo "🐍 Creating Python virtual environment..."
    cd fastapi-backend
    python3 -m venv venv
    cd ..
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source fastapi-backend/venv/bin/activate

# Install dependencies
echo "📦 Installing Python dependencies..."
cd fastapi-backend
pip install -r requirements.txt

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p generated/depth generated/mask generated/uv generated/normal generated/baked generated/stencil generated/svg generated/packages
mkdir -p logs uploads temp

# Set up database (if using PostgreSQL)
if [ "$USE_DATABASE" = "true" ]; then
    echo "🗄️  Setting up database..."
    
    # Check if PostgreSQL is running
    if ! pg_isready -h localhost -p 5432 > /dev/null 2>&1; then
        echo "⚠️  PostgreSQL is not running. Please start PostgreSQL first."
        echo "   On macOS: brew services start postgresql"
        echo "   On Ubuntu: sudo systemctl start postgresql"
        exit 1
    fi
    
    # Create database if it doesn't exist
    createdb tattoo_db 2>/dev/null || echo "Database already exists"
fi

# Start FastAPI server
echo "🌟 Starting FastAPI server on port ${FASTAPI_PORT:-8001}..."
echo "🔗 API will be available at: http://localhost:${FASTAPI_PORT:-8001}"
echo "📚 API docs will be available at: http://localhost:${FASTAPI_PORT:-8001}/docs"
echo "🔐 Google OAuth login: http://localhost:${FASTAPI_PORT:-8001}/auth/google"

# Run with uvicorn
uvicorn main:app \
    --host ${FASTAPI_HOST:-0.0.0.0} \
    --port ${FASTAPI_PORT:-8001} \
    --reload \
    --log-level info
