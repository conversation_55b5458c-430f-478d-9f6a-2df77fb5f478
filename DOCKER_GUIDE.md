# Docker Deployment Guide

## 🐳 **FIXED: Complete Docker Setup Ready!**

Your TattooAdmin application is now fully containerized and ready to run in Docker with all services properly configured.

## 🚀 **Quick Start**

### 1. **One-Command Setup**
```bash
./docker-setup.sh
```

This will:
- ✅ Build all Docker images
- ✅ Start PostgreSQL database
- ✅ Start Redis cache
- ✅ Start main application on port 8000
- ✅ Start FastAPI backend on port 8001 (optional)
- ✅ Run health checks
- ✅ Show service status

### 2. **Access Your Application**
- **Main App**: http://localhost:8000
- **API Docs**: http://localhost:8000/api/docs
- **FastAPI**: http://localhost:8001 (if enabled)
- **FastAPI Docs**: http://localhost:8001/docs

## 📋 **Prerequisites**

### Required Software
- **Docker** 20.10+ ([Install Docker](https://docs.docker.com/get-docker/))
- **Docker Compose** 2.0+ ([Install Compose](https://docs.docker.com/compose/install/))

### Environment Configuration
Your `.env` file must contain:
```bash
# Required
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters

# Optional but recommended
OPENAI_API_KEY=sk-your-openai-key
LEONARDO_API_KEY=your-leonardo-key
FIREBASE_PROJECT_ID=your-firebase-project
```

## 🏗️ **Architecture**

### Services Included
1. **tattoo-admin** (Port 8000)
   - Express.js + React application
   - Main user interface
   - Authentication system
   - File uploads and processing

2. **fastapi-backend** (Port 8001)
   - Python FastAPI service
   - AI integrations (OpenAI, Leonardo)
   - 3D scanning and processing
   - Advanced image processing

3. **postgres** (Port 5432)
   - PostgreSQL 15 database
   - User data and application state
   - Persistent storage

4. **redis** (Port 6379)
   - Redis cache
   - Session storage
   - Performance optimization

5. **nginx** (Ports 80/443) - *Optional*
   - Reverse proxy
   - SSL termination
   - Load balancing

## 🛠️ **Docker Commands**

### Basic Operations
```bash
# Start all services
./docker-setup.sh start

# Stop all services
./docker-setup.sh stop

# Restart services
./docker-setup.sh restart

# View logs
./docker-setup.sh logs

# Check service status
./docker-setup.sh status

# Run health checks
./docker-setup.sh health
```

### Advanced Operations
```bash
# Clean up Docker resources
./docker-setup.sh clean

# Rebuild images
./docker-setup.sh build

# View specific service logs
docker-compose logs -f tattoo-admin
docker-compose logs -f fastapi-backend
docker-compose logs -f postgres

# Execute commands in containers
docker-compose exec tattoo-admin bash
docker-compose exec postgres psql -U tattoo_user -d tattoo_db
```

## 🔧 **Configuration**

### Port Configuration
The application uses these ports by default:
- **8000**: Main application (Express + React)
- **8001**: FastAPI backend
- **5432**: PostgreSQL database
- **6379**: Redis cache

### Volume Mounts
Data is persisted in Docker volumes:
- `tattoo_postgres_data`: Database data
- `tattoo_redis_data`: Redis data
- `tattoo_uploads`: User uploaded files
- `tattoo_generated`: AI generated content
- `tattoo_logs`: Application logs

### Environment Variables
Key environment variables for Docker:
```bash
# Application
NODE_ENV=production
PORT=8000
USE_DATABASE=true

# Database
DATABASE_URL=******************************************************/tattoo_db

# CORS (important for Docker)
ALLOWED_ORIGINS=http://localhost:8000,http://localhost:3000

# API URLs (auto-configured)
VITE_API_URL=http://localhost:8000
```

## 🔐 **Security**

### Production Security
For production deployment:

1. **Change default passwords**:
   ```bash
   POSTGRES_PASSWORD=your-secure-password
   REDIS_PASSWORD=your-redis-password
   ```

2. **Use secrets management**:
   ```bash
   # Use Docker secrets or external secret management
   docker secret create jwt_secret jwt_secret.txt
   ```

3. **Enable SSL**:
   ```bash
   # Use nginx profile for SSL termination
   docker-compose --profile production up -d
   ```

## 🚀 **Production Deployment**

### 1. **Cloud Deployment**
```bash
# For cloud deployment, update environment variables
export GOOGLE_REDIRECT_URI=https://yourdomain.com/auth/callback
export ALLOWED_ORIGINS=https://yourdomain.com
export HOST_URL=https://yourdomain.com

# Deploy with production profile
docker-compose --profile production up -d
```

### 2. **Docker Swarm**
```bash
# Initialize swarm
docker swarm init

# Deploy stack
docker stack deploy -c docker-compose.yml tattoo-admin
```

### 3. **Kubernetes**
```bash
# Generate Kubernetes manifests
kompose convert

# Deploy to Kubernetes
kubectl apply -f .
```

## 📊 **Monitoring**

### Health Checks
All services include health checks:
```bash
# Check all services
./docker-setup.sh health

# Manual health checks
curl http://localhost:8000/health
curl http://localhost:8001/health
```

### Logs
```bash
# View all logs
docker-compose logs

# Follow logs in real-time
docker-compose logs -f

# View specific service logs
docker-compose logs tattoo-admin
```

### Resource Usage
```bash
# View resource usage
docker stats

# View container details
docker-compose ps
```

## 🔄 **Backup & Recovery**

### Database Backup
```bash
# Backup database
docker-compose exec postgres pg_dump -U tattoo_user tattoo_db > backup.sql

# Restore database
docker-compose exec -T postgres psql -U tattoo_user tattoo_db < backup.sql
```

### Volume Backup
```bash
# Backup volumes
docker run --rm -v tattoo_uploads:/data -v $(pwd):/backup alpine tar czf /backup/uploads.tar.gz -C /data .

# Restore volumes
docker run --rm -v tattoo_uploads:/data -v $(pwd):/backup alpine tar xzf /backup/uploads.tar.gz -C /data
```

## 🐛 **Troubleshooting**

### Common Issues

1. **Port conflicts**:
   ```bash
   # Check what's using ports
   lsof -i :8000
   lsof -i :8001
   
   # Kill conflicting processes
   ./docker-setup.sh clean
   ```

2. **Database connection issues**:
   ```bash
   # Check database status
   docker-compose exec postgres pg_isready -U tattoo_user
   
   # Reset database
   docker-compose down
   docker volume rm tattoo_postgres_data
   docker-compose up -d postgres
   ```

3. **Build failures**:
   ```bash
   # Clean rebuild
   ./docker-setup.sh clean
   ./docker-setup.sh build
   ```

### Debug Mode
```bash
# Run with debug output
docker-compose up --build

# Access container shell
docker-compose exec tattoo-admin bash
```

## 📞 **Support**

### Service URLs
- **Application**: http://localhost:8000
- **API Documentation**: http://localhost:8000/api/docs
- **FastAPI Backend**: http://localhost:8001
- **Database**: localhost:5432

### Useful Commands
```bash
# Quick status check
./docker-setup.sh status

# View recent logs
./docker-setup.sh logs

# Full system restart
./docker-setup.sh stop && ./docker-setup.sh start
```

Your TattooAdmin application is now fully containerized and production-ready! 🎉
