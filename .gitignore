# Dependencies
node_modules/
*/node_modules/

# Python
__pycache__/
*.py[cod]
*$py.class
venv/
env/
fastapi-backend/venv/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
server/public

# Logs
logs/
*.log

# Secrets and keys
*.pem
*.key
.secrets.enc
.master.key
firebase-key.json
gcp-credentials.json

# Database
*.db
*.sqlite
*.sqlite3

# Generated files
generated/
uploads/
temp/

# OS generated files
.DS_Store
.DS_Store?
._*
Thumbs.db

# Editor
.vscode/
.idea/
*.swp
*.swo

# Misc
vite.config.ts.*
*.tar.gz
*.tgz
coverage/
.nyc_output/