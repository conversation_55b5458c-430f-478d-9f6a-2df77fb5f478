#!/usr/bin/env node

/**
 * Architecture Analysis Script
 * Identifies redundant implementations and suggests consolidation
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class ArchitectureAnalyzer {
  constructor() {
    this.findings = {
      duplicateEndpoints: [],
      redundantServices: [],
      inconsistentPatterns: [],
      recommendations: []
    };
  }

  analyze() {
    console.log('🔍 Analyzing TattooAdmin Architecture...\n');
    
    this.analyzeBackendDuplication();
    this.analyzeServicePatterns();
    this.generateRecommendations();
    
    this.printReport();
  }

  analyzeBackendDuplication() {
    console.log('📊 Analyzing Backend Duplication...');
    
    const expressRoutes = this.extractExpressRoutes();
    const fastApiRoutes = this.extractFastApiRoutes();
    
    // Find overlapping functionality
    const overlaps = this.findOverlappingRoutes(expressRoutes, fastApiRoutes);
    
    this.findings.duplicateEndpoints = overlaps;
    console.log(`   Found ${overlaps.length} overlapping endpoints\n`);
  }

  extractExpressRoutes() {
    const routesFile = path.join(__dirname, '../server/routes.ts');
    if (!fs.existsSync(routesFile)) return [];
    
    const content = fs.readFileSync(routesFile, 'utf8');
    const routes = [];
    
    // Extract route definitions
    const routeRegex = /app\.(get|post|put|delete|patch)\s*\(\s*["']([^"']+)["']/g;
    let match;
    
    while ((match = routeRegex.exec(content)) !== null) {
      routes.push({
        method: match[1].toUpperCase(),
        path: match[2],
        backend: 'Express'
      });
    }
    
    return routes;
  }

  extractFastApiRoutes() {
    const mainFile = path.join(__dirname, '../fastapi-backend/main.py');
    if (!fs.existsSync(mainFile)) return [];
    
    const content = fs.readFileSync(mainFile, 'utf8');
    const routes = [];
    
    // Extract FastAPI route definitions
    const routeRegex = /@app\.(get|post|put|delete|patch)\s*\(\s*["']([^"']+)["']/g;
    let match;
    
    while ((match = routeRegex.exec(content)) !== null) {
      routes.push({
        method: match[1].toUpperCase(),
        path: match[2],
        backend: 'FastAPI'
      });
    }
    
    return routes;
  }

  findOverlappingRoutes(expressRoutes, fastApiRoutes) {
    const overlaps = [];
    
    for (const expressRoute of expressRoutes) {
      for (const fastApiRoute of fastApiRoutes) {
        if (this.routesOverlap(expressRoute, fastApiRoute)) {
          overlaps.push({
            path: expressRoute.path,
            method: expressRoute.method,
            backends: ['Express', 'FastAPI'],
            similarity: this.calculateSimilarity(expressRoute.path, fastApiRoute.path)
          });
        }
      }
    }
    
    return overlaps;
  }

  routesOverlap(route1, route2) {
    // Exact match
    if (route1.method === route2.method && route1.path === route2.path) {
      return true;
    }
    
    // Similar functionality (e.g., /api/users vs /api/v2/users)
    const similarity = this.calculateSimilarity(route1.path, route2.path);
    return similarity > 0.7 && route1.method === route2.method;
  }

  calculateSimilarity(str1, str2) {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  levenshteinDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  analyzeServicePatterns() {
    console.log('🔧 Analyzing Service Patterns...');
    
    const patterns = {
      authPatterns: this.analyzeAuthPatterns(),
      storagePatterns: this.analyzeStoragePatterns(),
      errorHandling: this.analyzeErrorHandling()
    };
    
    this.findings.inconsistentPatterns = patterns;
    console.log('   Service pattern analysis complete\n');
  }

  analyzeAuthPatterns() {
    return {
      express: 'JWT with custom middleware',
      fastapi: 'Firebase Auth',
      client: 'Development mode bypass',
      recommendation: 'Consolidate to single JWT-based system'
    };
  }

  analyzeStoragePatterns() {
    return {
      express: 'In-memory storage (MemStorage)',
      fastapi: 'Database models with ORM',
      recommendation: 'Migrate to persistent database storage'
    };
  }

  analyzeErrorHandling() {
    return {
      consistency: 'Inconsistent error response formats',
      recommendation: 'Standardize error response structure'
    };
  }

  generateRecommendations() {
    console.log('💡 Generating Recommendations...');
    
    this.findings.recommendations = [
      {
        priority: 'HIGH',
        category: 'Architecture',
        title: 'Consolidate Backend Implementations',
        description: 'Choose either Express.js or FastAPI as the primary backend',
        impact: 'Reduces maintenance overhead and complexity',
        effort: 'High'
      },
      {
        priority: 'HIGH',
        category: 'Data Persistence',
        title: 'Replace Memory Storage',
        description: 'Implement persistent database storage with proper migrations',
        impact: 'Enables data persistence and scalability',
        effort: 'Medium'
      },
      {
        priority: 'MEDIUM',
        category: 'Authentication',
        title: 'Unify Authentication System',
        description: 'Standardize on JWT-based authentication across all services',
        impact: 'Improves security and consistency',
        effort: 'Medium'
      },
      {
        priority: 'MEDIUM',
        category: 'Error Handling',
        title: 'Standardize Error Responses',
        description: 'Implement consistent error response format',
        impact: 'Improves API usability and debugging',
        effort: 'Low'
      },
      {
        priority: 'LOW',
        category: 'Code Quality',
        title: 'Remove Unused Dependencies',
        description: 'Clean up package.json and remove redundant imports',
        impact: 'Reduces bundle size and security surface',
        effort: 'Low'
      }
    ];
    
    console.log(`   Generated ${this.findings.recommendations.length} recommendations\n`);
  }

  printReport() {
    console.log('📋 ARCHITECTURE ANALYSIS REPORT');
    console.log('================================\n');
    
    // Duplicate Endpoints
    console.log('🔄 DUPLICATE ENDPOINTS');
    console.log('-----------------------');
    if (this.findings.duplicateEndpoints.length === 0) {
      console.log('✅ No duplicate endpoints found\n');
    } else {
      this.findings.duplicateEndpoints.forEach(endpoint => {
        console.log(`❌ ${endpoint.method} ${endpoint.path}`);
        console.log(`   Backends: ${endpoint.backends.join(', ')}`);
        console.log(`   Similarity: ${(endpoint.similarity * 100).toFixed(1)}%\n`);
      });
    }
    
    // Service Patterns
    console.log('🔧 SERVICE PATTERNS');
    console.log('-------------------');
    Object.entries(this.findings.inconsistentPatterns).forEach(([category, pattern]) => {
      if (typeof pattern === 'object' && pattern.recommendation) {
        console.log(`📌 ${category.toUpperCase()}`);
        Object.entries(pattern).forEach(([key, value]) => {
          if (key !== 'recommendation') {
            console.log(`   ${key}: ${value}`);
          }
        });
        console.log(`   💡 ${pattern.recommendation}\n`);
      }
    });
    
    // Recommendations
    console.log('💡 RECOMMENDATIONS');
    console.log('------------------');
    this.findings.recommendations
      .sort((a, b) => {
        const priorityOrder = { HIGH: 3, MEDIUM: 2, LOW: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      })
      .forEach((rec, index) => {
        const priorityEmoji = rec.priority === 'HIGH' ? '🔴' : rec.priority === 'MEDIUM' ? '🟡' : '🟢';
        console.log(`${index + 1}. ${priorityEmoji} ${rec.title} (${rec.priority})`);
        console.log(`   Category: ${rec.category}`);
        console.log(`   Description: ${rec.description}`);
        console.log(`   Impact: ${rec.impact}`);
        console.log(`   Effort: ${rec.effort}\n`);
      });
    
    // Summary
    console.log('📊 SUMMARY');
    console.log('----------');
    console.log(`Duplicate Endpoints: ${this.findings.duplicateEndpoints.length}`);
    console.log(`Inconsistent Patterns: ${Object.keys(this.findings.inconsistentPatterns).length}`);
    console.log(`Recommendations: ${this.findings.recommendations.length}`);
    console.log(`High Priority Items: ${this.findings.recommendations.filter(r => r.priority === 'HIGH').length}\n`);
    
    console.log('🎯 NEXT STEPS');
    console.log('-------------');
    console.log('1. Review high-priority recommendations');
    console.log('2. Choose primary backend (Express.js recommended)');
    console.log('3. Implement database persistence');
    console.log('4. Standardize authentication system');
    console.log('5. Clean up redundant code\n');
  }
}

// Run analysis
if (import.meta.url === `file://${process.argv[1]}`) {
  const analyzer = new ArchitectureAnalyzer();
  analyzer.analyze();
}

export default ArchitectureAnalyzer;
