# Multi-stage Docker build for TattooAdmin
# Stage 1: Build the application
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Stage 2: Production runtime
FROM node:18-alpine AS runtime

# Install system dependencies
RUN apk add --no-cache \
    python3 \
    py3-pip \
    curl \
    && rm -rf /var/cache/apk/*

# Create app user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S tattoo -u 1001

# Set working directory
WORKDIR /app

# Copy built application from builder stage
COPY --from=builder --chown=tattoo:nodejs /app/dist ./dist
COPY --from=builder --chown=tattoo:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=tattoo:nodejs /app/package*.json ./
COPY --from=builder --chown=tattoo:nodejs /app/server ./server

# Install ts-node for TypeScript execution
RUN npm install -g ts-node

# Create necessary directories
RUN mkdir -p logs uploads temp generated/depth generated/mask generated/uv generated/normal generated/baked generated/stencil generated/svg generated/packages \
    && chown -R tattoo:nodejs logs uploads temp generated

# Switch to non-root user
USER tattoo

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Start the application
CMD ["npm", "start"]
