# TattooAdmin Environment Configuration
# Copy this file to .env and fill in your actual values
# NEVER commit .env files to version control

# Application Environment
NODE_ENV=development
PORT=5000
HOST_URL=http://localhost:5000

# Security Configuration
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters-long
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=12

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/tattoo_admin

# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here

# Leonardo AI Configuration (Optional)
LEONARDO_API_KEY=your-leonardo-api-key-here

# Firebase Configuration (Optional)
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_CONFIG={"type":"service_account","project_id":"your-project"}
FIREBASE_KEY_PATH=./firebase-key.json

# Google OAuth Configuration (REQUIRED)
GOOGLE_CLIENT_ID=your-google-client-id-from-console
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8000/auth/callback

# Firebase Configuration (REQUIRED)
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_API_KEY=your-firebase-api-key
FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
FIREBASE_STORAGE_BUCKET=your-project.firebasestorage.app
FIREBASE_MESSAGING_SENDER_ID=your-sender-id
FIREBASE_APP_ID=your-app-id
FIREBASE_MEASUREMENT_ID=your-measurement-id

# Google Cloud Platform Configuration (Optional)
GCP_STORAGE_BUCKET=your-gcp-bucket-name
GOOGLE_APPLICATION_CREDENTIALS=./gcp-credentials.json

# Redis Configuration (Optional)
REDIS_URL=redis://localhost:6379

# File Upload Configuration
MAX_FILE_SIZE=********
UPLOAD_DIR=./uploads
TEMP_DIR=./temp

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX=5
AUTH_RATE_LIMIT_WINDOW_MS=900000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Development Mode Settings
VITE_DEV_MODE=false

# Session Configuration
SESSION_SECRET=your-session-secret-minimum-32-characters-long
SESSION_MAX_AGE=86400000

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Webhook Configuration (Optional)
WEBHOOK_SECRET=your-webhook-secret-key

# Monitoring Configuration (Optional)
SENTRY_DSN=your-sentry-dsn-here
ANALYTICS_ID=your-analytics-id

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_PASSWORD_RESET=true
ENABLE_EMAIL_VERIFICATION=false
ENABLE_2FA=false

# API Rate Limits
API_RATE_LIMIT_REQUESTS_PER_MINUTE=60
API_RATE_LIMIT_BURST=10

# Security Headers
ENABLE_HELMET=true
ENABLE_CSRF_PROTECTION=true
ENABLE_CONTENT_SECURITY_POLICY=true

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
