# Client Environment Variables for Vite
# These variables are prefixed with VITE_ to be accessible in the browser

# API Configuration - Use same origin in production, localhost:8000 in development
VITE_API_URL=http://localhost:8000

# Authentication Strategy
VITE_AUTH_STRATEGY=hybrid

# Development Mode
VITE_DEV_MODE=true

# Firebase Configuration (automatically loaded from firebase.ts)
# These are public configuration values, safe to expose in client
VITE_FIREBASE_API_KEY=AIzaSyDeNiKkzEVduF8Pk1CxbAlBLR_knaewB4M
VITE_FIREBASE_AUTH_DOMAIN=hardcore-tattoo-admin.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=hardcore-tattoo-admin
VITE_FIREBASE_STORAGE_BUCKET=hardcore-tattoo-admin.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=177472563492
VITE_FIREBASE_APP_ID=1:177472563492:web:ff5152d26f18a19244ebb1
VITE_FIREBASE_MEASUREMENT_ID=G-GS71H7K04F

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_OFFLINE_SUPPORT=true
VITE_ENABLE_PWA=false

# Debug Settings
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info
