import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Wand2, Palette, Download, Copy, RefreshCw, Loader2, Image as ImageIcon, Zap } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

interface FluxGeneration {
  generation_id: string;
  trace_id: string;
  status: string;
  image_url?: string;
  image_id?: string;
}

interface StylePreset {
  id: string;
  name: string;
  description: string;
  category: string;
}

interface BodyPartSize {
  width: number;
  height: number;
}

export default function FluxGenerator() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Form state
  const [subject, setSubject] = useState('');
  const [bodyPart, setBodyPart] = useState('forearm');
  const [style, setStyle] = useState('realistic_bw');
  const [additionalInstructions, setAdditionalInstructions] = useState('');
  const [seed, setSeed] = useState<number | null>(null);
  const [lockSeed, setLockSeed] = useState(false);
  const [enhancePrompt, setEnhancePrompt] = useState(false);
  
  // Generation state
  const [currentGeneration, setCurrentGeneration] = useState<FluxGeneration | null>(null);
  const [generationHistory, setGenerationHistory] = useState<FluxGeneration[]>([]);
  const [selectedReference, setSelectedReference] = useState<string | null>(null);
  const [variationInstructions, setVariationInstructions] = useState('');

  // Fetch style presets
  const { data: stylePresets } = useQuery({
    queryKey: ['/api/leonardo/styles'],
    queryFn: async () => {
      const response = await fetch('/api/leonardo/styles');
      return response.json();
    }
  });

  // Fetch body part sizes
  const { data: bodyPartSizes } = useQuery({
    queryKey: ['/api/leonardo/body-parts'],
    queryFn: async () => {
      const response = await fetch('/api/leonardo/body-parts');
      return response.json();
    }
  });

  // Generate tattoo mutation
  const generateMutation = useMutation({
    mutationFn: async (params: any) => {
      const response = await fetch('/api/leonardo/flux/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params)
      });
      return response.json();
    },
    onSuccess: (data) => {
      if (data.success) {
        const generation: FluxGeneration = {
          generation_id: data.generation_id,
          trace_id: data.trace_id,
          status: 'processing'
        };
        setCurrentGeneration(generation);
        pollGeneration(data.generation_id);
        toast({
          title: "Generation Started",
          description: `FLUX.1 Kontext is creating your ${bodyPart} tattoo design`
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Generation Failed",
        description: error.message || "Failed to start generation",
        variant: "destructive"
      });
    }
  });

  // Create variation mutation
  const variationMutation = useMutation({
    mutationFn: async (params: any) => {
      const response = await fetch('/api/leonardo/flux/variation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params)
      });
      return response.json();
    },
    onSuccess: (data) => {
      if (data.success) {
        const generation: FluxGeneration = {
          generation_id: data.generation_id,
          trace_id: data.trace_id,
          status: 'processing'
        };
        setCurrentGeneration(generation);
        pollGeneration(data.generation_id);
        toast({
          title: "Variation Started",
          description: "Creating design variation with FLUX.1 Kontext"
        });
      }
    }
  });

  // Create stencil mutation
  const stencilMutation = useMutation({
    mutationFn: async (imageUrl: string) => {
      const response = await fetch('/api/openai/stencil', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          image_url: imageUrl,
          mode: 'stencil'
        })
      });
      return response.json();
    },
    onSuccess: (data) => {
      if (data.success) {
        toast({
          title: "Stencil Created",
          description: "Professional stencil ready for download"
        });
      }
    }
  });

  // Poll generation status
  const pollGeneration = async (generationId: string) => {
    const maxAttempts = 60; // 5 minutes max
    let attempts = 0;

    const poll = async () => {
      try {
        const response = await fetch(`/api/leonardo/generation/${generationId}`);
        const data = await response.json();

        if (data.success && data.status.generations_by_pk) {
          const generation = data.status.generations_by_pk;
          
          if (generation.status === 'COMPLETE' && generation.generated_images?.length > 0) {
            const image = generation.generated_images[0];
            const completedGeneration: FluxGeneration = {
              generation_id: generationId,
              trace_id: currentGeneration?.trace_id || '',
              status: 'complete',
              image_url: image.url,
              image_id: image.id
            };
            
            setCurrentGeneration(completedGeneration);
            setGenerationHistory(prev => [completedGeneration, ...prev.slice(0, 9)]); // Keep last 10
            
            toast({
              title: "Generation Complete",
              description: "Your FLUX tattoo design is ready!"
            });
            return;
          }
          
          if (generation.status === 'FAILED') {
            setCurrentGeneration(prev => prev ? { ...prev, status: 'failed' } : null);
            toast({
              title: "Generation Failed",
              description: "FLUX generation encountered an error",
              variant: "destructive"
            });
            return;
          }
        }

        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // Poll every 5 seconds
        } else {
          toast({
            title: "Generation Timeout",
            description: "Generation is taking longer than expected",
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error('Polling error:', error);
        setTimeout(poll, 5000);
      }
    };

    setTimeout(poll, 2000); // Start polling after 2 seconds
  };

  const handleGenerate = () => {
    if (!subject.trim()) {
      toast({
        title: "Missing Subject",
        description: "Please describe your tattoo design",
        variant: "destructive"
      });
      return;
    }

    const params = {
      subject: subject.trim(),
      body_part: bodyPart,
      style,
      additional_instructions: additionalInstructions.trim(),
      enhance_prompt: enhancePrompt,
      ...(lockSeed && seed && { seed })
    };

    generateMutation.mutate(params);
  };

  const handleCreateVariation = () => {
    if (!selectedReference || !currentGeneration?.image_id) {
      toast({
        title: "No Reference Selected",
        description: "Select a previous generation to create variation",
        variant: "destructive"
      });
      return;
    }

    const params = {
      base_prompt: subject,
      base_image_id: currentGeneration.image_id,
      image_type: 'GENERATED',
      body_part: bodyPart,
      style,
      variation_instructions: variationInstructions,
      ...(lockSeed && seed && { seed })
    };

    variationMutation.mutate(params);
  };

  const handleCreateStencil = () => {
    if (!currentGeneration?.image_url) {
      toast({
        title: "No Image Available",
        description: "Generate a design first to create stencil",
        variant: "destructive"
      });
      return;
    }

    stencilMutation.mutate(currentGeneration.image_url);
  };

  const copyPrompt = () => {
    navigator.clipboard.writeText(subject);
    toast({
      title: "Prompt Copied",
      description: "Design prompt copied to clipboard"
    });
  };

  const generateRandomSeed = () => {
    setSeed(Math.floor(Math.random() * 2147483638));
  };

  // Get current body part dimensions
  const currentDimensions = bodyPartSizes?.success 
    ? bodyPartSizes.body_parts[bodyPart] 
    : { width: 832, height: 1248 };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2 flex items-center gap-2">
          <Zap className="h-8 w-8 text-yellow-500" />
          FLUX.1 Kontext Tattoo Generator
        </h1>
        <p className="text-muted-foreground">
          Professional tattoo design with contextual continuity and 3D try-on integration
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Generation Controls */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Design Specification</CardTitle>
              <CardDescription>
                Describe your tattoo design with FLUX.1 Kontext precision
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="subject">Tattoo Subject</Label>
                <Textarea
                  id="subject"
                  placeholder="biomechanical forearm tattoo with intricate gears and cables..."
                  value={subject}
                  onChange={(e) => setSubject(e.target.value)}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="bodyPart">Body Part</Label>
                  <Select value={bodyPart} onValueChange={setBodyPart}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="forearm">Forearm</SelectItem>
                      <SelectItem value="calf">Calf</SelectItem>
                      <SelectItem value="upper_arm">Upper Arm</SelectItem>
                      <SelectItem value="hand">Hand</SelectItem>
                      <SelectItem value="torso">Torso</SelectItem>
                      <SelectItem value="back">Back</SelectItem>
                      <SelectItem value="chest">Chest</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    {currentDimensions?.width}×{currentDimensions?.height}px
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="style">Style Preset</Label>
                  <Select value={style} onValueChange={setStyle}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {stylePresets?.success && Object.entries(stylePresets.styles).map(([key, preset]: [string, any]) => (
                        <SelectItem key={key} value={key}>
                          {preset.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="instructions">Additional Instructions</Label>
                <Input
                  id="instructions"
                  placeholder="add subtle shading, enhance contrast..."
                  value={additionalInstructions}
                  onChange={(e) => setAdditionalInstructions(e.target.value)}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="seed">Seed</Label>
                  <div className="flex gap-2">
                    <Input
                      id="seed"
                      type="number"
                      placeholder="Optional"
                      value={seed || ''}
                      onChange={(e) => setSeed(e.target.value ? parseInt(e.target.value) : null)}
                      disabled={!lockSeed}
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={generateRandomSeed}
                      disabled={!lockSeed}
                    >
                      <RefreshCw className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Options</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="lockSeed"
                        checked={lockSeed}
                        onCheckedChange={setLockSeed}
                      />
                      <Label htmlFor="lockSeed" className="text-sm">Lock Seed</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="enhancePrompt"
                        checked={enhancePrompt}
                        onCheckedChange={setEnhancePrompt}
                      />
                      <Label htmlFor="enhancePrompt" className="text-sm">Enhance Prompt</Label>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex gap-2">
                <Button 
                  onClick={handleGenerate}
                  disabled={generateMutation.isPending || !subject.trim()}
                  className="flex-1"
                >
                  {generateMutation.isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Wand2 className="h-4 w-4 mr-2" />
                      Generate Design
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={copyPrompt}
                  disabled={!subject.trim()}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Variation Controls */}
          {generationHistory.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Create Variation</CardTitle>
                <CardDescription>
                  Refine previous designs with contextual modifications
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Reference Image</Label>
                  <div className="grid grid-cols-3 gap-2">
                    {generationHistory.slice(0, 6).map((gen, index) => (
                      <div
                        key={gen.generation_id}
                        className={`relative cursor-pointer border-2 rounded-lg overflow-hidden aspect-[4/5] ${
                          selectedReference === gen.generation_id
                            ? 'border-primary'
                            : 'border-muted'
                        }`}
                        onClick={() => setSelectedReference(gen.generation_id)}
                      >
                        {gen.image_url ? (
                          <img
                            src={gen.image_url}
                            alt={`Generation ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-muted flex items-center justify-center">
                            <ImageIcon className="h-8 w-8 text-muted-foreground" />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="variations">Variation Instructions</Label>
                  <Input
                    id="variations"
                    placeholder="add more detail to the gears, enhance shading..."
                    value={variationInstructions}
                    onChange={(e) => setVariationInstructions(e.target.value)}
                  />
                </div>

                <Button
                  onClick={handleCreateVariation}
                  disabled={variationMutation.isPending || !selectedReference}
                  className="w-full"
                >
                  {variationMutation.isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Creating Variation...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Create Variation
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Results Display */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Generated Design</CardTitle>
              <CardDescription>
                FLUX.1 Kontext professional tattoo artwork
              </CardDescription>
            </CardHeader>
            <CardContent>
              {currentGeneration ? (
                <div className="space-y-4">
                  {currentGeneration.status === 'processing' && (
                    <div className="flex items-center justify-center p-8 border border-dashed rounded-lg">
                      <div className="text-center">
                        <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4 text-yellow-500" />
                        <p className="font-medium">FLUX.1 Kontext is generating...</p>
                        <p className="text-sm text-muted-foreground">
                          This may take 30-60 seconds
                        </p>
                      </div>
                    </div>
                  )}

                  {currentGeneration.status === 'complete' && currentGeneration.image_url && (
                    <div className="space-y-4">
                      <div className="relative">
                        <img
                          src={currentGeneration.image_url}
                          alt="Generated tattoo design"
                          className="w-full rounded-lg border"
                        />
                        <Badge 
                          variant="secondary" 
                          className="absolute top-2 right-2"
                        >
                          FLUX.1 Kontext
                        </Badge>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          onClick={handleCreateStencil}
                          disabled={stencilMutation.isPending}
                          variant="outline"
                          className="flex-1"
                        >
                          {stencilMutation.isPending ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Creating...
                            </>
                          ) : (
                            <>
                              <Palette className="h-4 w-4 mr-2" />
                              Make Stencil
                            </>
                          )}
                        </Button>
                        
                        <Button
                          variant="outline"
                          size="icon"
                          asChild
                        >
                          <a href={currentGeneration.image_url} download>
                            <Download className="h-4 w-4" />
                          </a>
                        </Button>
                      </div>

                      <div className="text-xs text-muted-foreground space-y-1">
                        <p>Generation ID: {currentGeneration.generation_id}</p>
                        <p>Trace ID: {currentGeneration.trace_id}</p>
                      </div>
                    </div>
                  )}

                  {currentGeneration.status === 'failed' && (
                    <div className="flex items-center justify-center p-8 border border-dashed border-destructive/50 rounded-lg">
                      <div className="text-center">
                        <p className="font-medium text-destructive">Generation failed</p>
                        <p className="text-sm text-muted-foreground">
                          Please try again with different parameters
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-center p-8 border border-dashed rounded-lg">
                  <div className="text-center">
                    <Wand2 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                    <p className="font-medium">Ready to generate</p>
                    <p className="text-sm text-muted-foreground">
                      Describe your tattoo design to get started
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Style Information */}
          {stylePresets?.success && (
            <Card>
              <CardHeader>
                <CardTitle>Style Information</CardTitle>
              </CardHeader>
              <CardContent>
                {stylePresets.styles[style] && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">
                        {stylePresets.styles[style].category}
                      </Badge>
                      <span className="font-medium">
                        {stylePresets.styles[style].name}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {stylePresets.styles[style].description}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}