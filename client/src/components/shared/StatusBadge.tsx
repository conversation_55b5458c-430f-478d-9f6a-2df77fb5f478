import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface StatusBadgeProps {
  status: string;
  variant?: 'outline' | 'default';
}

const getStatusColor = (status: string) => {
  const statusMap: Record<string, { bg: string, text: string }> = {
    'active': { bg: 'bg-green-100 dark:bg-green-900', text: 'text-green-800 dark:text-green-200' },
    'completed': { bg: 'bg-green-100 dark:bg-green-900', text: 'text-green-800 dark:text-green-200' },
    'inactive': { bg: 'bg-gray-100 dark:bg-gray-800', text: 'text-gray-800 dark:text-gray-200' },
    'pending': { bg: 'bg-yellow-100 dark:bg-yellow-900', text: 'text-yellow-800 dark:text-yellow-200' },
    'failed': { bg: 'bg-red-100 dark:bg-red-900', text: 'text-red-800 dark:text-red-200' },
    'blocked': { bg: 'bg-red-100 dark:bg-red-900', text: 'text-red-800 dark:text-red-200' },
    'info': { bg: 'bg-blue-100 dark:bg-blue-900', text: 'text-blue-800 dark:text-blue-200' },
    'admin': { bg: 'bg-purple-100 dark:bg-purple-900', text: 'text-purple-800 dark:text-purple-200' },
    'artist': { bg: 'bg-indigo-100 dark:bg-indigo-900', text: 'text-indigo-800 dark:text-indigo-200' },
    'client': { bg: 'bg-blue-100 dark:bg-blue-900', text: 'text-blue-800 dark:text-blue-200' }
  };
  
  return statusMap[status.toLowerCase()] || { bg: 'bg-gray-100 dark:bg-gray-800', text: 'text-gray-800 dark:text-gray-200' };
};

const StatusBadge = ({ status, variant = 'default' }: StatusBadgeProps) => {
  const { bg, text } = getStatusColor(status);
  
  if (variant === 'outline') {
    return (
      <Badge variant="outline" className={cn('capitalize', text)}>
        {status}
      </Badge>
    );
  }
  
  return (
    <span className={cn('px-2 inline-flex text-xs leading-5 font-semibold rounded-full capitalize', bg, text)}>
      {status}
    </span>
  );
};

export default StatusBadge;
