import { useState, useRef, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Camera, Scan, Download, Upload, RefreshCw, CheckCircle, AlertCircle, Eye, Zap } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useMutation } from "@tanstack/react-query";
import { ThreeDService, type ScanData, type OverlayData } from "@/services/threeDService";

// Using types from threeDService
type ScanResult = ScanData;
type TryOnResult = OverlayData;

export default function CameraScanner() {
  const { toast } = useToast();
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [scanProgress, setScanProgress] = useState(0);
  const [scanResult, setScanResult] = useState<ScanResult | null>(null);
  const [tryOnResult, setTryOnResult] = useState<TryOnResult | null>(null);
  const [selectedTattoo, setSelectedTattoo] = useState<string | null>(null);
  const [scanMode, setScanMode] = useState<'depth' | 'uv' | 'full'>('full');
  const [privacyMode, setPrivacyMode] = useState(true);

  // Start camera stream
  const startCamera = useCallback(async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1920 },
          height: { ideal: 1080 },
          frameRate: { ideal: 30 },
          facingMode: 'environment' // Prefer back camera
        }
      });
      
      setStream(mediaStream);
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
      
      toast({
        title: "Camera started",
        description: "Position the body part in frame for scanning"
      });
    } catch (error) {
      toast({
        title: "Camera access denied",
        description: "Please allow camera access to use 3D scanning",
        variant: "destructive"
      });
    }
  }, [toast]);

  // Stop camera stream
  const stopCamera = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
  }, [stream]);

  // Capture and process scan using ThreeDService
  const captureScan = useCallback(async () => {
    if (!videoRef.current || !canvasRef.current) return;

    setIsScanning(true);
    setScanProgress(0);

    try {
      const canvas = canvasRef.current;
      const video = videoRef.current;
      const ctx = canvas.getContext('2d')!;

      // Set canvas dimensions
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Capture frame
      ctx.drawImage(video, 0, 0);
      const imageData = canvas.toDataURL('image/jpeg', 0.9);

      // Simulate processing steps for UI feedback
      const steps = [
        { name: 'Capturing frame', progress: 20 },
        { name: 'Analyzing depth', progress: 40 },
        { name: 'Generating UV map', progress: 60 },
        { name: 'Creating 3D mesh', progress: 80 },
        { name: 'Finalizing scan', progress: 100 }
      ];

      // Show progress updates
      for (let i = 0; i < steps.length - 1; i++) {
        const step = steps[i];
        setScanProgress(step.progress);
        toast({
          title: step.name,
          description: `Processing... ${step.progress}%`
        });
        await new Promise(resolve => setTimeout(resolve, 600));
      }

      // Call actual 3D service
      const response = await ThreeDService.captureliveScan(
        imageData,
        'forearm', // Default body part - could be made selectable
        privacyMode,
        scanMode
      );

      if (response.success) {
        setScanResult(response.scan);
        setScanProgress(100);
        toast({
          title: "Scan completed",
          description: response.message
        });
      } else {
        throw new Error('Scan processing failed');
      }

    } catch (error) {
      console.error('Scan error:', error);
      toast({
        title: "Scan failed",
        description: "Please try again with better lighting",
        variant: "destructive"
      });
    } finally {
      setIsScanning(false);
    }
  }, [toast, privacyMode, scanMode]);

  // Apply tattoo to scan using ThreeDService
  const applyTattooMutation = useMutation({
    mutationFn: async ({ tattooUrl, scanData }: { tattooUrl: string; scanData: ScanResult }) => {
      const response = await ThreeDService.applyTattooOverlay(scanData, tattooUrl);
      if (!response.success) {
        throw new Error('Overlay application failed');
      }
      return response.overlay;
    },
    onSuccess: (result) => {
      setTryOnResult(result);
      toast({
        title: "Try-on complete",
        description: `Ray-traced overlay applied with ${(result.qualityScore * 100).toFixed(1)}% quality`
      });
    },
    onError: (error) => {
      console.error('Try-on error:', error);
      toast({
        title: "Try-on failed",
        description: "Failed to apply tattoo overlay",
        variant: "destructive"
      });
    }
  });

  const handleTattooUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const dataUrl = e.target?.result as string;
        setSelectedTattoo(dataUrl);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleTryOn = () => {
    if (selectedTattoo && scanResult) {
      applyTattooMutation.mutate({
        tattooUrl: selectedTattoo,
        scanData: scanResult
      });
    }
  };

  useEffect(() => {
    return () => {
      stopCamera();
    };
  }, [stopCamera]);

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2 flex items-center gap-2">
          <Scan className="h-8 w-8 text-blue-500" />
          3D Tattoo Try-On Scanner
        </h1>
        <p className="text-muted-foreground">
          Revolutionary 3D body scanning with ray-traced tattoo overlay and realistic lighting
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Panel - Camera and Controls */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Camera className="h-5 w-5" />
                3D Body Scanner
              </CardTitle>
              <CardDescription>
                {privacyMode ? "Privacy-first local processing" : "Cloud-enhanced scanning"}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Camera Preview */}
              <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
                <video
                  ref={videoRef}
                  autoPlay
                  muted
                  playsInline
                  className="w-full h-full object-cover"
                />
                <canvas
                  ref={canvasRef}
                  className="hidden"
                />
                
                {/* Overlay guides */}
                {stream && (
                  <div className="absolute inset-0 pointer-events-none">
                    <div className="absolute inset-4 border-2 border-dashed border-blue-500/50 rounded-lg" />
                    <div className="absolute top-4 left-4 bg-black/80 text-white px-2 py-1 rounded text-sm">
                      Position body part in frame
                    </div>
                  </div>
                )}
                
                {/* Scanning overlay */}
                {isScanning && (
                  <div className="absolute inset-0 bg-blue-500/20 flex items-center justify-center">
                    <div className="bg-black/80 text-white px-4 py-2 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        <span>Scanning in progress...</span>
                      </div>
                      <Progress value={scanProgress} className="w-48" />
                    </div>
                  </div>
                )}
              </div>

              {/* Camera Controls */}
              <div className="flex gap-2">
                {!stream ? (
                  <Button onClick={startCamera} className="flex-1">
                    <Camera className="h-4 w-4 mr-2" />
                    Start Camera
                  </Button>
                ) : (
                  <>
                    <Button 
                      onClick={captureScan} 
                      disabled={isScanning}
                      className="flex-1"
                    >
                      <Scan className="h-4 w-4 mr-2" />
                      {isScanning ? 'Scanning...' : 'Capture 3D Scan'}
                    </Button>
                    <Button variant="outline" onClick={stopCamera}>
                      Stop
                    </Button>
                  </>
                )}
              </div>

              {/* Scan Settings */}
              <Tabs value={scanMode} onValueChange={(v) => setScanMode(v as any)}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="depth">Depth Only</TabsTrigger>
                  <TabsTrigger value="uv">UV Mapping</TabsTrigger>
                  <TabsTrigger value="full">Full 3D</TabsTrigger>
                </TabsList>
              </Tabs>

              {/* Privacy Toggle */}
              <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                <div className="flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  <span className="text-sm font-medium">Privacy Mode</span>
                </div>
                <Button
                  variant={privacyMode ? "default" : "outline"}
                  size="sm"
                  onClick={() => setPrivacyMode(!privacyMode)}
                >
                  {privacyMode ? 'Local Processing' : 'Cloud Enhanced'}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Tattoo Upload */}
          <Card>
            <CardHeader>
              <CardTitle>Tattoo Design</CardTitle>
              <CardDescription>Upload or select a tattoo design to overlay</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleTattooUpload}
                  className="hidden"
                  id="tattoo-upload"
                />
                <label htmlFor="tattoo-upload" className="cursor-pointer">
                  <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm text-gray-600">Click to upload tattoo design</p>
                </label>
              </div>

              {selectedTattoo && (
                <div className="relative">
                  <img
                    src={selectedTattoo}
                    alt="Selected tattoo"
                    className="w-full h-32 object-cover rounded-lg"
                  />
                  <Badge className="absolute top-2 right-2">Ready</Badge>
                </div>
              )}

              <Button
                onClick={handleTryOn}
                disabled={!scanResult || !selectedTattoo || applyTattooMutation.isPending}
                className="w-full"
              >
                <Zap className="h-4 w-4 mr-2" />
                {applyTattooMutation.isPending ? 'Applying...' : 'Apply Ray-Traced Overlay'}
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Right Panel - Results */}
        <div className="space-y-6">
          {/* Scan Results */}
          {scanResult && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  3D Scan Results
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Confidence:</span>
                    <div className="font-medium">{(scanResult.confidence * 100).toFixed(1)}%</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Body Part:</span>
                    <div className="font-medium capitalize">{scanResult.bodyPart}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Width:</span>
                    <div className="font-medium">{scanResult.dimensions.width}cm</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Height:</span>
                    <div className="font-medium">{scanResult.dimensions.height}cm</div>
                  </div>
                </div>

                <Tabs defaultValue="depth" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="depth">Depth Map</TabsTrigger>
                    <TabsTrigger value="uv">UV Map</TabsTrigger>
                    <TabsTrigger value="mesh">3D Mesh</TabsTrigger>
                  </TabsList>
                  <TabsContent value="depth">
                    <img
                      src={scanResult.depthMap}
                      alt="Depth map"
                      className="w-full h-48 object-cover rounded-lg border"
                    />
                  </TabsContent>
                  <TabsContent value="uv">
                    <img
                      src={scanResult.uvMap}
                      alt="UV map"
                      className="w-full h-48 object-cover rounded-lg border"
                    />
                  </TabsContent>
                  <TabsContent value="mesh">
                    <img
                      src={scanResult.bodyMesh}
                      alt="3D mesh"
                      className="w-full h-48 object-cover rounded-lg border"
                    />
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          )}

          {/* Try-On Results */}
          {tryOnResult && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-yellow-500" />
                  Ray-Traced Overlay
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="relative">
                  <img
                    src={tryOnResult.compositeImage}
                    alt="Tattoo overlay"
                    className="w-full h-64 object-cover rounded-lg"
                  />
                  <Badge className="absolute top-2 right-2 bg-green-500">
                    {(tryOnResult.qualityScore * 100).toFixed(1)}% Quality
                  </Badge>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Ambient Light:</span>
                    <div className="font-medium">{(tryOnResult.lightingAnalysis.ambientLight * 100).toFixed(0)}%</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Skin Tone:</span>
                    <div className="font-medium flex items-center gap-2">
                      <div 
                        className="w-4 h-4 rounded-full border"
                        style={{ backgroundColor: tryOnResult.lightingAnalysis.skinTone }}
                      />
                      {tryOnResult.lightingAnalysis.skinTone}
                    </div>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button variant="outline" className="flex-1">
                    <Download className="h-4 w-4 mr-2" />
                    Export HD
                  </Button>
                  <Button variant="outline" className="flex-1">
                    Share Preview
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Instructions */}
          {!scanResult && (
            <Card>
              <CardHeader>
                <CardTitle>Scanning Instructions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    For best results, ensure good lighting and hold the camera steady during scanning.
                  </AlertDescription>
                </Alert>

                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    <span>Position body part within the frame guides</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    <span>Ensure even lighting without harsh shadows</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    <span>Hold camera steady during 3D capture</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    <span>Privacy mode processes data locally</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}