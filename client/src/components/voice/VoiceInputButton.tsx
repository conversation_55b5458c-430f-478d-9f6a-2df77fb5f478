import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ader2 } from 'lucide-react';
// Removed AudioRecorder dependency for simpler implementation

interface VoiceInputButtonProps {
  onTranscription: (text: string) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  className?: string;
}

export function VoiceInputButton({ 
  onTranscription, 
  onError, 
  disabled = false,
  className = ""
}: VoiceInputButtonProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleStartRecording = () => {
    startRecording();
  };

  const handleStopRecording = () => {
    stopRecording();
  };

  const handleAudioRecorded = async (audioBlob: Blob) => {
    if (!audioBlob) return;
    
    setIsProcessing(true);
    
    try {
      // Convert blob to base64
      const reader = new FileReader();
      reader.onloadend = async () => {
        const base64Audio = reader.result as string;
        
        try {
          // Send to transcription endpoint
          const response = await fetch('/api/ai/transcribe', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              audioData: base64Audio,
              format: 'webm'
            }),
          });

          if (!response.ok) {
            throw new Error(`Transcription failed: ${response.status}`);
          }

          const data = await response.json();
          const transcribedText = data.text || data.transcription || '';
          
          if (transcribedText.trim()) {
            onTranscription(transcribedText.trim());
          } else {
            onError?.('No speech detected. Please try again.');
          }
        } catch (err: any) {
          console.error('Transcription error:', err);
          onError?.(err.message || 'Failed to transcribe audio');
        } finally {
          setIsProcessing(false);
        }
      };
      
      reader.readAsDataURL(audioBlob);
    } catch (err: any) {
      console.error('Audio processing error:', err);
      onError?.(err.message || 'Failed to process audio');
      setIsProcessing(false);
    }
  };

  // Simple recording implementation
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      const audioChunks: Blob[] = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunks.push(event.data);
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
        handleAudioRecorded(audioBlob);
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);

      // Store recorder for stopping later
      (window as any).currentRecorder = mediaRecorder;
    } catch (err: any) {
      onError?.(err.message || 'Failed to start recording');
    }
  };

  const stopRecording = () => {
    const recorder = (window as any).currentRecorder;
    if (recorder && recorder.state === 'recording') {
      recorder.stop();
      setIsRecording(false);
    }
  };

  if (isProcessing) {
    return (
      <Button disabled variant="outline" size="icon" className={className}>
        <Loader2 className="h-4 w-4 animate-spin" />
      </Button>
    );
  }

  return (
    <Button
      type="button"
      variant="outline"
      size="icon"
      disabled={disabled || isProcessing}
      onMouseDown={handleStartRecording}
      onMouseUp={handleStopRecording}
      onTouchStart={handleStartRecording}
      onTouchEnd={handleStopRecording}
      className={`${className} ${isRecording ? 'bg-red-100 border-red-300 text-red-600' : ''}`}
      title="Hold to record voice message"
    >
      {isRecording ? (
        <MicOff className="h-4 w-4" />
      ) : (
        <Mic className="h-4 w-4" />
      )}
    </Button>
  );
}