import { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Mic, MicOff, Square } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PushToTalkButtonProps {
  onStartRecording: () => void;
  onStopRecording: () => void;
  onAudioData: (audioData: string) => void;
  isRecording: boolean;
  disabled?: boolean;
  className?: string;
}

export function PushToTalkButton({
  onStartRecording,
  onStopRecording,
  onAudioData,
  isRecording,
  disabled = false,
  className
}: PushToTalkButtonProps) {
  const [isPressed, setIsPressed] = useState(false);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  const startRecording = useCallback(async () => {
    if (disabled || isRecording) return;

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 16000
        } 
      });
      
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      audioChunksRef.current = [];
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };
      
      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm;codecs=opus' });
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64Audio = reader.result as string;
          onAudioData(base64Audio);
        };
        reader.readAsDataURL(audioBlob);
        
        // Stop all tracks
        stream.getTracks().forEach(track => track.stop());
      };
      
      mediaRecorder.start(100); // Collect data every 100ms
      mediaRecorderRef.current = mediaRecorder;
      
      setIsPressed(true);
      onStartRecording();
    } catch (error) {
      console.error('Error starting recording:', error);
    }
  }, [disabled, isRecording, onStartRecording, onAudioData]);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current = null;
    }
    
    setIsPressed(false);
    onStopRecording();
  }, [onStopRecording]);

  // Handle mouse/touch events
  const handleMouseDown = useCallback(() => {
    startRecording();
  }, [startRecording]);

  const handleMouseUp = useCallback(() => {
    stopRecording();
  }, [stopRecording]);

  // Handle keyboard events (spacebar for push-to-talk)
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.code === 'Space' && !isPressed) {
      e.preventDefault();
      startRecording();
    }
  }, [isPressed, startRecording]);

  const handleKeyUp = useCallback((e: React.KeyboardEvent) => {
    if (e.code === 'Space' && isPressed) {
      e.preventDefault();
      stopRecording();
    }
  }, [isPressed, stopRecording]);

  return (
    <div className="flex flex-col items-center gap-2">
      <Button
        variant={isPressed ? "default" : "outline"}
        size="lg"
        className={cn(
          "relative h-16 w-16 rounded-full p-0 transition-all duration-150",
          isPressed && "scale-95 ring-2 ring-primary ring-offset-2",
          disabled && "opacity-50 cursor-not-allowed",
          className
        )}
        disabled={disabled}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp} // Stop recording if mouse leaves button
        onTouchStart={handleMouseDown}
        onTouchEnd={handleMouseUp}
        onKeyDown={handleKeyDown}
        onKeyUp={handleKeyUp}
        tabIndex={0}
      >
        {isPressed ? (
          <Square className="h-8 w-8" />
        ) : (
          <Mic className="h-8 w-8" />
        )}
        
        {/* Visual feedback for recording */}
        {isPressed && (
          <div className="absolute inset-0 rounded-full bg-red-500/20 animate-pulse" />
        )}
      </Button>
      
      <div className="text-center">
        <p className="text-sm font-medium">
          {isPressed ? "Recording..." : "Push to Talk"}
        </p>
        <p className="text-xs text-muted-foreground">
          Hold to record • Press Space
        </p>
      </div>
    </div>
  );
}