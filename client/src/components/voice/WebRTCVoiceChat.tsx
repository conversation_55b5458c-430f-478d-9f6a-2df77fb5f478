import { useState, useEffect, useRef, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Mic, 
  MicOff, 
  Phone, 
  PhoneOff, 
  Volume2, 
  VolumeX,
  Settings,
  Users
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface WebRTCVoiceChatProps {
  agentId?: string;
  onSessionStart?: (sessionId: string) => void;
  onSessionEnd?: () => void;
  onError?: (error: string) => void;
}

interface RealtimeEvent {
  type: string;
  [key: string]: any;
}

export function WebRTCVoiceChat({ 
  agentId = 'designer', 
  onSessionStart, 
  onSessionEnd, 
  onError 
}: WebRTCVoiceChatProps) {
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [selectedVoice, setSelectedVoice] = useState("alloy");
  const [error, setError] = useState<string | null>(null);
  const [events, setEvents] = useState<RealtimeEvent[]>([]);

  const peerConnectionRef = useRef<RTCPeerConnection | null>(null);
  const dataChannelRef = useRef<RTCDataChannel | null>(null);
  const audioElementRef = useRef<HTMLAudioElement | null>(null);
  const localStreamRef = useRef<MediaStream | null>(null);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (localStreamRef.current) {
      localStreamRef.current.getTracks().forEach(track => track.stop());
      localStreamRef.current = null;
    }
    
    if (dataChannelRef.current) {
      dataChannelRef.current.close();
      dataChannelRef.current = null;
    }
    
    if (peerConnectionRef.current) {
      peerConnectionRef.current.close();
      peerConnectionRef.current = null;
    }
    
    if (audioElementRef.current) {
      audioElementRef.current.srcObject = null;
    }
    
    setIsConnected(false);
    setIsConnecting(false);
    setError(null);
  }, []);

  // Initialize WebRTC connection
  const initializeConnection = async () => {
    setIsConnecting(true);
    setError(null);

    try {
      // Get ephemeral token from our backend
      const tokenResponse = await fetch("/api/realtime/token");
      if (!tokenResponse.ok) {
        throw new Error(`Failed to get token: ${tokenResponse.status}`);
      }
      const tokenData = await tokenResponse.json();
      const ephemeralKey = tokenData.value;

      // Create peer connection
      const pc = new RTCPeerConnection({
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
      });

      peerConnectionRef.current = pc;

      // Set up audio element for remote audio
      if (!audioElementRef.current) {
        audioElementRef.current = document.createElement("audio");
        audioElementRef.current.autoplay = true;
        audioElementRef.current.style.display = 'none';
        document.body.appendChild(audioElementRef.current);
      }

      // Handle remote streams (AI voice)
      pc.ontrack = (event) => {
        console.log("Received remote track:", event);
        if (audioElementRef.current && event.streams[0]) {
          audioElementRef.current.srcObject = event.streams[0];
        }
      };

      // Get user media for microphone
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        }
      });

      localStreamRef.current = mediaStream;
      
      // Add local audio track
      mediaStream.getTracks().forEach(track => {
        pc.addTrack(track, mediaStream);
      });

      // Set up data channel for events
      const dataChannel = pc.createDataChannel("oai-events");
      dataChannelRef.current = dataChannel;

      dataChannel.onopen = () => {
        console.log("Data channel opened");
        setIsConnected(true);
        setIsConnecting(false);
        onSessionStart?.("webrtc-session");
      };

      dataChannel.onmessage = (event) => {
        try {
          const realtimeEvent = JSON.parse(event.data);
          console.log("Received event:", realtimeEvent);
          
          setEvents(prev => [...prev.slice(-9), realtimeEvent]); // Keep last 10 events
          
          // Handle specific events
          if (realtimeEvent.type === 'error') {
            setError(realtimeEvent.error?.message || 'Realtime API error');
          }
        } catch (err) {
          console.error("Failed to parse realtime event:", err);
        }
      };

      dataChannel.onerror = (error) => {
        console.error("Data channel error:", error);
        setError("Data channel error occurred");
      };

      // Handle connection state changes
      pc.onconnectionstatechange = () => {
        console.log("Connection state:", pc.connectionState);
        if (pc.connectionState === 'failed' || pc.connectionState === 'disconnected') {
          setError("Connection failed or disconnected");
          cleanup();
        }
      };

      // Create offer and set local description
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);

      // Send offer to OpenAI Realtime API
      const baseUrl = "https://api.openai.com/v1/realtime/calls";
      const model = "gpt-realtime";
      
      const sdpResponse = await fetch(`${baseUrl}?model=${model}`, {
        method: "POST",
        body: offer.sdp,
        headers: {
          Authorization: `Bearer ${ephemeralKey}`,
          "Content-Type": "application/sdp",
        },
      });

      if (!sdpResponse.ok) {
        throw new Error(`SDP exchange failed: ${sdpResponse.status}`);
      }

      const answerSdp = await sdpResponse.text();
      const answer = {
        type: "answer" as RTCSdpType,
        sdp: answerSdp,
      };

      await pc.setRemoteDescription(answer);

      console.log("WebRTC connection established successfully");

    } catch (err: any) {
      console.error("Failed to initialize WebRTC connection:", err);
      setError(err.message || "Failed to connect");
      setIsConnecting(false);
      cleanup();
      onError?.(err.message || "Connection failed");
    }
  };

  // Disconnect function
  const disconnect = useCallback(() => {
    cleanup();
    onSessionEnd?.();
  }, [cleanup, onSessionEnd]);

  // Toggle microphone
  const toggleMicrophone = () => {
    if (localStreamRef.current) {
      const audioTrack = localStreamRef.current.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        setIsMuted(!audioTrack.enabled);
      }
    }
  };

  // Toggle audio output
  const toggleAudio = () => {
    if (audioElementRef.current) {
      audioElementRef.current.muted = !audioElementRef.current.muted;
      setIsAudioEnabled(!audioElementRef.current.muted);
    }
  };

  // Send text event (for testing)
  const sendTestMessage = (text: string) => {
    if (dataChannelRef.current && dataChannelRef.current.readyState === 'open') {
      const event = {
        type: "conversation.item.create",
        item: {
          type: "message",
          role: "user",
          content: [
            {
              type: "input_text",
              text: text,
            },
          ],
        },
      };
      dataChannelRef.current.send(JSON.stringify(event));
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Phone className="h-5 w-5" />
          Real-time Voice Chat
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Connection Controls */}
        <div className="flex gap-2 justify-center">
          {!isConnected && !isConnecting && (
            <Button onClick={initializeConnection} className="flex items-center gap-2">
              <Phone className="h-4 w-4" />
              Connect
            </Button>
          )}
          
          {isConnecting && (
            <Button disabled className="flex items-center gap-2">
              <Phone className="h-4 w-4 animate-pulse" />
              Connecting...
            </Button>
          )}
          
          {isConnected && (
            <>
              <Button 
                onClick={disconnect} 
                variant="destructive" 
                className="flex items-center gap-2"
              >
                <PhoneOff className="h-4 w-4" />
                Disconnect
              </Button>
              
              <Button 
                onClick={toggleMicrophone}
                variant={isMuted ? "destructive" : "secondary"}
                size="icon"
              >
                {isMuted ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
              </Button>
              
              <Button 
                onClick={toggleAudio}
                variant={!isAudioEnabled ? "destructive" : "secondary"}
                size="icon"
              >
                {isAudioEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
              </Button>
            </>
          )}
        </div>

        {/* Voice Selection */}
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">Voice:</label>
          <Select value={selectedVoice} onValueChange={setSelectedVoice} disabled={isConnected}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="alloy">Alloy</SelectItem>
              <SelectItem value="echo">Echo</SelectItem>
              <SelectItem value="fable">Fable</SelectItem>
              <SelectItem value="onyx">Onyx</SelectItem>
              <SelectItem value="nova">Nova</SelectItem>
              <SelectItem value="shimmer">Shimmer</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Connection Status */}
        <div className="text-center">
          <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm ${
            isConnected 
              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
              : 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400'
          }`}>
            <div className={`w-2 h-2 rounded-full ${
              isConnected ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
            }`} />
            {isConnected ? 'Connected - Speak naturally!' : 'Not connected'}
          </div>
        </div>

        {/* Recent Events (for debugging) */}
        {events.length > 0 && (
          <div className="bg-muted p-3 rounded-lg">
            <h4 className="text-sm font-medium mb-2 flex items-center gap-1">
              <Settings className="h-3 w-3" />
              Recent Events
            </h4>
            <div className="space-y-1 text-xs font-mono max-h-32 overflow-y-auto">
              {events.slice(-5).map((event, i) => (
                <div key={i} className="text-muted-foreground">
                  {event.type}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="text-sm text-muted-foreground text-center">
          {isConnected 
            ? "Start speaking naturally - the AI will respond in real-time!"
            : "Click Connect to start a real-time voice conversation with the tattoo AI assistant."
          }
        </div>
      </CardContent>
    </Card>
  );
}