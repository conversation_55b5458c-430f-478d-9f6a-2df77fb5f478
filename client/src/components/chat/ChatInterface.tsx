import { useState, useRef, useEffect } from 'react';
import { Send, Paperclip, Mic, Bot, User, Volume2, Radio } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  agentId?: string;
  imageUrl?: string;
  isStreaming?: boolean;
}

interface ChatInterfaceProps {
  messages: Message[];
  onSendMessage: (message: string, imageUrl?: string) => void;
  onVoiceMessage: (audioData: string) => void;
  onStartVoiceSession: () => void;
  onEndVoiceSession: () => void;
  isLoading: boolean;
  currentAgent: string;
  agents: Array<{ id: string; name: string; color: string }>;
  onAgentSwitch: (agentId: string) => void;
  isVoiceSessionActive: boolean;
  voiceSessionStatus: 'idle' | 'connecting' | 'connected' | 'error';
  onImageUpload: (file: File) => void;
}

export function ChatInterface({
  messages,
  onSendMessage,
  onVoiceMessage,
  onStartVoiceSession,
  onEndVoiceSession,
  isLoading,
  currentAgent,
  agents,
  onAgentSwitch,
  isVoiceSessionActive,
  voiceSessionStatus,
  onImageUpload
}: ChatInterfaceProps) {
  const [input, setInput] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;
    
    onSendMessage(input.trim());
    setInput('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      onImageUpload(file);
    }
  };

  const getAgentInfo = (agentId: string) => {
    return agents.find(a => a.id === agentId) || { name: 'Assistant', color: 'blue' };
  };

  const getMessageTime = (timestamp: Date) => {
    return timestamp.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Header */}
      <div className="border-b bg-muted/30 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="bg-primary text-primary-foreground text-sm">
                <Bot className="h-4 w-4" />
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-semibold text-sm">
                {getAgentInfo(currentAgent).name}
              </div>
              <div className="text-xs text-muted-foreground">
                Tattoo AI Assistant
              </div>
            </div>
          </div>
          
          {/* Voice Session Status */}
          <div className="flex items-center gap-4">
            {isVoiceSessionActive && (
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-muted-foreground">
                  {voiceSessionStatus === 'connecting' && 'Connecting...'}
                  {voiceSessionStatus === 'connected' && 'Voice Active'}
                  {voiceSessionStatus === 'error' && 'Connection Error'}
                </span>
              </div>
            )}
            
            {/* Agent Quick Switch */}
            <div className="flex items-center gap-1">
              {agents.slice(0, 3).map((agent) => (
                <Button
                  key={agent.id}
                  variant={currentAgent === agent.id ? "default" : "ghost"}
                  size="sm"
                  className="h-8 px-2 text-xs"
                  onClick={() => onAgentSwitch(agent.id)}
                >
                  {agent.name}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 px-4">
        <div className="py-4 space-y-4">
          {messages.map((message, index) => (
            <div
              key={message.id}
              className={cn(
                "flex gap-3 group",
                message.role === 'user' ? "flex-row-reverse" : "flex-row"
              )}
            >
              {/* Avatar */}
              <Avatar className="h-8 w-8 shrink-0">
                {message.role === 'user' ? (
                  <AvatarFallback className="bg-blue-500 text-white">
                    <User className="h-4 w-4" />
                  </AvatarFallback>
                ) : message.role === 'system' ? (
                  <AvatarFallback className="bg-gray-500 text-white">
                    <Volume2 className="h-4 w-4" />
                  </AvatarFallback>
                ) : (
                  <AvatarFallback className="bg-primary text-primary-foreground">
                    <Bot className="h-4 w-4" />
                  </AvatarFallback>
                )}
              </Avatar>

              {/* Message Content */}
              <div className={cn(
                "flex flex-col max-w-[80%] gap-1",
                message.role === 'user' ? "items-end" : "items-start"
              )}>
                {/* Message Bubble */}
                <div
                  className={cn(
                    "rounded-2xl px-4 py-2.5 text-sm",
                    message.role === 'user'
                      ? "bg-primary text-primary-foreground rounded-br-md"
                      : message.role === 'system'
                      ? "bg-muted text-muted-foreground rounded-bl-md italic"
                      : "bg-muted rounded-bl-md",
                    message.isStreaming && "animate-pulse"
                  )}
                >
                  {/* Image if present */}
                  {message.imageUrl && (
                    <img
                      src={message.imageUrl}
                      alt="Shared image"
                      className="rounded-lg mb-2 max-w-sm"
                    />
                  )}
                  
                  {/* Message Text */}
                  <div className="whitespace-pre-wrap break-words">
                    {message.content || (message.isStreaming ? "..." : "")}
                  </div>
                </div>

                {/* Timestamp and Agent Info */}
                <div className={cn(
                  "flex items-center gap-2 text-xs text-muted-foreground px-1",
                  message.role === 'user' ? "flex-row-reverse" : "flex-row"
                )}>
                  <span>{getMessageTime(message.timestamp)}</span>
                  {message.role === 'assistant' && message.agentId && (
                    <>
                      <Separator orientation="vertical" className="h-3" />
                      <Badge variant="secondary" className="text-xs">
                        {getAgentInfo(message.agentId).name}
                      </Badge>
                    </>
                  )}
                </div>
              </div>
            </div>
          ))}
          
          {/* Loading indicator */}
          {isLoading && (
            <div className="flex gap-3">
              <Avatar className="h-8 w-8">
                <AvatarFallback className="bg-primary text-primary-foreground">
                  <Bot className="h-4 w-4" />
                </AvatarFallback>
              </Avatar>
              <div className="bg-muted rounded-2xl rounded-bl-md px-4 py-2.5">
                <div className="flex gap-1">
                  <div className="w-2 h-2 rounded-full bg-muted-foreground/60 animate-bounce [animation-delay:-0.3s]"></div>
                  <div className="w-2 h-2 rounded-full bg-muted-foreground/60 animate-bounce [animation-delay:-0.15s]"></div>
                  <div className="w-2 h-2 rounded-full bg-muted-foreground/60 animate-bounce"></div>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Input Area */}
      <div className="border-t bg-muted/30 px-4 py-4">
        {isVoiceSessionActive ? (
          /* Voice Session Active UI */
          <div className="flex flex-col items-center gap-4 py-4">
            <div className="text-center">
              <div className="text-lg font-semibold mb-1">Voice Conversation Active</div>
              <div className="text-sm text-muted-foreground">
                Speaking with {getAgentInfo(currentAgent).name}
              </div>
            </div>
            
            {/* Voice Status Indicator */}
            <div className="relative">
              <div className="w-16 h-16 rounded-full bg-primary/20 border-2 border-primary/30 flex items-center justify-center">
                <Radio className="h-8 w-8 text-primary" />
              </div>
              {/* Animated pulse rings */}
              <div className="absolute inset-0 rounded-full border border-primary/20 animate-ping"></div>
              <div className="absolute inset-0 rounded-full border border-primary/10 animate-ping [animation-delay:0.5s]"></div>
            </div>
            
            <Button
              onClick={onEndVoiceSession}
              variant="destructive"
              size="sm"
              className="rounded-full"
            >
              End Voice Session
            </Button>
          </div>
        ) : (
          /* Regular Text Input */
          <form onSubmit={handleSubmit} className="flex items-end gap-3">
            {/* File Upload */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              className="hidden"
              onChange={handleFileUpload}
            />
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="shrink-0 hover:bg-background"
              onClick={() => fileInputRef.current?.click()}
            >
              <Paperclip className="h-5 w-5" />
            </Button>

            {/* Message Input */}
            <div className="flex-1 relative">
              <Input
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder={`Message ${getAgentInfo(currentAgent).name}...`}
                className="pr-20 py-3 rounded-xl border-0 bg-background focus-visible:ring-1"
                disabled={isLoading}
              />
              
              {/* Sound Wave Button - Real-time Voice */}
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-10 top-1/2 -translate-y-1/2 h-8 w-8 hover:bg-muted"
                onClick={onStartVoiceSession}
                disabled={voiceSessionStatus === 'connecting'}
              >
                <Radio className={cn(
                  "h-4 w-4",
                  voiceSessionStatus === 'connecting' && "animate-pulse"
                )} />
              </Button>

              {/* Voice Input Button - Voice to Text */}
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 hover:bg-muted"
                onClick={() => {
                  setIsRecording(!isRecording);
                }}
              >
                <Mic className={cn(
                  "h-4 w-4",
                  isRecording && "text-red-500"
                )} />
              </Button>
            </div>

            {/* Send Button */}
            <Button
              type="submit"
              size="icon"
              className="shrink-0 rounded-xl"
              disabled={!input.trim() || isLoading}
            >
              <Send className="h-5 w-5" />
            </Button>
          </form>
        )}
      </div>
    </div>
  );
}