import { useState, useRef } from 'react';
import { useMutation } from '@tanstack/react-query';
import { analyzeImage, fileToBase64 } from '../../services/imageAnalysisService';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Loader2, Upload, Image as ImageIcon, ArrowRight, Zap, Eye } from 'lucide-react';
import OpenAIAgentsClient from '@/services/openaiAgentsClient';

interface ImageAnalyzerProps {
  onPromptGenerated: (prompt: string) => void;
}

export function ImageAnalyzer({ onPromptGenerated }: ImageAnalyzerProps) {
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [imageToAnalyze, setImageToAnalyze] = useState<string | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<{
    analysis: string;
    recreationPrompt: string;
  } | null>(null);

  // Mutation for analyzing images
  const analyzeMutation = useMutation({
    mutationFn: async (imageUrl: string) => {
      setIsAnalyzing(true);
      try {
        return await analyzeImage(imageUrl);
      } finally {
        setIsAnalyzing(false);
      }
    },
    onSuccess: (data) => {
      setAnalysisResults(data);
      toast({
        title: 'Image analyzed successfully',
        description: 'The tattoo design has been analyzed.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Failed to analyze image',
        description: error.message,
        variant: 'destructive',
      });
    }
  });

  // Enhanced mutation using OpenAI Agents SDK
  const enhancedAnalyzeMutation = useMutation({
    mutationFn: async (imageUrl: string) => {
      setIsAnalyzing(true);
      try {
        return await OpenAIAgentsClient.analyzeEnhancedTattooImage(imageUrl);
      } finally {
        setIsAnalyzing(false);
      }
    },
    onSuccess: (data) => {
      setAnalysisResults({
        analysis: data.analysis,
        recreationPrompt: data.recreationPrompt
      });
      toast({
        title: 'Enhanced analysis complete',
        description: 'The tattoo has been analyzed using advanced AI.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Enhanced analysis failed',
        description: error.message,
        variant: 'destructive',
      });
    }
  });

  // Handle file selection for image analysis
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    try {
      // Convert file to base64
      const base64Image = await fileToBase64(file);
      setImageToAnalyze(base64Image);
      
      // Reset analysis results when a new image is uploaded
      setAnalysisResults(null);
    } catch (error) {
      toast({
        title: 'Image upload failed',
        description: 'Failed to process the image. Please try a different file.',
        variant: 'destructive',
      });
    }
  };

  // Handle using the recreation prompt
  const handleUseRecreationPrompt = () => {
    if (!analysisResults?.recreationPrompt) return;
    
    onPromptGenerated(analysisResults.recreationPrompt);
    
    toast({
      title: 'Recreation prompt copied',
      description: 'The recreation prompt has been set as your custom prompt.',
      variant: 'default',
    });
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label>Upload Tattoo Design to Analyze</Label>
        <div className="border-2 border-dashed rounded-md p-4 flex flex-col items-center justify-center">
          <input
            ref={fileInputRef}
            type="file"
            className="hidden"
            accept="image/*"
            onChange={handleImageUpload}
          />
          
          {!imageToAnalyze ? (
            <div className="py-8 flex flex-col items-center text-center">
              <ImageIcon className="h-14 w-14 text-muted-foreground/50 mb-4" />
              <p className="text-sm text-muted-foreground mb-4">
                Upload a tattoo design image to analyze its style, elements, and get a recreation prompt
              </p>
              <Button 
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="mr-2 h-4 w-4" />
                Choose Image
              </Button>
            </div>
          ) : (
            <div className="py-4 flex flex-col items-center w-full">
              <div className="relative w-full max-w-[300px] h-[200px] mb-4 rounded-md overflow-hidden">
                <img 
                  src={imageToAnalyze} 
                  alt="Uploaded design" 
                  className="w-full h-full object-contain"
                />
              </div>
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Change Image
                </Button>
                <Button 
                  onClick={() => setImageToAnalyze(null)}
                  variant="outline" 
                  size="sm"
                >
                  Remove
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {imageToAnalyze && (
        <div className="space-y-3">
          <Button 
            onClick={() => {
              if (imageToAnalyze) {
                analyzeMutation.mutate(imageToAnalyze);
              }
            }}
            variant="outline"
            className="w-full" 
            disabled={isAnalyzing || !imageToAnalyze}
          >
            {isAnalyzing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <Eye className="mr-2 h-4 w-4" />
                Standard Analysis
              </>
            )}
          </Button>
          
          <Button 
            onClick={() => {
              if (imageToAnalyze) {
                enhancedAnalyzeMutation.mutate(imageToAnalyze);
              }
            }}
            className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600" 
            disabled={isAnalyzing || !imageToAnalyze}
          >
            {isAnalyzing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Enhanced Analyzing...
              </>
            ) : (
              <>
                <Zap className="mr-2 h-4 w-4" />
                Enhanced AI Analysis
              </>
            )}
          </Button>
          
          <div className="text-xs text-muted-foreground text-center">
            Enhanced analysis provides deeper insights using OpenAI Agents SDK
          </div>
        </div>
      )}
      
      {analysisResults && (
        <div className="space-y-4 mt-4">
          <div className="space-y-2">
            <Label>Design Analysis</Label>
            <Card className="text-sm">
              <CardContent className="p-4 overflow-auto max-h-[200px]">
                {analysisResults.analysis}
              </CardContent>
            </Card>
          </div>
          
          <div className="space-y-2">
            <Label>Recreation Prompt</Label>
            <Card className="text-sm bg-muted/50">
              <CardContent className="p-4 overflow-auto max-h-[150px]">
                {analysisResults.recreationPrompt}
              </CardContent>
            </Card>
          </div>
          
          <Button 
            className="w-full"
            onClick={handleUseRecreationPrompt}
          >
            <ArrowRight className="mr-2 h-4 w-4" />
            Use This Prompt to Generate
          </Button>
        </div>
      )}
    </div>
  );
}