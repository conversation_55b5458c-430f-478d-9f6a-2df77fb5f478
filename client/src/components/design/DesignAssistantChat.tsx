import { useState, useRef, useEffect } from 'react';
import { useMutation } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Loader2, Send, Wand2, User, Bot, Download, Save } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import OpenAIAgentsClient from '@/services/openaiAgentsClient';

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  imageUrl?: string;
  timestamp: Date;
}

export function DesignAssistantChat() {
  const { toast } = useToast();
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      role: 'assistant',
      content: "Hi! I'm your personal tattoo design assistant. I'll help you create the perfect tattoo design through conversation. Tell me about what you have in mind - I can generate images, refine designs, and guide you through the entire process. What kind of tattoo are you thinking about?",
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  // Chat mutation for conversational responses
  const chatMutation = useMutation({
    mutationFn: async (userMessage: string) => {
      setIsGenerating(true);
      try {
        // Use the designer agent for conversational tattoo design
        const response = await fetch('/api/agents/run', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ agentId: 'designer', message: userMessage })
        });
        
        if (!response.ok) {
          throw new Error('Failed to get response from design agent');
        }
        
        const data = await response.json();
        return data.response;
      } finally {
        setIsGenerating(false);
      }
    },
    onSuccess: (response) => {
      const assistantMessage: ChatMessage = {
        id: Date.now().toString(),
        role: 'assistant',
        content: response,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, assistantMessage]);
    },
    onError: (error: Error) => {
      toast({
        title: 'Chat failed',
        description: error.message,
        variant: 'destructive',
      });
      setIsGenerating(false);
    }
  });

  // Design generation mutation
  const generateMutation = useMutation({
    mutationFn: async (prompt: string) => {
      setIsGenerating(true);
      try {
        const design = await OpenAIAgentsClient.generateEnhancedTattooDesign(prompt);
        return design;
      } finally {
        setIsGenerating(false);
      }
    },
    onSuccess: (design) => {
      const assistantMessage: ChatMessage = {
        id: Date.now().toString(),
        role: 'assistant',
        content: `Here's your tattoo design! ${design.prompt ? `\n\nDesign details: ${design.prompt}` : ''}\n\nWhat do you think? I can help you refine it, change elements, or create variations. Just tell me what you'd like to adjust!`,
        imageUrl: design.imageUrl,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, assistantMessage]);
    },
    onError: (error: Error) => {
      toast({
        title: 'Design generation failed',
        description: error.message,
        variant: 'destructive',
      });
      setIsGenerating(false);
    }
  });

  const handleSendMessage = () => {
    if (!inputMessage.trim() || isGenerating) return;

    // Add user message
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: inputMessage,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, userMessage]);

    // Check if user is asking for image generation
    const generateKeywords = ['generate', 'create', 'design', 'make', 'show me', 'draw'];
    const isGenerateRequest = generateKeywords.some(keyword => 
      inputMessage.toLowerCase().includes(keyword)
    );

    if (isGenerateRequest) {
      generateMutation.mutate(inputMessage);
    } else {
      // Create context from recent messages for better conversation
      const recentContext = messages
        .slice(-3)
        .map(msg => `${msg.role}: ${msg.content}`)
        .join('\n');
      
      const contextualMessage = `Previous context:\n${recentContext}\n\nUser: ${inputMessage}`;
      chatMutation.mutate(contextualMessage);
    }

    setInputMessage('');
    inputRef.current?.focus();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleDownloadImage = async (imageUrl: string) => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `tattoo-design-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: 'Design downloaded',
        description: 'The tattoo design has been saved to your device.',
        variant: 'default',
      });
    } catch (error) {
      toast({
        title: 'Download failed',
        description: 'Failed to download the image.',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Chat Messages */}
      <Card className="flex-1 mb-4">
        <CardContent className="p-0">
          <ScrollArea className="h-[480px] p-4" ref={scrollAreaRef}>
            <div className="space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex gap-3 ${
                    message.role === 'user' ? 'justify-end' : 'justify-start'
                  }`}
                >
                  {message.role === 'assistant' && (
                    <Avatar className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500">
                      <AvatarFallback className="text-white">
                        <Bot className="w-4 h-4" />
                      </AvatarFallback>
                    </Avatar>
                  )}
                  
                  <div
                    className={`max-w-[70%] rounded-lg p-3 ${
                      message.role === 'user'
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted'
                    }`}
                  >
                    <div className="text-sm whitespace-pre-wrap">{message.content}</div>
                    
                    {/* Display generated image */}
                    {message.imageUrl && (
                      <div className="mt-3 space-y-2">
                        <div className="rounded-lg overflow-hidden border">
                          <img
                            src={message.imageUrl}
                            alt="Generated tattoo design"
                            className="w-full h-auto max-h-[300px] object-contain"
                          />
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDownloadImage(message.imageUrl!)}
                          >
                            <Download className="w-3 h-3 mr-1" />
                            Download
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setInputMessage("Can you make some variations of this design?");
                              inputRef.current?.focus();
                            }}
                          >
                            <Wand2 className="w-3 h-3 mr-1" />
                            Refine
                          </Button>
                        </div>
                      </div>
                    )}
                    
                    <div className="text-xs opacity-70 mt-2">
                      {message.timestamp.toLocaleTimeString([], { 
                        hour: '2-digit', 
                        minute: '2-digit' 
                      })}
                    </div>
                  </div>
                  
                  {message.role === 'user' && (
                    <Avatar className="w-8 h-8">
                      <AvatarFallback>
                        <User className="w-4 h-4" />
                      </AvatarFallback>
                    </Avatar>
                  )}
                </div>
              ))}
              
              {/* Loading indicator */}
              {isGenerating && (
                <div className="flex gap-3 justify-start">
                  <Avatar className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500">
                    <AvatarFallback className="text-white">
                      <Bot className="w-4 h-4" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="bg-muted rounded-lg p-3">
                    <div className="flex items-center gap-2">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span className="text-sm">Thinking...</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Input Area */}
      <div className="flex gap-2">
        <Input
          ref={inputRef}
          value={inputMessage}
          onChange={(e) => setInputMessage(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Describe your tattoo idea or ask for refinements..."
          disabled={isGenerating}
          className="flex-1"
        />
        <Button
          onClick={handleSendMessage}
          disabled={!inputMessage.trim() || isGenerating}
          className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
        >
          <Send className="w-4 h-4" />
        </Button>
      </div>

      {/* Quick Actions */}
      <div className="flex gap-2 mt-2 text-xs">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            setInputMessage("Can you help me design a traditional style tattoo?");
            inputRef.current?.focus();
          }}
          disabled={isGenerating}
        >
          Traditional Style
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            setInputMessage("I want a realistic black and gray tattoo");
            inputRef.current?.focus();
          }}
          disabled={isGenerating}
        >
          Realistic B&G
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            setInputMessage("Show me some geometric tattoo designs");
            inputRef.current?.focus();
          }}
          disabled={isGenerating}
        >
          Geometric
        </Button>
      </div>
    </div>
  );
}