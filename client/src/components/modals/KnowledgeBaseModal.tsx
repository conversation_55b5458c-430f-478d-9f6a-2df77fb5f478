import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useForm } from 'react-hook-form';
import { useToast } from '@/hooks/use-toast';

interface KnowledgeBaseFormData {
  title: string;
  content: string;
  category: string;
}

interface KnowledgeBaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  defaultValues?: Partial<KnowledgeBaseFormData>;
  isEdit?: boolean;
}

const KnowledgeBaseModal = ({ 
  isOpen, 
  onClose, 
  onSubmit, 
  defaultValues, 
  isEdit = false 
}: KnowledgeBaseModalProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  
  const { register, handleSubmit, formState: { errors }, setValue, reset } = useForm<KnowledgeBaseFormData>({
    defaultValues: defaultValues || {
      title: '',
      content: '',
      category: 'aftercare'
    }
  });
  
  const handleFormSubmit = async (data: KnowledgeBaseFormData) => {
    setIsSubmitting(true);
    
    try {
      await onSubmit(data);
      reset();
      onClose();
      toast({
        title: `Knowledge base entry ${isEdit ? 'updated' : 'created'} successfully`,
        variant: "default",
      });
    } catch (error) {
      toast({
        title: `Failed to ${isEdit ? 'update' : 'create'} knowledge base entry`,
        description: (error as Error).message || "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>{isEdit ? 'Edit Knowledge Base Entry' : 'Add Knowledge Base Entry'}</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input 
              id="title" 
              {...register('title', { required: 'Title is required' })}
              error={errors.title?.message}
            />
            {errors.title && (
              <p className="text-xs text-red-500">{errors.title.message}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select 
              defaultValue={defaultValues?.category || 'aftercare'}
              onValueChange={(value) => setValue('category', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="aftercare">Aftercare</SelectItem>
                <SelectItem value="styles">Tattoo Styles</SelectItem>
                <SelectItem value="preparation">Preparation</SelectItem>
                <SelectItem value="faq">FAQ</SelectItem>
                <SelectItem value="techniques">Techniques</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="content">Content</Label>
            <Textarea 
              id="content" 
              rows={8} 
              {...register('content', { required: 'Content is required' })}
              placeholder="Knowledge base content"
              error={errors.content?.message}
            />
            {errors.content && (
              <p className="text-xs text-red-500">{errors.content.message}</p>
            )}
          </div>
          
          <DialogFooter className="pt-4">
            <Button variant="outline" type="button" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : isEdit ? 'Update' : 'Create'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default KnowledgeBaseModal;
