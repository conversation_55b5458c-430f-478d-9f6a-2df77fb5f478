import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { useForm } from 'react-hook-form';
import { useToast } from '@/hooks/use-toast';

interface AgentFormData {
  name: string;
  description: string;
  systemPrompt: string;
  isActive: boolean;
}

interface AgentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  defaultValues?: Partial<AgentFormData>;
  isEdit?: boolean;
}

const AgentModal = ({ isOpen, onClose, onSubmit, defaultValues, isEdit = false }: AgentModalProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  
  const { register, handleSubmit, formState: { errors }, setValue, watch, reset } = useForm<AgentFormData>({
    defaultValues: defaultValues || {
      name: '',
      description: '',
      systemPrompt: '',
      isActive: true
    }
  });
  
  const isActive = watch('isActive');
  
  const handleFormSubmit = async (data: AgentFormData) => {
    setIsSubmitting(true);
    
    try {
      await onSubmit(data);
      reset();
      onClose();
      toast({
        title: `Agent ${isEdit ? 'updated' : 'created'} successfully`,
        variant: "default",
      });
    } catch (error) {
      toast({
        title: `Failed to ${isEdit ? 'update' : 'create'} agent`,
        description: (error as Error).message || "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>{isEdit ? 'Edit Agent' : 'Add New Agent'}</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="name">Agent Name</Label>
            <Input 
              id="name" 
              {...register('name', { required: 'Agent name is required' })}
              error={errors.name?.message}
            />
            {errors.name && (
              <p className="text-xs text-red-500">{errors.name.message}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea 
              id="description" 
              {...register('description')}
              placeholder="Brief description of the agent's purpose"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="systemPrompt">System Prompt</Label>
            <Textarea 
              id="systemPrompt" 
              rows={5} 
              {...register('systemPrompt', { required: 'System prompt is required' })}
              placeholder="Instructions for how the agent should behave"
              error={errors.systemPrompt?.message}
            />
            {errors.systemPrompt && (
              <p className="text-xs text-red-500">{errors.systemPrompt.message}</p>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch 
              checked={isActive} 
              onCheckedChange={(checked) => setValue('isActive', checked)}
              id="isActive"
            />
            <Label htmlFor="isActive">Active</Label>
          </div>
          
          <DialogFooter className="pt-4">
            <Button variant="outline" type="button" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : isEdit ? 'Update' : 'Create'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AgentModal;
