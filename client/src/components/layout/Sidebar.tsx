import { useState } from 'react';
import { Link, useLocation } from 'wouter';
import { useAuth } from '../../hooks/useAuth';
import { 
  LayoutDashboard, 
  Users, 
  Bot, 
  ListTodo, 
  Book, 
  Settings, 
  LogOut,
  ChevronLeft,
  MessageSquare,
  Paintbrush,
  Scan
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

interface SidebarProps {
  collapsed: boolean;
  toggleSidebar: () => void;
}

const Sidebar = ({ collapsed, toggleSidebar }: SidebarProps) => {
  const [location] = useLocation();
  const { user, logout } = useAuth();
  
  // Get user initials for avatar
  const getInitials = () => {
    if (!user) return '';
    return `${user.firstName.charAt(0)}${user.lastName.charAt(0)}`;
  };

  const isActive = (path: string) => {
    return location === path;
  };

  const navItems = [
    {
      name: 'Dashboard',
      path: '/dashboard',
      icon: <LayoutDashboard size={20} />,
      access: ['admin', 'artist', 'client']
    },
    {
      name: 'Chat',
      path: '/chat',
      icon: <MessageSquare size={20} />,
      access: ['admin', 'artist', 'client']
    },
    {
      name: 'Design Generator',
      path: '/design',
      icon: <Paintbrush size={20} />,
      access: ['admin', 'artist', 'client']
    },
    {
      name: '3D Try-On',
      path: '/3d-tryron',
      icon: <Scan size={20} />,
      access: ['admin', 'artist', 'client']
    },
    {
      name: 'User Management',
      path: '/users',
      icon: <Users size={20} />,
      access: ['admin']
    },
    {
      name: 'Agent Management',
      path: '/agents',
      icon: <Bot size={20} />,
      access: ['admin']
    },
    {
      name: 'Chat Logs',
      path: '/logs',
      icon: <ListTodo size={20} />,
      access: ['admin', 'artist']
    },
    {
      name: 'Knowledge Base',
      path: '/knowledge',
      icon: <Book size={20} />,
      access: ['admin', 'artist']
    },
    {
      name: 'Settings',
      path: '/settings',
      icon: <Settings size={20} />,
      access: ['admin', 'artist']
    }
  ];

  // Filter nav items based on user role
  const filteredNavItems = navItems.filter(item => 
    user && item.access.includes(user.role as string)
  );

  return (
    <div 
      className={cn(
        "bg-white dark:bg-neutral-900 shadow-lg z-20 h-screen fixed inset-y-0 left-0 transform transition-transform duration-300 lg:translate-x-0 lg:static lg:inset-0 flex flex-col",
        collapsed ? "w-16" : "w-64"
      )}
    >
      <div className="flex-grow overflow-y-auto">
        {/* Logo */}
        <div className="px-4 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
          <div className="flex items-center">
            <svg className="h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
              <path d="M11.25 5.337c0-.355-.186-.676-.401-.959a1.647 1.647 0 01-.349-1.003c0-1.036 1.007-1.875 2.25-1.875S15 2.34 15 3.375c0 .369-.128.713-.349 1.003-.215.283-.401.604-.401.959 0 .422.19.828.463 **********.812 1.28.812 2.037 0 1.949-1.727 3.5-3.75 3.5s-3.75-1.551-3.75-3.5c0-.758.312-1.487.812-2.037.273-.3.463-.706.463-1.128z"/>
              <path fillRule="evenodd" d="M12 14.25a.75.75 0 01.75.75v6a.75.75 0 01-1.5 0v-6a.75.75 0 01.75-.75z" clipRule="evenodd"/>
              <path d="M8.122 13.434a7.417 7.417 0 01-4.226-5.936c-.023-.23.038-.527.178-.527h15.85c.14 0 .202.296.179.527a7.417 7.417 0 01-4.226 5.936 7.5 7.5 0 01-7.755 0z"/>
            </svg>
            {!collapsed && <h1 className="ml-2 text-xl font-bold">Tattoo Admin</h1>}
          </div>
          <Button 
            variant="ghost" 
            size="icon" 
            className="lg:hidden text-gray-500"
            onClick={toggleSidebar}
          >
            <ChevronLeft size={20} />
          </Button>
        </div>
        
        {/* Navigation Menu */}
        <nav className="flex-1 py-4 overflow-y-auto">
          {!collapsed && (
            <div className="px-4 mb-2 text-xs font-semibold text-gray-500 uppercase tracking-wider dark:text-gray-400">
              Main
            </div>
          )}
          
          {filteredNavItems.map((item) => (
            <Tooltip key={item.path} delayDuration={150}>
              <TooltipTrigger asChild>
                <Link href={item.path}>
                  <div 
                    className={cn(
                      "flex items-center px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200",
                      isActive(item.path) ? "bg-gray-100 dark:bg-gray-800 border-l-4 border-primary text-primary" : "text-gray-600 dark:text-gray-400"
                    )}
                  >
                    <span className={cn(
                      isActive(item.path) ? "text-primary" : "text-gray-500 dark:text-gray-400",
                      "flex-shrink-0"
                    )}>
                      {item.icon}
                    </span>
                    {!collapsed && <span className="ml-3">{item.name}</span>}
                  </div>
                </Link>
              </TooltipTrigger>
              {collapsed && (
                <TooltipContent side="right">
                  {item.name}
                </TooltipContent>
              )}
            </Tooltip>
          ))}
          
          {!collapsed && user && user.role === 'admin' && (
            <div className="px-4 mt-6 mb-2 text-xs font-semibold text-gray-500 uppercase tracking-wider dark:text-gray-400">
              System
            </div>
          )}
        </nav>
      </div>
      
      {/* User Panel */}
      <div className="px-4 py-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center">
            <span className="text-sm font-medium">{getInitials()}</span>
          </div>
          {!collapsed && (
            <>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{user?.firstName} {user?.lastName}</p>
                <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">{user?.role}</p>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="ml-auto text-gray-500"
                onClick={logout}
              >
                <LogOut size={18} />
              </Button>
            </>
          )}
          {collapsed && (
            <Tooltip delayDuration={150}>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="ml-auto text-gray-500"
                  onClick={logout}
                >
                  <LogOut size={18} />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">
                Logout
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
