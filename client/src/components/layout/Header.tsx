import { Menu, Search, Bell, Moon, Sun, ChevronRight, ChevronLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useTheme } from '@/lib/theme.tsx';
import { Input } from '@/components/ui/input';
import { useLocation } from 'wouter';

interface HeaderProps {
  toggleSidebar: () => void;
  collapsed: boolean;
}

const Header = ({ toggleSidebar, collapsed }: HeaderProps) => {
  const { theme, setTheme } = useTheme();
  const [location] = useLocation();
  
  // Get page title based on current path
  const getPageTitle = () => {
    const path = location.split('/')[1] || 'dashboard';
    return path.charAt(0).toUpperCase() + path.slice(1);
  };

  return (
    <header className="bg-white dark:bg-neutral-900 shadow-sm z-10 sticky top-0">
      <div className="flex items-center justify-between px-4 py-3">
        {/* Mobile Menu Button */}
        <div className="flex items-center gap-2">
          <Button 
            variant="ghost" 
            size="icon"
            onClick={toggleSidebar}
            className={collapsed ? "lg:hidden" : "hidden"}
          >
            <Menu className="h-5 w-5" />
          </Button>
          
          {/* Breadcrumb */}
          <div className="flex items-center">
            <Button 
              variant="ghost" 
              size="icon"
              onClick={toggleSidebar}
              className={collapsed ? "hidden" : "lg:hidden"}
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <a href="/dashboard" className="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200">
              Dashboard
            </a>
            {location !== '/dashboard' && (
              <>
                <ChevronRight className="mx-2 h-4 w-4 text-gray-400" />
                <span className="text-gray-800 dark:text-gray-200">{getPageTitle()}</span>
              </>
            )}
          </div>
        </div>
        
        {/* Right Side Actions */}
        <div className="flex items-center space-x-4">
          {/* Search Input */}
          <div className="hidden md:block relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-400" />
            <Input 
              type="text" 
              placeholder="Search..." 
              className="w-64 pl-9 text-sm bg-gray-100 dark:bg-gray-800 border-gray-200 dark:border-gray-700"
            />
          </div>
          
          {/* Notifications */}
          <Button variant="ghost" size="icon" className="relative">
            <Bell className="h-5 w-5 text-gray-600 dark:text-gray-300" />
            <span className="absolute top-0 right-0 h-4 w-4 bg-accent-400 text-white text-xs rounded-full flex items-center justify-center">3</span>
          </Button>
          
          {/* Dark Mode Toggle */}
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
          >
            {theme === 'dark' ? (
              <Sun className="h-5 w-5 text-gray-300" />
            ) : (
              <Moon className="h-5 w-5 text-gray-600" />
            )}
          </Button>
        </div>
      </div>
    </header>
  );
};

export default Header;
