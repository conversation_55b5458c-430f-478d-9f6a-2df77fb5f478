import { Card, CardContent } from '@/components/ui/card';
import { <PERSON>C<PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, Tooltip, ResponsiveContainer } from 'recharts';

interface ActivityChartProps {
  data: {
    name: string;
    value: number;
  }[];
  title: string;
}

const ActivityChart = ({ data, title }: ActivityChartProps) => {
  return (
    <Card className="col-span-1">
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium">{title}</h2>
          <div className="flex space-x-2">
            <button className="px-3 py-1 text-sm bg-primary-50 dark:bg-primary-900 text-primary dark:text-primary-300 rounded-full">Daily</button>
            <button className="px-3 py-1 text-sm text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full">Weekly</button>
            <button className="px-3 py-1 text-sm text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full">Monthly</button>
          </div>
        </div>
        
        <div className="h-64 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data}>
              <XAxis dataKey="name" tick={{ fontSize: 12 }} />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: 'var(--background)', 
                  borderColor: 'var(--border)',
                  borderRadius: '0.375rem',
                }}
              />
              <Bar 
                dataKey="value" 
                fill="hsl(var(--primary))" 
                radius={[4, 4, 0, 0]} 
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};

export default ActivityChart;
