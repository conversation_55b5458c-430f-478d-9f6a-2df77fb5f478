import { ReactNode } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowDown, ArrowUp } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StatsCardProps {
  title: string;
  value: number | string;
  icon: ReactNode;
  iconBgColor: string;
  iconColor: string;
  percentageChange?: number;
  comparisonText?: string;
}

const StatsCard = ({
  title,
  value,
  icon,
  iconBgColor,
  iconColor,
  percentageChange,
  comparisonText = 'from last month'
}: StatsCardProps) => {
  const isPositive = percentageChange !== undefined && percentageChange > 0;
  const isNegative = percentageChange !== undefined && percentageChange < 0;

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center">
          <div className={cn("p-3 rounded-full", iconBgColor)}>
            <div className={cn("h-6 w-6", iconColor)}>
              {icon}
            </div>
          </div>
          <div className="ml-4">
            <p className="text-sm font-medium text-gray-500 dark:text-gray-400">{title}</p>
            <p className="text-2xl font-semibold">{value}</p>
          </div>
        </div>
        
        {percentageChange !== undefined && (
          <div className={cn(
            "mt-3 flex items-center text-sm",
            isPositive ? "text-green-500" : isNegative ? "text-red-500" : "text-gray-500"
          )}>
            {isPositive ? (
              <ArrowUp className="h-4 w-4 mr-1" />
            ) : isNegative ? (
              <ArrowDown className="h-4 w-4 mr-1" />
            ) : null}
            <span>{Math.abs(percentageChange)}% {comparisonText}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default StatsCard;
