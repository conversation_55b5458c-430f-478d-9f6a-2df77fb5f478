import { 
  signInWithGoogle as firebaseSignInWithGoogle, 
  signOutUser as firebaseSignOut,
  onAuthStateChange,
  getIdToken,
  mapFirebaseUser,
  trackUserAction,
  trackError,
  type FirebaseUser
} from './firebase';
import { loginWithGoogle as fastApiLoginWithGoogle, handleGoogleCallback } from './auth';

// API backend URL - defaults to same origin in production, localhost:8000 in development
const API_BASE_URL = import.meta.env.VITE_API_URL ||
  (import.meta.env.PROD ? window.location.origin : 'http://localhost:8000');

// Authentication strategy
type AuthStrategy = 'firebase' | 'fastapi' | 'hybrid';
const AUTH_STRATEGY: AuthStrategy = (import.meta.env.VITE_AUTH_STRATEGY as AuthStrategy) || 'hybrid';

export interface HybridUser {
  id: string;
  email: string;
  name: string;
  picture?: string;
  role?: 'admin' | 'artist' | 'client';
  status?: 'active' | 'inactive' | 'pending';
  provider: 'firebase' | 'google-oauth';
  firebaseUser?: FirebaseUser;
}

export interface AuthResult {
  user: HybridUser;
  token?: string;
  expiresAt?: string;
}

class HybridAuthService {
  private currentUser: HybridUser | null = null;
  private authStateListeners: ((user: HybridUser | null) => void)[] = [];

  constructor() {
    this.initializeAuthStateListener();
  }

  private initializeAuthStateListener() {
    // Listen to Firebase auth state changes
    onAuthStateChange(async (firebaseUser) => {
      if (firebaseUser) {
        const hybridUser = await this.createHybridUserFromFirebase(firebaseUser);
        this.setCurrentUser(hybridUser);
      } else {
        this.setCurrentUser(null);
      }
    });
  }

  private async createHybridUserFromFirebase(firebaseUser: any): Promise<HybridUser> {
    const mappedUser = mapFirebaseUser(firebaseUser);
    
    return {
      id: mappedUser.uid,
      email: mappedUser.email || '',
      name: mappedUser.displayName || mappedUser.email?.split('@')[0] || 'User',
      picture: mappedUser.photoURL || undefined,
      role: 'client', // Default role, can be updated from backend
      status: 'active',
      provider: 'firebase',
      firebaseUser: mappedUser
    };
  }

  private setCurrentUser(user: HybridUser | null) {
    this.currentUser = user;
    this.notifyAuthStateListeners(user);
    
    // Store user in localStorage for persistence
    if (user) {
      localStorage.setItem('hybrid-auth-user', JSON.stringify(user));
    } else {
      localStorage.removeItem('hybrid-auth-user');
    }
  }

  private notifyAuthStateListeners(user: HybridUser | null) {
    this.authStateListeners.forEach(listener => listener(user));
  }

  /**
   * Sign in with Google using the configured strategy
   */
  async signInWithGoogle(): Promise<AuthResult> {
    try {
      trackUserAction('login_attempt', { method: 'google', strategy: AUTH_STRATEGY });

      switch (AUTH_STRATEGY) {
        case 'firebase':
          return await this.signInWithFirebase();
        
        case 'fastapi':
          return await this.signInWithFastAPI();
        
        case 'hybrid':
        default:
          // Try Firebase first, fallback to FastAPI
          try {
            return await this.signInWithFirebase();
          } catch (firebaseError) {
            console.warn('Firebase auth failed, trying FastAPI:', firebaseError);
            trackError('firebase_auth_failed', 'falling_back_to_fastapi');
            return await this.signInWithFastAPI();
          }
      }
    } catch (error: any) {
      trackError('login_failed', error.message);
      throw error;
    }
  }

  private async signInWithFirebase(): Promise<AuthResult> {
    const firebaseUser = await firebaseSignInWithGoogle();
    const hybridUser = await this.createHybridUserFromFirebase(firebaseUser);
    
    // Get Firebase ID token for backend authentication
    const token = await getIdToken();
    
    trackUserAction('login_success', { method: 'firebase', userId: hybridUser.id });
    
    return {
      user: hybridUser,
      token: token || undefined
    };
  }

  private async signInWithFastAPI(): Promise<AuthResult> {
    // Get Google OAuth URL and redirect
    const authUrl = await fastApiLoginWithGoogle();
    window.location.href = authUrl;
    
    // This will be handled by the callback page
    throw new Error('Redirecting to Google OAuth...');
  }

  /**
   * Handle OAuth callback (for FastAPI strategy)
   */
  async handleOAuthCallback(code: string): Promise<AuthResult> {
    try {
      const authData = await handleGoogleCallback(code);
      
      const hybridUser: HybridUser = {
        id: authData.user.id,
        email: authData.user.email,
        name: authData.user.name,
        picture: authData.user.picture,
        role: 'client',
        status: 'active',
        provider: 'google-oauth'
      };

      this.setCurrentUser(hybridUser);
      
      trackUserAction('login_success', { method: 'fastapi', userId: hybridUser.id });
      
      return {
        user: hybridUser,
        token: authData.token,
        expiresAt: authData.expires_at
      };
    } catch (error: any) {
      trackError('oauth_callback_failed', error.message);
      throw error;
    }
  }

  /**
   * Sign out user
   */
  async signOut(): Promise<void> {
    try {
      const userId = this.currentUser?.id;
      
      // Sign out from Firebase if using Firebase auth
      if (this.currentUser?.provider === 'firebase') {
        await firebaseSignOut();
      }
      
      // Clear local storage
      localStorage.removeItem('hybrid-auth-user');
      localStorage.removeItem('auth-token');
      localStorage.removeItem('auth-user');
      
      this.setCurrentUser(null);
      
      trackUserAction('logout_success', { userId });
    } catch (error: any) {
      trackError('logout_failed', error.message);
      throw error;
    }
  }

  /**
   * Get current user
   */
  getCurrentUser(): HybridUser | null {
    return this.currentUser;
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return this.currentUser !== null;
  }

  /**
   * Get authentication token for API calls
   */
  async getAuthToken(): Promise<string | null> {
    if (!this.currentUser) return null;

    if (this.currentUser.provider === 'firebase') {
      return await getIdToken();
    } else {
      return localStorage.getItem('auth-token');
    }
  }

  /**
   * Add auth state listener
   */
  onAuthStateChanged(callback: (user: HybridUser | null) => void): () => void {
    this.authStateListeners.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.authStateListeners.indexOf(callback);
      if (index > -1) {
        this.authStateListeners.splice(index, 1);
      }
    };
  }

  /**
   * Initialize from stored user data
   */
  initializeFromStorage(): void {
    const storedUser = localStorage.getItem('hybrid-auth-user');
    if (storedUser) {
      try {
        const user = JSON.parse(storedUser);
        this.currentUser = user;
      } catch (error) {
        console.error('Failed to parse stored user data:', error);
        localStorage.removeItem('hybrid-auth-user');
      }
    }
  }

  /**
   * Sync user data with backend
   */
  async syncUserWithBackend(): Promise<void> {
    if (!this.currentUser) return;

    try {
      const token = await this.getAuthToken();
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const userData = await response.json();
        
        // Update user with backend data
        this.currentUser = {
          ...this.currentUser,
          role: userData.user?.role || this.currentUser.role,
          status: userData.user?.status || this.currentUser.status
        };
        
        this.setCurrentUser(this.currentUser);
      }
    } catch (error) {
      console.error('Failed to sync user with backend:', error);
    }
  }
}

// Export singleton instance
export const hybridAuth = new HybridAuthService();

// Initialize from storage on module load
hybridAuth.initializeFromStorage();
