// API backend URL - defaults to same origin in production, localhost:8000 in development
const API_BASE_URL = import.meta.env.VITE_API_URL ||
  (import.meta.env.PROD ? window.location.origin : 'http://localhost:8000');

interface LoginCredentials {
  username: string;
  password: string;
}

interface AuthResponse {
  user: {
    id: number;
    username: string;
    email: string;
    firstName: string;
    lastName: string;
    role: 'admin' | 'artist' | 'client';
    status: 'active' | 'inactive' | 'pending';
  };
  token: string;
}

interface GoogleAuthResponse {
  user: {
    id: string;
    email: string;
    name: string;
    picture: string;
    verified_email: boolean;
  };
  token: string;
  expires_at: string;
}

export async function login(credentials: LoginCredentials): Promise<AuthResponse> {
  // Use FastAPI backend
  const response = await fetch(`${API_BASE_URL}/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(credentials),
  });

  if (!response.ok) {
    throw new Error('Login failed');
  }

  const data = await response.json();

  // Store the token in localStorage
  localStorage.setItem('auth-token', data.token);

  return data;
}

export async function loginWithGoogle(): Promise<string> {
  // Get Google OAuth URL from FastAPI
  const response = await fetch(`${API_BASE_URL}/auth/google`);
  if (!response.ok) {
    throw new Error('Failed to get Google auth URL');
  }

  const data = await response.json();
  return data.auth_url;
}

export async function handleGoogleCallback(code: string): Promise<GoogleAuthResponse> {
  // Handle Google OAuth callback
  const response = await fetch(`${API_BASE_URL}/auth/google/callback?code=${encodeURIComponent(code)}`);
  if (!response.ok) {
    throw new Error('Google authentication failed');
  }

  const data = await response.json();

  // Store the token in localStorage
  localStorage.setItem('auth-token', data.token);

  return data;
}

export function logout(): void {
  localStorage.removeItem('auth-token');
  localStorage.removeItem('auth-user');
}

export function getToken(): string | null {
  return localStorage.getItem('auth-token');
}

export function isAuthenticated(): boolean {
  return !!getToken();
}

export function saveUser(user: AuthResponse['user']): void {
  localStorage.setItem('auth-user', JSON.stringify(user));
}

export function getUser(): AuthResponse['user'] | null {
  const userJson = localStorage.getItem('auth-user');
  if (!userJson) return null;
  
  try {
    return JSON.parse(userJson);
  } catch (e) {
    return null;
  }
}
