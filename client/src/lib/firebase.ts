// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics, Analytics } from "firebase/analytics";
import { getAuth, Auth, GoogleAuthProvider, signInWithPopup, signOut, onAuthStateChanged, User } from "firebase/auth";
import { getFirestore, Firestore } from "firebase/firestore";
import { getStorage, FirebaseStorage } from "firebase/storage";

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || "AIzaSyDeNiKkzEVduF8Pk1CxbAlBLR_knaewB4M",
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || "hardcore-tattoo-admin.firebaseapp.com",
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || "hardcore-tattoo-admin",
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || "hardcore-tattoo-admin.firebasestorage.app",
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || "177472563492",
  appId: import.meta.env.VITE_FIREBASE_APP_ID || "1:177472563492:web:ff5152d26f18a19244ebb1",
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID || "G-GS71H7K04F"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth: Auth = getAuth(app);
export const db: Firestore = getFirestore(app);
export const storage: FirebaseStorage = getStorage(app);

// Initialize Analytics (only in browser environment)
const analytics: Analytics | null = typeof window !== 'undefined' ? getAnalytics(app) : null;
export { analytics };

// Google Auth Provider
export const googleProvider = new GoogleAuthProvider();
googleProvider.addScope('email');
googleProvider.addScope('profile');

// Auth helper functions
export const signInWithGoogle = async (): Promise<User> => {
  try {
    const result = await signInWithPopup(auth, googleProvider);
    
    // Track sign-in event
    if (analytics) {
      // You can add custom analytics events here
      console.log('User signed in with Google:', result.user.email);
    }
    
    return result.user;
  } catch (error: any) {
    console.error('Google sign-in error:', error);
    throw new Error(error.message || 'Failed to sign in with Google');
  }
};

export const signOutUser = async (): Promise<void> => {
  try {
    await signOut(auth);
    
    // Track sign-out event
    if (analytics) {
      console.log('User signed out');
    }
  } catch (error: any) {
    console.error('Sign-out error:', error);
    throw new Error('Failed to sign out');
  }
};

// Auth state observer
export const onAuthStateChange = (callback: (user: User | null) => void) => {
  return onAuthStateChanged(auth, callback);
};

// Get current user
export const getCurrentUser = (): User | null => {
  return auth.currentUser;
};

// Get ID token for API authentication
export const getIdToken = async (): Promise<string | null> => {
  const user = getCurrentUser();
  if (!user) return null;
  
  try {
    return await user.getIdToken();
  } catch (error) {
    console.error('Error getting ID token:', error);
    return null;
  }
};

// Firebase Auth state management
export interface FirebaseUser {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
}

export const mapFirebaseUser = (user: User): FirebaseUser => {
  return {
    uid: user.uid,
    email: user.email,
    displayName: user.displayName,
    photoURL: user.photoURL,
    emailVerified: user.emailVerified
  };
};

// Analytics helper functions
export const logEvent = (eventName: string, parameters?: { [key: string]: any }) => {
  if (analytics) {
    // Import logEvent dynamically to avoid SSR issues
    import('firebase/analytics').then(({ logEvent }) => {
      logEvent(analytics, eventName, parameters);
    });
  }
};

// Custom analytics events
export const trackUserAction = (action: string, details?: { [key: string]: any }) => {
  logEvent('user_action', {
    action,
    ...details,
    timestamp: new Date().toISOString()
  });
};

export const trackPageView = (pageName: string) => {
  logEvent('page_view', {
    page_title: pageName,
    page_location: window.location.href
  });
};

export const trackError = (error: string, context?: string) => {
  logEvent('error', {
    error_message: error,
    error_context: context,
    timestamp: new Date().toISOString()
  });
};

// Firestore helper functions
export { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit,
  onSnapshot,
  serverTimestamp 
} from 'firebase/firestore';

// Storage helper functions
export { 
  ref, 
  uploadBytes, 
  uploadBytesResumable, 
  getDownloadURL, 
  deleteObject 
} from 'firebase/storage';

export default app;
