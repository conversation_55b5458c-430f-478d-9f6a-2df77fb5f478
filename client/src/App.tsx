import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import NotFound from "@/pages/not-found";
import Login from "@/pages/login";
import AuthCallback from "@/pages/auth-callback";
import Dashboard from "@/pages/dashboard";
import Users from "@/pages/users";
import Agents from "@/pages/agents";
import Logs from "@/pages/logs";
import Knowledge from "@/pages/knowledge";
import Settings from "@/pages/settings";
import Chat from "@/pages/chat";
// import VoiceChat from "@/pages/voice-chat"; // Merged into Chat component
import DesignGenerator from "@/pages/design-generator";
import ThreeDTryOn from "@/pages/3d-tryron";
import { AuthProvider } from "./hooks/useAuth.tsx";
import Layout from "./components/layout/Layout";
import PrivateRoute from "./components/layout/PrivateRoute";

function Router() {
  return (
    <Switch>
      <Route path="/login" component={Login} />
      <Route path="/auth/callback" component={AuthCallback} />

      {/* Protected routes */}
      <Route path="/">
        <PrivateRoute>
          <Layout>
            <Dashboard />
          </Layout>
        </PrivateRoute>
      </Route>
      
      <Route path="/dashboard">
        <PrivateRoute>
          <Layout>
            <Dashboard />
          </Layout>
        </PrivateRoute>
      </Route>
      
      <Route path="/users">
        <PrivateRoute>
          <Layout>
            <Users />
          </Layout>
        </PrivateRoute>
      </Route>
      
      <Route path="/agents">
        <PrivateRoute>
          <Layout>
            <Agents />
          </Layout>
        </PrivateRoute>
      </Route>
      
      <Route path="/logs">
        <PrivateRoute>
          <Layout>
            <Logs />
          </Layout>
        </PrivateRoute>
      </Route>
      
      <Route path="/knowledge">
        <PrivateRoute>
          <Layout>
            <Knowledge />
          </Layout>
        </PrivateRoute>
      </Route>
      
      <Route path="/settings">
        <PrivateRoute>
          <Layout>
            <Settings />
          </Layout>
        </PrivateRoute>
      </Route>
      
      <Route path="/chat">
        <PrivateRoute>
          <Layout>
            <Chat />
          </Layout>
        </PrivateRoute>
      </Route>
      

      
      <Route path="/design-generator">
        <PrivateRoute>
          <Layout>
            <DesignGenerator />
          </Layout>
        </PrivateRoute>
      </Route>
      
      <Route path="/design">
        <PrivateRoute>
          <Layout>
            <DesignGenerator />
          </Layout>
        </PrivateRoute>
      </Route>
      
      <Route path="/3d-tryron">
        <PrivateRoute>
          <Layout>
            <ThreeDTryOn />
          </Layout>
        </PrivateRoute>
      </Route>
      
      {/* Fallback to 404 */}
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <Router />
        </TooltipProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
