import { createContext, useContext, useEffect, useState, ReactNode, useMemo } from 'react';
import { login as loginApi, logout as logoutApi, getUser, saveUser, isAuthenticated } from '../lib/auth';
import { hybridAuth, type HybridUser } from '../lib/hybridAuth';
import { trackPageView } from '../lib/firebase';

interface User {
  id: number;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'admin' | 'artist' | 'client';
  status: 'active' | 'inactive' | 'pending';
}

interface AuthContextType {
  user: User | null;
  login: (username: string, password: string) => Promise<void>;
  loginWithGoogle: () => Promise<void>;
  logout: () => void;
  isLoading: boolean;
  error: string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// FastAPI backend URL
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

// For development purposes - mock an admin user
const DEV_MODE = import.meta.env.VITE_DEV_MODE === 'true' || import.meta.env.MODE === 'development';
const MOCK_ADMIN_USER: User = {
  id: 1,
  username: 'admin',
  email: '<EMAIL>',
  firstName: 'Admin',
  lastName: 'User',
  role: 'admin',
  status: 'active'
};

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Check if user is logged in on initial load
    const storedUser = getUser();
    if (storedUser && isAuthenticated()) {
      setUser(storedUser);
    } else if (DEV_MODE) {
      // In development mode, auto-login with mock admin
      console.log('DEV MODE: Auto-logging in with mock admin user');
      setUser(MOCK_ADMIN_USER);
      saveUser(MOCK_ADMIN_USER);
    }
    setIsLoading(false);
  }, []);

  const login = async (username: string, password: string) => {
    setIsLoading(true);
    setError(null);
    
    if (DEV_MODE) {
      // In dev mode, always succeed with mock user
      setUser(MOCK_ADMIN_USER);
      saveUser(MOCK_ADMIN_USER);
      setIsLoading(false);
      return;
    }
    
    try {
      const response = await loginApi({ username, password });
      setUser(response.user);
      saveUser(response.user);
    } catch (err) {
      setError('Failed to login. Please check your credentials.');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithGoogle = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await hybridAuth.signInWithGoogle();

      // Convert HybridUser to User format
      const user: User = {
        id: parseInt(result.user.id) || 1,
        username: result.user.email.split('@')[0],
        email: result.user.email,
        firstName: result.user.name.split(' ')[0] || '',
        lastName: result.user.name.split(' ').slice(1).join(' ') || '',
        role: result.user.role || 'client',
        status: result.user.status || 'active'
      };

      setUser(user);
      saveUser(user);
    } catch (err) {
      if (err instanceof Error && err.message.includes('Redirecting')) {
        // This is expected for OAuth redirect
        return;
      }
      setError(err instanceof Error ? err.message : 'Google login failed');
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await hybridAuth.signOut();
      logoutApi();
      setUser(null);

      if (DEV_MODE) {
        console.log('DEV MODE: Logged out, refreshing page to see login page');
      }
    } catch (err) {
      console.error('Logout error:', err);
    }
  };

  return (
    <AuthContext.Provider value={{ user, login, loginWithGoogle, logout, isLoading, error }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}