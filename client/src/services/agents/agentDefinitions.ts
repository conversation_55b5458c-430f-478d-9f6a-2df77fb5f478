/**
 * This file contains the definitions and system prompts for each specialized agent
 */
import { AGENT_SYSTEM_PROMPTS } from '../../config/systemInstructions';

export interface AgentDefinition {
  id: string;
  name: string;
  description: string;
  icon: string;
  systemPrompt: string;
  temperature?: number;
  canUseKnowledgeBase?: boolean;
  canGenerateImages?: boolean;
  canAnalyzeImages?: boolean;
  tools?: string[];
  capabilities?: string[];
}

export const AGENT_TYPES = {
  MANAGER: 'manager',
  GUIDANCE: 'guidance',
  DESIGNER: 'designer',
  ANALYST: 'analyst',
  AFTERCARE: 'aftercare',
  BOOKING: 'booking'
} as const;

export type AgentType = typeof AGENT_TYPES[keyof typeof AGENT_TYPES];

/**
 * Tattoo Manager Agent - Serves as the coordinator/router for other agents
 */
export const ManagerAgent: AgentDefinition = {
  id: AGENT_TYPES.MANAGER,
  name: 'Tattoo Manager',
  description: 'The Tattoo Manager helps route your query to the most appropriate specialized agent or directly answers general questions.',
  icon: 'user',
  systemPrompt: AGENT_SYSTEM_PROMPTS.manager,
  temperature: 0.5
};

/**
 * Tattoo Guidance Agent - Provides advice on styles, meanings, considerations
 */
export const GuidanceAgent: AgentDefinition = {
  id: AGENT_TYPES.GUIDANCE,
  name: 'Tattoo Guidance',
  description: 'I provide expert guidance on tattoo styles, cultural meanings, placement advice, and help you make informed decisions.',
  icon: 'compass',
  systemPrompt: AGENT_SYSTEM_PROMPTS.guidance,
  temperature: 0.7,
  canUseKnowledgeBase: true,
  tools: ['search_tattoo_knowledge', 'get_placement_advice'],
  capabilities: ['Style consultation', 'Cultural significance', 'Placement advice', 'Knowledge base search', 'Decision support']
};

/**
 * Tattoo Designer Agent - Helps visualize and refine design concepts
 */
export const DesignerAgent: AgentDefinition = {
  id: AGENT_TYPES.DESIGNER,
  name: 'Tattoo Designer',
  description: 'I create custom tattoo designs using AI image generation and help refine your concepts through conversation.',
  icon: 'paintbrush',
  systemPrompt: AGENT_SYSTEM_PROMPTS.designer,
  temperature: 0.7,
  canGenerateImages: true,
  tools: ['generate_tattoo_design', 'refine_design'],
  capabilities: ['Design generation', 'Style consultation', 'Iterative refinement', 'Visual concept development']
};

/**
 * Tattoo Analyst Agent - Analyzes and provides insights on existing tattoos
 */
export const AnalystAgent: AgentDefinition = {
  id: AGENT_TYPES.ANALYST,
  name: 'Tattoo Analyst',
  description: 'I analyze tattoo images to identify styles, assess quality, and provide detailed technical feedback.',
  icon: 'magnifying-glass',
  systemPrompt: AGENT_SYSTEM_PROMPTS.analyst,
  temperature: 0.5,
  canAnalyzeImages: true,
  tools: ['analyze_tattoo_image', 'compare_designs'],
  capabilities: ['Image analysis', 'Style identification', 'Quality assessment', 'Design comparison', 'Technical evaluation']
};

/**
 * Tattoo Aftercare Agent - Specialized in healing, maintenance, and addressing issues
 */
export const AftercareAgent: AgentDefinition = {
  id: AGENT_TYPES.AFTERCARE,
  name: 'Tattoo Aftercare',
  description: 'I provide comprehensive aftercare guidance, healing support, and maintenance advice for your tattoo journey.',
  icon: 'heart',
  systemPrompt: AGENT_SYSTEM_PROMPTS.aftercare,
  temperature: 0.5,
  canUseKnowledgeBase: true,
  tools: ['get_aftercare_instructions'],
  capabilities: ['Healing guidance', 'Problem diagnosis', 'Maintenance advice', 'Timeline tracking', 'Recovery support']
};

/**
 * Booking Agent - Handles appointment logistics and consultation information
 */
export const BookingAgent: AgentDefinition = {
  id: AGENT_TYPES.BOOKING,
  name: 'Booking Consultant',
  description: 'I help estimate session times, provide scheduling advice, and guide you through the booking process.',
  icon: 'calendar',
  systemPrompt: AGENT_SYSTEM_PROMPTS.booking,
  temperature: 0.5,
  tools: ['estimate_session_time'],
  capabilities: ['Time estimation', 'Scheduling advice', 'Session planning', 'Cost estimation', 'Appointment logistics']
};

// Export array of all agents
export const ALL_AGENTS: AgentDefinition[] = [
  ManagerAgent,
  GuidanceAgent,
  DesignerAgent,
  AnalystAgent,
  AftercareAgent,
  BookingAgent
];

/**
 * Get agent definition by ID
 */
export function getAgentById(id: string): AgentDefinition | undefined {
  return ALL_AGENTS.find(agent => agent.id === id);
}