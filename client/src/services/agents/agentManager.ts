import {
  AgentDefinition,
  AGENT_TYPES,
  ALL_AGENTS,
  ManagerAgent,
  getAgentById
} from './agentDefinitions';
import { AgentMessage, generateAgentResponse, generateImage, analyzeImage } from '../openai';
import { KnowledgeBase } from '@shared/schema';

// Chat message interface for the UI
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system' | 'function';
  content: string;
  agentId?: string;
  timestamp: Date;
  // For messages that include images
  imageUrl?: string;
}

export interface ChatSession {
  id: string;
  messages: ChatMessage[];
  currentAgentId: string;
  userId?: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Extracts routing information from the manager agent's response
 */
function parseManagerResponse(response: string): { routeTo: string | null; message: string } {
  // Check for routing instruction
  const routeMatch = response.match(/ROUTE_TO:\s*(\w+)/i);
  if (routeMatch) {
    const agentType = routeMatch[1].toLowerCase();
    // Remove the routing instruction from the message
    const message = response.replace(/ROUTE_TO:\s*\w+\s*-?\s*/i, '').trim();
    return { routeTo: agentType, message };
  }

  // Check for direct answer
  const directMatch = response.match(/DIRECT_ANSWER:/i);
  if (directMatch) {
    const message = response.replace(/DIRECT_ANSWER:/i, '').trim();
    return { routeTo: null, message };
  }

  // If no specific format is found, return the whole response
  return { routeTo: null, message: response };
}

/**
 * Main agent manager class that coordinates interactions between agents
 */
export class AgentManager {
  private sessions: Map<string, ChatSession> = new Map();
  private knowledgeBase: KnowledgeBase[] = [];

  constructor(knowledgeBase: KnowledgeBase[] = []) {
    this.knowledgeBase = knowledgeBase;
  }

  /**
   * Create a new chat session with the Manager agent
   */
  createSession(userId?: number): ChatSession {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    const session: ChatSession = {
      id: sessionId,
      messages: [],
      currentAgentId: AGENT_TYPES.MANAGER,
      userId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.sessions.set(sessionId, session);
    return session;
  }

  /**
   * Get session by ID
   */
  getSession(sessionId: string): ChatSession | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * Send a message to the current agent and get a response
   */
  async sendMessage(
    sessionId: string,
    message: string,
    imageUrl?: string
  ): Promise<ChatMessage> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`Session not found: ${sessionId}`);
    }

    // Add user message to the session
    const userMessage: ChatMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      role: 'user',
      content: message,
      timestamp: new Date(),
      imageUrl
    };
    session.messages.push(userMessage);
    session.updatedAt = new Date();
    
    try {
      // If we're talking to the manager agent, we need to handle potential routing
      if (session.currentAgentId === AGENT_TYPES.MANAGER) {
        const response = await this.routeMessageWithManager(session, message, imageUrl);
        return response;
      } 
      // Otherwise, send to the current specialist agent
      else {
        const response = await this.getSpecialistResponse(session, message, imageUrl);
        return response;
      }
    } catch (error) {
      console.error("Error processing message:", error);
      
      // Add error message to the conversation
      const errorMessage: ChatMessage = {
        id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        role: 'assistant',
        content: `I'm sorry, I encountered an error while processing your request. Please try again or start a new conversation.`,
        agentId: session.currentAgentId,
        timestamp: new Date()
      };
      
      session.messages.push(errorMessage);
      return errorMessage;
    }
  }

  /**
   * Route a message through the Manager agent
   */
  private async routeMessageWithManager(
    session: ChatSession,
    message: string,
    imageUrl?: string
  ): Promise<ChatMessage> {
    // Prepare messages for the manager agent
    const managerMessages: AgentMessage[] = [
      { role: 'system', content: ManagerAgent.systemPrompt }
    ];

    // Add context from previous messages
    const contextMessages = session.messages
      .filter(msg => msg.role === 'user' || (msg.role === 'assistant' && msg.agentId === AGENT_TYPES.MANAGER))
      .slice(-10); // Get last 10 relevant messages for context
      
    contextMessages.forEach(msg => {
      managerMessages.push({
        role: msg.role === 'assistant' ? 'assistant' : 'user',
        content: msg.content
      });
    });
    
    // Add the new message
    managerMessages.push({ role: 'user', content: message });
    
    // Get manager's response
    const managerResponse = await generateAgentResponse(managerMessages, {
      temperature: ManagerAgent.temperature
    });
    
    // Parse manager response for routing
    const { routeTo, message: responseMessage } = parseManagerResponse(managerResponse.message);
    
    // Add manager's response to the session
    const assistantMessage: ChatMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      role: 'assistant',
      content: responseMessage,
      agentId: AGENT_TYPES.MANAGER,
      timestamp: new Date()
    };
    
    session.messages.push(assistantMessage);
    
    // If manager wants to route to another agent
    if (routeTo) {
      // Find matching agent type
      const agentType = Object.values(AGENT_TYPES).find(
        type => type.toLowerCase() === routeTo.toLowerCase()
      );
      
      if (agentType && agentType !== AGENT_TYPES.MANAGER) {
        // Update current agent
        session.currentAgentId = agentType;
        
        // Get initial response from the specialist agent
        return await this.getSpecialistResponse(session, message, imageUrl);
      }
    }
    
    return assistantMessage;
  }

  /**
   * Get a response from the current specialist agent
   */
  private async getSpecialistResponse(
    session: ChatSession,
    message: string,
    imageUrl?: string
  ): Promise<ChatMessage> {
    const agent = getAgentById(session.currentAgentId);
    if (!agent) {
      throw new Error(`Unknown agent type: ${session.currentAgentId}`);
    }
    
    // Prepare messages for the specialist agent
    const agentMessages: AgentMessage[] = [
      { role: 'system', content: agent.systemPrompt }
    ];
    
    // If the agent can use the knowledge base, include relevant documents
    if (agent.canUseKnowledgeBase && this.knowledgeBase.length > 0) {
      // Find relevant knowledge base entries
      const relevantEntries = this.knowledgeBase.filter(entry => 
        entry.category.toLowerCase().includes(agent.id.toLowerCase()) ||
        message.toLowerCase().includes(entry.title.toLowerCase())
      );
      
      if (relevantEntries.length > 0) {
        const knowledgeContext = relevantEntries
          .map(entry => `[${entry.title}]: ${entry.content}`)
          .join('\n\n');
          
        agentMessages[0].content += `\n\nRelevant Knowledge Base Information:\n${knowledgeContext}`;
      }
    }
    
    // Add context from previous messages with this agent
    const contextMessages = session.messages
      .filter(msg => msg.role === 'user' || (msg.role === 'assistant' && msg.agentId === agent.id))
      .slice(-10); // Get last 10 relevant messages for context
      
    contextMessages.forEach(msg => {
      agentMessages.push({
        role: msg.role === 'assistant' ? 'assistant' : 'user',
        content: msg.content
      });
    });
    
    // Add the new message
    agentMessages.push({ role: 'user', content: message });
    
    // Handle image analysis if needed
    let imageAnalysis = '';
    if (imageUrl && agent.canAnalyzeImages) {
      try {
        imageAnalysis = await analyzeImage(imageUrl);
        agentMessages.push({ 
          role: 'system', 
          content: `Image Analysis: ${imageAnalysis}` 
        });
      } catch (error) {
        console.error("Error analyzing image:", error);
      }
    }
    
    // Get agent's response
    const agentResponse = await generateAgentResponse(agentMessages, {
      temperature: agent.temperature
    });
    
    // Check if the agent wants to generate an image
    let generatedImageUrl: string | undefined;
    if (agent.canGenerateImages && agentResponse.message.includes("[GENERATE_IMAGE:")) {
      try {
        const promptMatch = agentResponse.message.match(/\[GENERATE_IMAGE:(.*?)\]/);
        if (promptMatch && promptMatch[1]) {
          const imagePrompt = promptMatch[1].trim();
          generatedImageUrl = await generateImage(imagePrompt);
          
          // Remove the generation tag from the message
          agentResponse.message = agentResponse.message.replace(/\[GENERATE_IMAGE:.*?\]/, '');
        }
      } catch (error) {
        console.error("Error generating image:", error);
      }
    }
    
    // Add agent's response to the session
    const assistantMessage: ChatMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      role: 'assistant',
      content: agentResponse.message,
      agentId: agent.id,
      timestamp: new Date(),
      imageUrl: generatedImageUrl
    };
    
    session.messages.push(assistantMessage);
    return assistantMessage;
  }

  /**
   * Switch to a different agent in the current session
   */
  async switchAgent(sessionId: string, agentId: string): Promise<ChatMessage> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`Session not found: ${sessionId}`);
    }
    
    const agent = getAgentById(agentId);
    if (!agent) {
      throw new Error(`Unknown agent type: ${agentId}`);
    }
    
    // Update current agent
    session.currentAgentId = agentId;
    
    // Add system message about agent switch
    const systemMessage: ChatMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      role: 'system',
      content: `You are now speaking with the ${agent.name} agent.`,
      agentId,
      timestamp: new Date()
    };
    
    session.messages.push(systemMessage);
    
    // Get introduction from the new agent
    const introMessage = `Please introduce yourself and explain how you can help with ${agent.name.toLowerCase()} related questions.`;
    const agentMessages: AgentMessage[] = [
      { role: 'system', content: agent.systemPrompt },
      { role: 'user', content: introMessage }
    ];
    
    const response = await generateAgentResponse(agentMessages, {
      temperature: agent.temperature
    });
    
    // Add agent's introduction to the session
    const assistantMessage: ChatMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      role: 'assistant',
      content: response.message,
      agentId,
      timestamp: new Date()
    };
    
    session.messages.push(assistantMessage);
    return assistantMessage;
  }

  /**
   * Set or update the knowledge base
   */
  setKnowledgeBase(knowledgeBase: KnowledgeBase[]): void {
    this.knowledgeBase = knowledgeBase;
  }
}

// Export a singleton instance
export const agentManager = new AgentManager();