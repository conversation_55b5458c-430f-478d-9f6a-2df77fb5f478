import OpenAI from "openai";

// the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
const DEFAULT_MODEL = "gpt-4o";

// Initialize the OpenAI client
// API key is injected by the server
const openai = new OpenAI();

export interface AgentMessage {
  role: 'system' | 'user' | 'assistant' | 'function';
  content: string;
  name?: string;
}

export interface AgentResponse {
  message: string;
  rawResponse?: any;
}

export interface AgentAction {
  type: string;
  payload?: any;
}

/**
 * Generate a response from OpenAI with a specific agent system prompt
 */
export async function generateAgentResponse(
  messages: AgentMessage[],
  options: {
    model?: string;
    temperature?: number;
    jsonMode?: boolean;
  } = {}
): Promise<AgentResponse> {
  try {
    const model = options.model || DEFAULT_MODEL;
    const temperature = options.temperature !== undefined ? options.temperature : 0.7;
    
    const requestOptions: any = {
      model,
      messages,
      temperature,
    };
    
    // Use JSON mode if requested
    if (options.jsonMode) {
      requestOptions.response_format = { type: "json_object" };
    }
    
    const response = await openai.chat.completions.create(requestOptions);
    
    return {
      message: response.choices[0].message.content || "No response generated",
      rawResponse: response
    };
  } catch (error: any) {
    console.error("Error generating agent response:", error);
    throw new Error(`Failed to generate response: ${error.message}`);
  }
}

/**
 * Generate an image based on a text prompt
 */
export async function generateImage(
  prompt: string,
  options: {
    size?: "1024x1024" | "1792x1024" | "1024x1792";
    quality?: "standard" | "hd";
    style?: "natural" | "vivid";
  } = {}
): Promise<string> {
  try {
    const response = await openai.images.generate({
      model: "dall-e-3",
      prompt,
      n: 1,
      size: options.size || "1024x1024",
      quality: options.quality || "standard",
      style: options.style || "vivid",
    });

    return response.data[0].url || '';
  } catch (error: any) {
    console.error("Error generating image:", error);
    throw new Error(`Failed to generate image: ${error.message}`);
  }
}

/**
 * Analyze an image and generate a text description
 */
export async function analyzeImage(
  imageUrl: string,
  prompt: string = "Analyze this tattoo image in detail. Describe the style, technique, and artistic elements."
): Promise<string> {
  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: prompt },
            {
              type: "image_url",
              image_url: { url: imageUrl }
            }
          ]
        }
      ]
    });
    
    return response.choices[0].message.content || "No analysis generated";
  } catch (error: any) {
    console.error("Error analyzing image:", error);
    throw new Error(`Failed to analyze image: ${error.message}`);
  }
}

export default {
  generateAgentResponse,
  generateImage,
  analyzeImage
};