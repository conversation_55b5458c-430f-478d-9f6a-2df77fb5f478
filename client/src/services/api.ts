import { apiRequest } from '@/lib/queryClient';

// User API functions
export const getUsers = async (page = 1, limit = 10) => {
  const res = await apiRequest('GET', `/api/users?page=${page}&limit=${limit}`);
  return res.json();
};

export const getUser = async (id: number) => {
  const res = await apiRequest('GET', `/api/users/${id}`);
  return res.json();
};

export const createUser = async (userData: any) => {
  const res = await apiRequest('POST', '/api/users', userData);
  return res.json();
};

export const updateUser = async (id: number, userData: any) => {
  const res = await apiRequest('PUT', `/api/users/${id}`, userData);
  return res.json();
};

export const deleteUser = async (id: number) => {
  await apiRequest('DELETE', `/api/users/${id}`);
  return true;
};

// Agent API functions
export const getAgents = async (page = 1, limit = 10) => {
  const res = await apiRequest('GET', `/api/agents?page=${page}&limit=${limit}`);
  return res.json();
};

export const getAgent = async (id: number) => {
  const res = await apiRequest('GET', `/api/agents/${id}`);
  return res.json();
};

export const createAgent = async (agentData: any) => {
  const res = await apiRequest('POST', '/api/agents', agentData);
  return res.json();
};

export const updateAgent = async (id: number, agentData: any) => {
  const res = await apiRequest('PUT', `/api/agents/${id}`, agentData);
  return res.json();
};

export const deleteAgent = async (id: number) => {
  await apiRequest('DELETE', `/api/agents/${id}`);
  return true;
};

// Chat Logs API functions
export const getChatLogs = async (page = 1, limit = 10, filters = {}) => {
  const queryParams = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    ...filters
  });
  
  const res = await apiRequest('GET', `/api/logs?${queryParams}`);
  return res.json();
};

export const getChatLog = async (id: number) => {
  const res = await apiRequest('GET', `/api/logs/${id}`);
  return res.json();
};

// Knowledge Base API functions
export const getKnowledgeBase = async (page = 1, limit = 10, category?: string) => {
  const queryParams = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString()
  });
  
  if (category) {
    queryParams.append('category', category);
  }
  
  const res = await apiRequest('GET', `/api/knowledge?${queryParams}`);
  return res.json();
};

export const getKnowledgeBaseItem = async (id: number) => {
  const res = await apiRequest('GET', `/api/knowledge/${id}`);
  return res.json();
};

export const createKnowledgeBaseItem = async (itemData: any) => {
  const res = await apiRequest('POST', '/api/knowledge', itemData);
  return res.json();
};

export const updateKnowledgeBaseItem = async (id: number, itemData: any) => {
  const res = await apiRequest('PUT', `/api/knowledge/${id}`, itemData);
  return res.json();
};

export const deleteKnowledgeBaseItem = async (id: number) => {
  await apiRequest('DELETE', `/api/knowledge/${id}`);
  return true;
};

// Dashboard Stats API
export const getDashboardStats = async () => {
  const res = await apiRequest('GET', '/api/stats');
  return res.json();
};
