import { apiRequest } from "../lib/queryClient";

export interface DesignOptions {
  style: string;
  technique: string;
  placement: string;
  colorPalette: string;
  subjectMatter: string;
  clientIntent: string;
  artistSpecialty: string;
  bookingPreference?: string;
}

export interface DesignResponse {
  imageUrl: string;
  prompt?: string;
}

export interface SavedDesign {
  id: number;
  name: string;
  imageUrl: string;
  options: DesignOptions;
  userId: number;
  createdAt: Date;
}

// Generate a tattoo design using OpenAI DALL-E API
export async function generateDesign(options: DesignOptions, customPrompt?: string): Promise<DesignResponse> {
  try {
    const response = await apiRequest('POST', '/api/designs/generate', {
      options,
      customPrompt
    });
    return await response.json();
  } catch (error) {
    console.error('Error generating design:', error);
    throw new Error('Failed to generate design. Please try again.');
  }
}

// Save a generated design
export async function saveDesign(design: { name: string; imageUrl: string; options: DesignOptions }): Promise<SavedDesign> {
  try {
    const response = await apiRequest('POST', '/api/designs', design);
    return await response.json();
  } catch (error) {
    console.error('Error saving design:', error);
    throw new Error('Failed to save design. Please try again.');
  }
}

// Get all designs for a user
export async function getUserDesigns(userId?: number): Promise<SavedDesign[]> {
  const queryParam = userId ? `?userId=${userId}` : '';
  try {
    const response = await apiRequest('GET', `/api/designs${queryParam}`);
    return await response.json();
  } catch (error) {
    console.error('Error fetching designs:', error);
    throw new Error('Failed to load designs. Please try again.');
  }
}

// Build a prompt for tattoo generation
export function buildDesignPrompt(options: DesignOptions): string {
  // Map options to more detailed descriptions
  const styleDescriptions: Record<string, string> = {
    watercolor: "watercolor style with soft, flowing colors that blend together like a painting",
    traditional: "traditional American style with bold black outlines and solid, vibrant colors",
    minimalist: "minimalist design with clean, simple lines and minimal details",
    geometric: "geometric style composed of shapes, patterns, and clean lines",
    realistic: "photorealistic style with fine details and shading",
    blackwork: "blackwork style with intricate black ink patterns and negative space"
  };

  const placementDescriptions: Record<string, string> = {
    "upper-arm": "designed for upper arm placement",
    "forearm": "designed for forearm placement",
    "shoulder": "designed for shoulder placement",
    "back": "designed for back placement",
    "leg": "designed for leg placement",
    "chest": "designed for chest placement"
  };

  const colorDescriptions: Record<string, string> = {
    "pastel": "soft, muted pastel colors",
    "vibrant": "bold, vibrant, saturated colors",
    "monochrome": "monochromatic design with different shades of a single color",
    "blackAndGray": "black and gray shading without color",
    "neon": "bright neon colors that pop"
  };

  const subjectDescriptions: Record<string, string> = {
    "floral": "floral design with flowers, plants, and botanical elements",
    "animal": "animal design featuring detailed wildlife or pet imagery",
    "abstract": "abstract design with non-representational elements",
    "symbolic": "symbolic design with meaningful imagery and symbols",
    "portrait": "portrait design featuring realistic face or figure"
  };

  // Build the prompt components
  const components = [];

  // Add the primary subject and style
  if (options.subjectMatter && subjectDescriptions[options.subjectMatter]) {
    components.push(subjectDescriptions[options.subjectMatter]);
  }

  if (options.style && styleDescriptions[options.style]) {
    components.push(styleDescriptions[options.style]);
  }

  // Add color palette
  if (options.colorPalette && colorDescriptions[options.colorPalette]) {
    components.push(`with ${colorDescriptions[options.colorPalette]}`);
  }

  // Add placement if specified
  if (options.placement && placementDescriptions[options.placement]) {
    components.push(placementDescriptions[options.placement]);
  }

  // Add technique if specified
  if (options.technique) {
    components.push(`using ${options.technique} technique`);
  }

  // Create a cohesive prompt
  let prompt = `A high-quality tattoo design featuring ${components.join(", ")}. `;
  
  // Add intent if specified
  if (options.clientIntent) {
    prompt += `The tattoo has ${options.clientIntent} significance. `;
  }

  // Add artist specialty if it differs from style
  if (options.artistSpecialty && options.artistSpecialty !== options.style) {
    prompt += `Created in the style of a tattoo artist who specializes in ${options.artistSpecialty} designs. `;
  }

  // Append professional quality descriptors
  prompt += "The design should be detailed, professional quality, suitable for a tattoo studio portfolio. Isolated on a plain background without text or borders.";

  return prompt;
}