// Intent detection and keyword extraction service
// Based on Tattoo AI Buddy specifications

export interface TattooKeywords {
  subject?: string;
  theme?: string;
  style?: string;
  color_palette?: string;
  technique?: string;
  artist_refs?: string[];
  body_zone?: string;
}

export interface ExtractedIntent {
  intent: 'info' | 'style_talk' | 'design_preview' | 'body_preview' | 'free_chat';
  keywords: TattooKeywords;
  confidence: number;
  shouldGenerateDesign: boolean;
}

// Interface for agent routing
export interface IntentResult {
  intent: string;
  confidence: number;
  keywords: string[];
  recommendedAgent?: string;
}

export class IntentDetectionService {
  private static readonly DESIGN_TRIGGER_WORDS = [
    'design', 'tattoo', 'ink', 'piece', 'artwork', 'create', 'generate', 'make', 'draw',
    'style', 'traditional', 'realistic', 'geometric', 'blackwork', 'watercolor',
    'arm', 'leg', 'back', 'chest', 'shoulder', 'forearm', 'thigh', 'calf'
  ];

  private static readonly STYLE_WORDS = [
    'traditional', 'neo-traditional', 'realistic', 'realism', 'geometric', 'blackwork',
    'dotwork', 'watercolor', 'japanese', 'irezumi', 'biomechanical', 'portrait',
    'minimalist', 'fine line', 'horror', 'surreal', 'trash polka', 'illustrative'
  ];

  private static readonly BODY_ZONES = [
    'arm', 'forearm', 'upper arm', 'shoulder', 'back', 'chest', 'leg', 'thigh', 'calf',
    'ankle', 'wrist', 'neck', 'hand', 'finger', 'ribs', 'stomach', 'hip', 'spine'
  ];

  static extractKeywords(text: string): TattooKeywords {
    const lowercaseText = text.toLowerCase();
    const keywords: TattooKeywords = {};

    // Extract style
    for (const style of this.STYLE_WORDS) {
      if (lowercaseText.includes(style)) {
        keywords.style = style;
        break;
      }
    }

    // Extract body zone
    for (const zone of this.BODY_ZONES) {
      if (lowercaseText.includes(zone)) {
        keywords.body_zone = zone;
        break;
      }
    }

    // Extract colors (simple pattern matching)
    const colorMatches = text.match(/\b(red|blue|green|black|white|gray|grey|yellow|orange|purple|pink|brown)\b/gi);
    if (colorMatches) {
      keywords.color_palette = colorMatches.join(', ');
    }

    // Extract subject matter (simple noun extraction)
    const subjectPatterns = [
      /\b(wolf|lion|tiger|eagle|dragon|snake|rose|skull|cross|heart|butterfly|phoenix)\b/gi
    ];
    
    for (const pattern of subjectPatterns) {
      const matches = text.match(pattern);
      if (matches) {
        keywords.subject = matches[0];
        break;
      }
    }

    return keywords;
  }

  static detectIntent(text: string): ExtractedIntent {
    const lowercaseText = text.toLowerCase();
    const keywords = this.extractKeywords(text);
    
    // Check for design-related intent
    const hasDesignWords = this.DESIGN_TRIGGER_WORDS.some(word => lowercaseText.includes(word));
    const hasKeywords = Object.keys(keywords).length > 0;
    
    if (hasDesignWords || hasKeywords) {
      return {
        intent: 'design_preview',
        keywords,
        confidence: hasDesignWords && hasKeywords ? 0.9 : 0.7,
        shouldGenerateDesign: true
      };
    }

    // Check for style discussion
    if (lowercaseText.includes('style') || lowercaseText.includes('what kind') || lowercaseText.includes('recommend')) {
      return {
        intent: 'style_talk',
        keywords,
        confidence: 0.8,
        shouldGenerateDesign: false
      };
    }

    // Check for informational queries
    if (lowercaseText.includes('how') || lowercaseText.includes('what') || lowercaseText.includes('when') || lowercaseText.includes('where')) {
      return {
        intent: 'info',
        keywords,
        confidence: 0.6,
        shouldGenerateDesign: false
      };
    }

    return {
      intent: 'free_chat',
      keywords,
      confidence: 0.5,
      shouldGenerateDesign: false
    };
  }

  static createTGAHandoff(intent: ExtractedIntent, modelChoice: string = 'flux'): any {
    return {
      operation: intent.intent === 'design_preview' ? 'design' : 'body_preview',
      keywords: intent.keywords,
      model_choice: modelChoice
    };
  }
}

// Agent routing logic based on intent
export function detectIntent(text: string): IntentResult {
  const lowercaseText = text.toLowerCase();
  const extractedIntent = IntentDetectionService.detectIntent(text);
  const keywords = Object.values(extractedIntent.keywords).filter(Boolean) as string[];
  
  // Agent routing based on intent and keywords
  let recommendedAgent: string | undefined;
  
  if (extractedIntent.intent === 'design_preview' || 
      lowercaseText.includes('design') || 
      lowercaseText.includes('create') || 
      lowercaseText.includes('generate')) {
    recommendedAgent = 'designer';
  } else if (lowercaseText.includes('aftercare') || 
             lowercaseText.includes('healing') || 
             lowercaseText.includes('care') ||
             lowercaseText.includes('infection')) {
    recommendedAgent = 'aftercare';
  } else if (lowercaseText.includes('analyze') || 
             lowercaseText.includes('look at') || 
             lowercaseText.includes('examine') ||
             lowercaseText.includes('rate') ||
             lowercaseText.includes('opinion')) {
    recommendedAgent = 'analyst';
  } else if (lowercaseText.includes('book') || 
             lowercaseText.includes('appointment') || 
             lowercaseText.includes('schedule') ||
             lowercaseText.includes('consultation') ||
             lowercaseText.includes('price')) {
    recommendedAgent = 'booking';
  } else if (lowercaseText.includes('style') || 
             lowercaseText.includes('recommend') || 
             lowercaseText.includes('suggest') ||
             lowercaseText.includes('advice') ||
             lowercaseText.includes('should i')) {
    recommendedAgent = 'guidance';
  } else {
    recommendedAgent = 'manager'; // Default to manager for routing
  }
  
  return {
    intent: extractedIntent.intent,
    confidence: extractedIntent.confidence,
    keywords: keywords,
    recommendedAgent
  };
}