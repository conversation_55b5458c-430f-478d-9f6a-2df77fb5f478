import { apiRequest } from "@/lib/queryClient";

export interface ScanData {
  scanId: string;
  depthMap: string;
  uvMap: string;
  bodyMesh: string;
  confidence: number;
  bodyPart: string;
  dimensions: {
    width: number;
    height: number;
    depth: number;
  };
  metadata?: {
    timestamp: Date;
    privacyMode: boolean;
    scanMode: string;
    processingTime: number;
  };
}

export interface OverlayData {
  compositeImage: string;
  rayTracedOverlay: string;
  lightingAnalysis: {
    ambientLight: number;
    directionalLight: { x: number; y: number; z: number };
    skinTone: string;
    shadowIntensity?: number;
    highlightIntensity?: number;
  };
  qualityScore: number;
  renderingStats?: {
    samples: number;
    bounces: number;
    renderTime: number;
  };
}

export interface ArtistPackage {
  packageId: string;
  stencilImage: string;
  vectorStencil: string | null;
  colorPalette: string[];
  dimensions: {
    width: number;
    height: number;
    depth: number;
  };
  placement: {
    bodyPart: string;
    coordinates: { x: number; y: number; z: number };
    rotation: { x: number; y: number; z: number };
  };
  instructions: string;
  downloadUrls: {
    stencilPng: string;
    vectorSvg: string | null;
    zipPackage: string | null;
  };
}

export class ThreeDService {
  // Live camera scanning
  static async captureliveScan(
    imageData: string,
    bodyPart: string,
    privacyMode: boolean = true,
    scanMode: 'depth' | 'uv' | 'full' = 'full'
  ): Promise<{ success: boolean; scan: ScanData; message: string }> {
    const response = await apiRequest('POST', '/api/3d/scan/live-capture', {
      imageData,
      bodyPart,
      privacyMode,
      scanMode
    });
    return await response.json();
  }

  // Apply ray-traced tattoo overlay
  static async applyTattooOverlay(
    scanData: ScanData,
    tattooImageUrl: string,
    lightingSettings?: any
  ): Promise<{ success: boolean; overlay: OverlayData; message: string }> {
    const response = await apiRequest('POST', '/api/3d/tattoo/apply-overlay', {
      scanData,
      tattooImageUrl,
      lightingSettings
    });
    return await response.json();
  }

  // Create professional artist package
  static async createArtistPackage(
    scanData: ScanData,
    overlayData: OverlayData,
    stencilOptions?: any
  ): Promise<{ success: boolean; package: ArtistPackage; message: string }> {
    const response = await apiRequest('POST', '/api/3d/export/artist-package', {
      scanData,
      overlayData,
      stencilOptions
    });
    return await response.json();
  }

  // Legacy endpoints for existing upload-based workflow
  static async uploadScan(
    photo: string,
    bodyPart: string
  ): Promise<{ success: boolean; depthPng: string; maskPng: string; traceId: string }> {
    const response = await apiRequest('POST', '/api/3d/scan/ingest', {
      photo,
      bodyPart
    });
    return await response.json();
  }

  static async generateUVMapping(
    depthPng: string,
    maskPng: string
  ): Promise<{ success: boolean; uvPng: string; normalPng: string }> {
    const response = await apiRequest('POST', '/api/3d/scan/unwrap', {
      depthPng,
      maskPng
    });
    return await response.json();
  }

  static async createStencil(
    bakedTexture: string
  ): Promise<{ success: boolean; stencilPng: string; svgVector: string }> {
    const response = await apiRequest('POST', '/api/3d/export/stencil', {
      bakedTexture
    });
    return await response.json();
  }

  static async extractPalette(
    tattooImage: string
  ): Promise<{ success: boolean; palette: string[] }> {
    const response = await apiRequest('POST', '/api/3d/export/palette', {
      tattooImage
    });
    return await response.json();
  }

  static async createDownloadPackage(
    stencilPng: string,
    svgVector: string,
    palette: string[],
    bakedTexture: string,
    originalPhoto: string
  ): Promise<{ success: boolean; zipUrl: string }> {
    const response = await apiRequest('POST', '/api/3d/export/package', {
      stencilPng,
      svgVector,
      palette,
      bakedTexture,
      originalPhoto
    });
    return await response.json();
  }
}

// Utility functions
export const formatScanDimensions = (dimensions: { width: number; height: number; depth: number }) => {
  return `${dimensions.width}cm × ${dimensions.height}cm × ${dimensions.depth}cm`;
};

export const getConfidenceColor = (confidence: number) => {
  if (confidence >= 0.9) return 'text-green-600';
  if (confidence >= 0.7) return 'text-yellow-600';
  return 'text-red-600';
};

export const getQualityScoreColor = (score: number) => {
  if (score >= 0.9) return 'bg-green-500';
  if (score >= 0.7) return 'bg-yellow-500';
  return 'bg-red-500';
};