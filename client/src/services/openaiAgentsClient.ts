// Client-side service for OpenAI Agents SDK integration
import { apiRequest } from '@/lib/queryClient';

export interface TattooDesignRequest {
  prompt: string;
  style?: string;
  size?: '1024x1024' | '1792x1024' | '1024x1792';
}

export interface TattooDesignResponse {
  imageUrl: string;
  prompt: string;
  style?: string;
  size: string;
}

export interface TattooAnalysisRequest {
  imageUrl: string;
  analysisType?: 'style_identification' | 'technical_assessment' | 'comprehensive_review';
}

export interface TattooAnalysisResponse {
  analysis: string;
  imageUrl: string;
  analysisType: string;
}

export interface AgentRunRequest {
  agentId: string;
  message: string;
  imageUrl?: string;
}

export interface AgentRunResponse {
  response: string;
  agentId: string;
  message: string;
}

export class OpenAIAgentsClient {
  /**
   * Generate a tattoo design using the OpenAI Agents SDK
   */
  static async generateTattooDesign(request: TattooDesignRequest): Promise<TattooDesignResponse> {
    const response = await apiRequest('POST', '/api/agents/tattoo/generate-design', request);
    return await response.json();
  }

  /**
   * Analyze a tattoo image using the OpenAI Agents SDK
   */
  static async analyzeTattooImage(request: TattooAnalysisRequest): Promise<TattooAnalysisResponse> {
    const response = await apiRequest('POST', '/api/agents/tattoo/analyze-image', request);
    return await response.json();
  }

  /**
   * Run a specific agent with a message and optional image
   */
  static async runAgent(request: AgentRunRequest): Promise<AgentRunResponse> {
    const response = await apiRequest('POST', '/api/agents/run', request);
    return await response.json();
  }

  /**
   * Generate enhanced tattoo designs with style-specific prompts
   */
  static async generateEnhancedTattooDesign(
    userPrompt: string, 
    selectedStyle?: string,
    bodyZone?: string
  ): Promise<TattooDesignResponse> {
    // Create an enhanced prompt based on user input and style
    let enhancedPrompt = userPrompt;
    
    if (selectedStyle) {
      enhancedPrompt = `${selectedStyle} tattoo style: ${userPrompt}`;
    }
    
    if (bodyZone) {
      enhancedPrompt += `, designed for ${bodyZone} placement`;
    }
    
    // Add tattoo-specific enhancements
    enhancedPrompt += ', professional tattoo design, black and gray or color, clean line work, appropriate for skin application';
    
    return this.generateTattooDesign({
      prompt: enhancedPrompt,
      style: selectedStyle,
      size: '1024x1024'
    });
  }

  /**
   * Perform comprehensive tattoo analysis
   */
  static async performComprehensiveAnalysis(imageUrl: string): Promise<{
    styleAnalysis: TattooAnalysisResponse;
    technicalAssessment: TattooAnalysisResponse;
    comprehensiveReview: TattooAnalysisResponse;
  }> {
    const [styleAnalysis, technicalAssessment, comprehensiveReview] = await Promise.all([
      this.analyzeTattooImage({ imageUrl, analysisType: 'style_identification' }),
      this.analyzeTattooImage({ imageUrl, analysisType: 'technical_assessment' }),
      this.analyzeTattooImage({ imageUrl, analysisType: 'comprehensive_review' })
    ]);

    return {
      styleAnalysis,
      technicalAssessment,
      comprehensiveReview
    };
  }

  /**
   * Get AI-powered design suggestions based on user preferences
   */
  static async getDesignSuggestions(
    preferences: {
      style?: string;
      theme?: string;
      bodyZone?: string;
      colorPreference?: string;
    }
  ): Promise<string[]> {
    const message = `Based on these preferences: ${JSON.stringify(preferences)}, suggest 5 creative tattoo design ideas that would work well in this style and placement.`;
    
    const response = await this.runAgent({
      agentId: 'designer',
      message
    });

    // Parse suggestions from the response
    const suggestions = response.response
      .split('\n')
      .filter(line => line.trim().length > 0)
      .map(line => line.replace(/^\d+[\.\)]\s*/, '').trim())
      .filter(suggestion => suggestion.length > 10);

    return suggestions.slice(0, 5);
  }

  /**
   * Get tattoo care recommendations from aftercare agent
   */
  static async getTattooCareRecommendations(
    tattooAge: 'fresh' | 'healing' | 'healed',
    concerns?: string[]
  ): Promise<string> {
    let message = `I have a ${tattooAge} tattoo`;
    
    if (concerns && concerns.length > 0) {
      message += ` and I'm concerned about: ${concerns.join(', ')}`;
    }
    
    message += '. What care recommendations do you have?';

    const response = await this.runAgent({
      agentId: 'aftercare',
      message
    });

    return response.response;
  }
}

export default OpenAIAgentsClient;