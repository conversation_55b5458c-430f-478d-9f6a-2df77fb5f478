// This file contains mock data for charts and visualizations
// Real data comes from API endpoints

// Dashboard activity chart data
export const activityChartData = [
  { name: '<PERSON>', value: 120 },
  { name: '<PERSON><PERSON>', value: 180 },
  { name: 'Wed', value: 250 },
  { name: 'Thu', value: 190 },
  { name: 'Fri', value: 220 },
  { name: 'Sat', value: 140 },
  { name: 'Sun', value: 110 },
];

// Dashboard agent usage chart data
export const agentUsageData = [
  { name: 'Style Advisor', value: 72, color: 'hsl(var(--primary))' },
  { name: 'Appointment Bot', value: 18, color: 'hsl(var(--secondary))' },
  { name: 'Design Assistant', value: 10, color: 'hsl(var(--accent))' },
];

// User activity trend data
export const userActivityTrend = [
  { month: 'Jan', newUsers: 45, activeUsers: 120 },
  { month: 'Feb', newUsers: 52, activeUsers: 145 },
  { month: 'Mar', newUsers: 61, activeUsers: 162 },
  { month: 'Apr', newUsers: 67, activeUsers: 170 },
  { month: 'May', newUsers: 72, activeUsers: 199 },
  { month: 'Jun', newUsers: 58, activeUsers: 215 },
  { month: 'Jul', newUsers: 64, activeUsers: 230 },
  { month: 'Aug', newUsers: 70, activeUsers: 242 },
  { month: 'Sep', newUsers: 81, activeUsers: 271 },
  { month: 'Oct', newUsers: 75, activeUsers: 290 },
  { month: 'Nov', newUsers: 80, activeUsers: 310 },
  { month: 'Dec', newUsers: 92, activeUsers: 342 }
];

// Top queries data
export const topQueriesData = [
  { query: 'How long does a tattoo take to heal?', count: 245 },
  { query: 'What is the best aftercare for a new tattoo?', count: 198 },
  { query: 'How painful is getting a tattoo?', count: 165 },
  { query: 'Do you have any traditional Japanese designs?', count: 142 },
  { query: 'What is the minimum age to get a tattoo?', count: 136 },
  { query: 'How much does a sleeve tattoo cost?', count: 129 },
  { query: 'Can I get a tattoo while pregnant?', count: 112 },
  { query: 'What tattoo styles do you offer?', count: 104 },
  { query: 'How do I book a consultation?', count: 98 },
  { query: 'What areas are the most painful to get tattooed?', count: 87 }
];

// Response time data
export const responseTimeData = [
  { day: '1', avgTime: 1.2 },
  { day: '2', avgTime: 1.5 },
  { day: '3', avgTime: 1.1 },
  { day: '4', avgTime: 0.9 },
  { day: '5', avgTime: 1.0 },
  { day: '6', avgTime: 1.3 },
  { day: '7', avgTime: 1.4 },
  { day: '8', avgTime: 1.2 },
  { day: '9', avgTime: 1.1 },
  { day: '10', avgTime: 0.8 },
  { day: '11', avgTime: 0.9 },
  { day: '12', avgTime: 1.0 },
  { day: '13', avgTime: 1.2 },
  { day: '14', avgTime: 1.4 }
];

// Knowledge base category distribution
export const knowledgeCategoryData = [
  { name: 'Aftercare', value: 35, color: 'hsl(var(--primary))' },
  { name: 'Styles', value: 25, color: 'hsl(var(--secondary))' },
  { name: 'Preparation', value: 15, color: 'hsl(var(--accent))' },
  { name: 'FAQ', value: 20, color: 'hsl(var(--chart-1))' },
  { name: 'Techniques', value: 5, color: 'hsl(var(--chart-2))' }
];

// Chat satisfaction scores
export const satisfactionScoreData = [
  { month: 'Jan', score: 4.2 },
  { month: 'Feb', score: 4.3 },
  { month: 'Mar', score: 4.1 },
  { month: 'Apr', score: 4.4 },
  { month: 'May', score: 4.5 },
  { month: 'Jun', score: 4.6 },
  { month: 'Jul', score: 4.6 },
  { month: 'Aug', score: 4.7 },
  { month: 'Sep', score: 4.8 },
  { month: 'Oct', score: 4.7 },
  { month: 'Nov', score: 4.8 },
  { month: 'Dec', score: 4.9 }
];
