import { apiRequest } from "../lib/queryClient";

/**
 * Analyze an image using OpenAI Vision API
 * @param imageUrl URL of the image or base64 encoded image data
 * @param customPrompt Optional custom prompt to guide the analysis
 * @returns Analysis results including description and prompt for recreation
 */
export async function analyzeImage(
  imageUrl: string,
  customPrompt?: string
): Promise<{
  analysis: string;
  recreationPrompt: string;
}> {
  try {
    const response = await apiRequest("POST", "/api/designs/analyze", {
      imageUrl,
      customPrompt: customPrompt || "Analyze this tattoo design in detail. Describe its style, elements, techniques, and unique characteristics."
    });
    
    return await response.json();
  } catch (error) {
    console.error("Error analyzing image:", error);
    throw new Error("Failed to analyze image. Please try again.");
  }
}

/**
 * Convert a file to base64 format
 * @param file File to convert
 * @returns Promise resolving to base64 string
 */
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      if (typeof reader.result === "string") {
        resolve(reader.result);
      } else {
        reject(new Error("Failed to convert file to base64"));
      }
    };
    reader.onerror = error => reject(error);
  });
}