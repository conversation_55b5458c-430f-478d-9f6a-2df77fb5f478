import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { apiRequest } from '@/lib/queryClient';
import { useTheme } from '@/lib/theme.tsx';
import { useToast } from '@/hooks/use-toast';
import { Settings as SettingsIcon, Save, PaintBucket, Globe, Moon, Server, ShieldCheck } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';

// Mock function to get settings
const getSettings = async () => {
  const res = await apiRequest('GET', '/api/settings');
  return res.json();
};

// Mock function to update a setting
const updateSetting = async ({ key, value }: { key: string, value: string }) => {
  const res = await apiRequest('PUT', `/api/settings/${key}`, { value });
  return res.json();
};

const Settings = () => {
  const { user: currentUser } = useAuth();
  const { theme, setTheme } = useTheme();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [activeTab, setActiveTab] = useState('appearance');
  const [apiEndpoint, setApiEndpoint] = useState('https://api.example.com');
  const [debugMode, setDebugMode] = useState(false);
  const [autoSave, setAutoSave] = useState(true);
  const [dataRetention, setDataRetention] = useState('30');
  
  const { data: settings, isLoading } = useQuery({
    queryKey: ['/api/settings'],
    onSuccess: (data) => {
      // Initialize form values from settings if available
      const endpointSetting = data.find((s: any) => s.key === 'apiEndpoint');
      if (endpointSetting) setApiEndpoint(endpointSetting.value);
      
      const debugSetting = data.find((s: any) => s.key === 'debugMode');
      if (debugSetting) setDebugMode(debugSetting.value === 'true');
      
      const autoSaveSetting = data.find((s: any) => s.key === 'autoSave');
      if (autoSaveSetting) setAutoSave(autoSaveSetting.value === 'true');
      
      const dataRetentionSetting = data.find((s: any) => s.key === 'dataRetention');
      if (dataRetentionSetting) setDataRetention(dataRetentionSetting.value);
    }
  });
  
  const updateSettingMutation = useMutation({
    mutationFn: updateSetting,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/settings'] });
      toast({
        title: "Settings updated successfully",
      });
    },
  });
  
  const handleThemeChange = (value: string) => {
    setTheme(value as 'light' | 'dark' | 'system');
    updateSettingMutation.mutateAsync({ key: 'theme', value });
  };
  
  const handleSaveSettings = async () => {
    try {
      await Promise.all([
        updateSettingMutation.mutateAsync({ key: 'apiEndpoint', value: apiEndpoint }),
        updateSettingMutation.mutateAsync({ key: 'debugMode', value: debugMode.toString() }),
        updateSettingMutation.mutateAsync({ key: 'autoSave', value: autoSave.toString() }),
        updateSettingMutation.mutateAsync({ key: 'dataRetention', value: dataRetention }),
      ]);
    } catch (error) {
      toast({
        title: "Failed to update settings",
        description: (error as Error).message || "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  // Only allow access to admin and artist users
  const isAuthorized = currentUser?.role === 'admin' || currentUser?.role === 'artist';
  
  if (!isAuthorized) {
    return (
      <div className="text-center py-10">
        <h2 className="text-2xl font-bold mb-2">Access Restricted</h2>
        <p className="text-gray-600 dark:text-gray-400">
          You don't have permission to access the settings section.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Settings</h1>
        <Button 
          onClick={handleSaveSettings} 
          disabled={updateSettingMutation.isPending}
        >
          <Save className="h-4 w-4 mr-2" />
          {updateSettingMutation.isPending ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
      
      {/* Settings overview card */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <div className="p-3 rounded-full bg-primary-100 dark:bg-primary-900">
              <SettingsIcon className="h-6 w-6 text-primary dark:text-primary-400" />
            </div>
            <div>
              <h3 className="text-lg font-medium mb-1">System Settings</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Configure the appearance, API endpoints, and other system settings for the Tattoo Chatbot admin panel.
                Changes will be applied immediately after saving.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-1 md:grid-cols-3">
          <TabsTrigger value="appearance" className="flex items-center gap-2">
            <PaintBucket className="h-4 w-4" />
            Appearance
          </TabsTrigger>
          <TabsTrigger value="api" className="flex items-center gap-2">
            <Server className="h-4 w-4" />
            API Configuration
          </TabsTrigger>
          <TabsTrigger value="system" className="flex items-center gap-2">
            <ShieldCheck className="h-4 w-4" />
            System
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="appearance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Theme Settings</CardTitle>
              <CardDescription>Customize the appearance of the admin panel</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="theme">Color Theme</Label>
                <Select value={theme} onValueChange={handleThemeChange}>
                  <SelectTrigger id="theme" className="w-full">
                    <SelectValue placeholder="Select theme" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">Light</SelectItem>
                    <SelectItem value="dark">Dark</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Choose between light, dark, or system-based theme
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="primaryColor">Primary Color</Label>
                <div className="flex gap-4">
                  <button 
                    className="w-8 h-8 rounded-full bg-primary hover:opacity-90 cursor-pointer ring-2 ring-offset-2 ring-primary ring-offset-background"
                    aria-label="Primary Purple"
                  />
                  <button 
                    className="w-8 h-8 rounded-full bg-blue-600 hover:opacity-90 cursor-pointer"
                    aria-label="Blue"
                  />
                  <button 
                    className="w-8 h-8 rounded-full bg-emerald-600 hover:opacity-90 cursor-pointer"
                    aria-label="Emerald"
                  />
                  <button 
                    className="w-8 h-8 rounded-full bg-rose-600 hover:opacity-90 cursor-pointer"
                    aria-label="Rose"
                  />
                  <button 
                    className="w-8 h-8 rounded-full bg-amber-600 hover:opacity-90 cursor-pointer"
                    aria-label="Amber"
                  />
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Select a primary color for the interface
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="fontFamily">Font Style</Label>
                <Select defaultValue="inter">
                  <SelectTrigger id="fontFamily">
                    <SelectValue placeholder="Select font" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="inter">Inter (Default)</SelectItem>
                    <SelectItem value="poppins">Poppins</SelectItem>
                    <SelectItem value="roboto">Roboto</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Choose the font family for the interface
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="api" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>API Configuration</CardTitle>
              <CardDescription>Configure API endpoints and settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="apiEndpoint">API Endpoint</Label>
                <Input 
                  id="apiEndpoint" 
                  value={apiEndpoint}
                  onChange={(e) => setApiEndpoint(e.target.value)}
                  placeholder="https://api.example.com"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  The base URL for API requests
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="apiVersion">API Version</Label>
                <Select defaultValue="v1">
                  <SelectTrigger id="apiVersion">
                    <SelectValue placeholder="Select API version" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="v1">v1</SelectItem>
                    <SelectItem value="v2">v2</SelectItem>
                    <SelectItem value="v3">v3 (Beta)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Select the API version to use
                </p>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="debugMode">Debug Mode</Label>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Enable detailed logging for API requests
                  </p>
                </div>
                <Switch
                  id="debugMode"
                  checked={debugMode}
                  onCheckedChange={setDebugMode}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="system" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Settings</CardTitle>
              <CardDescription>Configure system behavior and preferences</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="autoSave">Auto-save</Label>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Automatically save changes as they are made
                  </p>
                </div>
                <Switch
                  id="autoSave"
                  checked={autoSave}
                  onCheckedChange={setAutoSave}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="language">Default Language</Label>
                <Select defaultValue="en">
                  <SelectTrigger id="language">
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="es">Spanish</SelectItem>
                    <SelectItem value="fr">French</SelectItem>
                    <SelectItem value="de">German</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Default language for the interface
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="dataRetention">Data Retention (days)</Label>
                <Select 
                  value={dataRetention}
                  onValueChange={setDataRetention}
                >
                  <SelectTrigger id="dataRetention">
                    <SelectValue placeholder="Select retention period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7">7 days</SelectItem>
                    <SelectItem value="30">30 days</SelectItem>
                    <SelectItem value="90">90 days</SelectItem>
                    <SelectItem value="365">1 year</SelectItem>
                    <SelectItem value="0">Indefinite</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  How long to retain chat logs and user data
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="sessions">Session Timeout (minutes)</Label>
                <Input
                  id="sessions"
                  type="number"
                  defaultValue="60"
                  min="5"
                  max="1440"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  How long before an inactive session expires
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Settings;
