import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getChatLogs } from '../services/api';
import DataTable from '../components/shared/DataTable';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { DateRange } from 'react-day-picker';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { MessageSquare, Search, CalendarIcon, SlidersHorizontal } from 'lucide-react';
import StatusBadge from '@/components/shared/StatusBadge';
import { useAuth } from '../hooks/useAuth.tsx';

const Logs = () => {
  const { user: currentUser } = useAuth();
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [filters, setFilters] = useState<any>({});
  const [date, setDate] = useState<DateRange | undefined>();
  const [showFilters, setShowFilters] = useState(false);
  
  // If the user is an artist, only show logs for that artist
  const initialFilters = currentUser?.role === 'artist' ? { userId: currentUser.id } : {};
  
  const { data, isLoading } = useQuery({
    queryKey: ['/api/logs', { page, limit: pageSize, ...initialFilters, ...filters }],
  });
  
  const handleFilterChange = (key: string, value: any) => {
    if (!value) {
      const newFilters = { ...filters };
      delete newFilters[key];
      setFilters(newFilters);
    } else {
      setFilters({ ...filters, [key]: value });
    }
  };
  
  const handleDateChange = (range: DateRange | undefined) => {
    setDate(range);
    if (range?.from) {
      const fromDate = new Date(range.from);
      handleFilterChange('dateFrom', fromDate.toISOString());
      
      if (range.to) {
        const toDate = new Date(range.to);
        handleFilterChange('dateTo', toDate.toISOString());
      } else {
        handleFilterChange('dateTo', undefined);
      }
    } else {
      handleFilterChange('dateFrom', undefined);
      handleFilterChange('dateTo', undefined);
    }
  };
  
  const applyFilters = () => {
    setPage(1); // Reset to first page when applying filters
  };
  
  const resetFilters = () => {
    setFilters({});
    setDate(undefined);
    setPage(1);
  };
  
  const columns = [
    {
      key: 'user',
      label: 'User',
      render: (_: any, row: any) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
            <span className="text-xs font-medium">
              {row.user ? `${row.user.firstName?.charAt(0)}${row.user.lastName?.charAt(0)}` : 'NA'}
            </span>
          </div>
          <div className="ml-3">
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {row.user ? `${row.user.firstName} ${row.user.lastName}` : 'Unknown User'}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {row.user?.email || 'No email'}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'agent',
      label: 'Agent',
      render: (_: any, row: any) => (
        <div className="text-sm">
          {row.agent ? row.agent.name : 'N/A'}
        </div>
      )
    },
    {
      key: 'conversation',
      label: 'Conversation',
      render: (value: string) => (
        <div className="text-sm max-w-md truncate">
          {value ? (value.length > 100 ? value.substring(0, 100) + '...' : value) : 'No data'}
        </div>
      )
    },
    {
      key: 'createdAt',
      label: 'Date',
      render: (value: string) => (
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {value ? new Date(value).toLocaleString() : 'N/A'}
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: string) => <StatusBadge status={value} />
    }
  ];

  // Don't restrict access based on role, just filter data for artists
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Chat Logs</h1>
        <Button variant="outline" onClick={() => setShowFilters(!showFilters)}>
          <SlidersHorizontal className="h-4 w-4 mr-2" />
          {showFilters ? 'Hide Filters' : 'Show Filters'}
        </Button>
      </div>
      
      {/* Stats Card */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-primary-100 dark:bg-primary-900 text-primary dark:text-primary-300">
                <MessageSquare size={24} />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Logs</p>
                <p className="text-2xl font-semibold">{data?.total || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Filters Section */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle>Filter Chat Logs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {currentUser?.role === 'admin' && (
                <div className="space-y-2">
                  <Label htmlFor="userId">User</Label>
                  <Select 
                    onValueChange={(value) => handleFilterChange('userId', value)}
                    value={filters.userId || ''}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All Users" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Users</SelectItem>
                      <SelectItem value="1">Admin User</SelectItem>
                      <SelectItem value="2">Artist User</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
              
              <div className="space-y-2">
                <Label htmlFor="agentId">Agent</Label>
                <Select 
                  onValueChange={(value) => handleFilterChange('agentId', value)}
                  value={filters.agentId || ''}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Agents" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Agents</SelectItem>
                    <SelectItem value="1">Style Advisor</SelectItem>
                    <SelectItem value="2">Appointment Bot</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="dateRange">Date Range</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="dateRange"
                      variant={"outline"}
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date?.from ? (
                        date.to ? (
                          <>
                            {format(date.from, "LLL dd, y")} -{" "}
                            {format(date.to, "LLL dd, y")}
                          </>
                        ) : (
                          format(date.from, "LLL dd, y")
                        )
                      ) : (
                        <span>Pick a date range</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      initialFocus
                      mode="range"
                      defaultMonth={date?.from}
                      selected={date}
                      onSelect={handleDateChange}
                      numberOfMonths={2}
                    />
                  </PopoverContent>
                </Popover>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select 
                  onValueChange={(value) => handleFilterChange('status', value)}
                  value={filters.status || ''}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="failed">Failed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="search">Search</Label>
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-400" />
                  <Input
                    id="search"
                    placeholder="Search in conversations..."
                    className="pl-9"
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    value={filters.search || ''}
                  />
                </div>
              </div>
            </div>
            
            <div className="mt-6 flex items-center justify-end space-x-4">
              <Button variant="outline" onClick={resetFilters}>Reset Filters</Button>
              <Button onClick={applyFilters}>Apply Filters</Button>
            </div>
          </CardContent>
        </Card>
      )}
      
      <DataTable 
        columns={columns}
        data={data?.logs || []}
        totalItems={data?.total || 0}
        currentPage={page}
        pageSize={pageSize}
        onPageChange={setPage}
        onPageSizeChange={setPageSize}
        isLoading={isLoading}
      />
    </div>
  );
};

export default Logs;
