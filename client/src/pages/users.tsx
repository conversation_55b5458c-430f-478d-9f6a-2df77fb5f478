import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getUsers, createUser, updateUser, deleteUser } from '../services/api';
import DataTable from '../components/shared/DataTable';
import { Button } from '@/components/ui/button';
import { Plus, Pencil, Trash } from 'lucide-react';
import UserModal from '../components/modals/UserModal';
import ConfirmDeleteModal from '../components/modals/ConfirmDeleteModal';
import { useToast } from '@/hooks/use-toast';
import StatusBadge from '@/components/shared/StatusBadge';
import { useAuth } from '../hooks/useAuth.tsx';

const Users = () => {
  const { user: currentUser } = useAuth();
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  
  const queryClient = useQueryClient();
  const { toast } = useToast();
  
  const { data, isLoading } = useQuery({
    queryKey: ['/api/users', { page, limit: pageSize }],
  });
  
  const createMutation = useMutation({
    mutationFn: createUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/users'] });
      toast({
        title: "User created successfully",
      });
    },
  });
  
  const updateMutation = useMutation({
    mutationFn: (data: any) => updateUser(data.id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/users'] });
      toast({
        title: "User updated successfully",
      });
    },
  });
  
  const deleteMutation = useMutation({
    mutationFn: deleteUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/users'] });
      toast({
        title: "User deleted successfully",
      });
    },
  });
  
  const handleAddUser = (userData: any) => {
    return createMutation.mutateAsync(userData);
  };
  
  const handleEditUser = (userData: any) => {
    if (!selectedUser) return Promise.reject(new Error('No user selected'));
    return updateMutation.mutateAsync({ ...userData, id: selectedUser.id });
  };
  
  const handleDeleteUser = () => {
    if (!selectedUser) return Promise.reject(new Error('No user selected'));
    return deleteMutation.mutateAsync(selectedUser.id);
  };
  
  const columns = [
    {
      key: 'name',
      label: 'Name',
      render: (_: any, row: any) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-8 w-8 rounded-full bg-primary text-white flex items-center justify-center">
            <span className="text-xs font-medium">{`${row.firstName?.charAt(0)}${row.lastName?.charAt(0)}`}</span>
          </div>
          <div className="ml-3">
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{`${row.firstName} ${row.lastName}`}</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">{row.email}</div>
          </div>
        </div>
      )
    },
    {
      key: 'username',
      label: 'Username',
    },
    {
      key: 'role',
      label: 'Role',
      render: (value: string) => <StatusBadge status={value} />
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: string) => <StatusBadge status={value} />
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_: any, row: any) => (
        <div className="flex items-center space-x-3">
          <Button 
            variant="ghost" 
            size="icon"
            onClick={() => {
              setSelectedUser(row);
              setIsEditModalOpen(true);
            }}
          >
            <Pencil className="h-4 w-4" />
          </Button>
          <Button 
            variant="ghost" 
            size="icon"
            onClick={() => {
              setSelectedUser(row);
              setIsDeleteModalOpen(true);
            }}
          >
            <Trash className="h-4 w-4 text-red-500" />
          </Button>
        </div>
      )
    }
  ];

  // Only allow access to admin users
  if (currentUser?.role !== 'admin') {
    return (
      <div className="text-center py-10">
        <h2 className="text-2xl font-bold mb-2">Access Restricted</h2>
        <p className="text-gray-600 dark:text-gray-400">
          You don't have permission to access the user management section.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">User Management</h1>
        <Button onClick={() => setIsAddModalOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add User
        </Button>
      </div>
      
      <DataTable 
        columns={columns}
        data={data?.users || []}
        totalItems={data?.total || 0}
        currentPage={page}
        pageSize={pageSize}
        onPageChange={setPage}
        onPageSizeChange={setPageSize}
        isLoading={isLoading}
      />
      
      {/* Add User Modal */}
      <UserModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={handleAddUser}
      />
      
      {/* Edit User Modal */}
      <UserModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedUser(null);
        }}
        onSubmit={handleEditUser}
        defaultValues={selectedUser}
        isEdit
      />
      
      {/* Delete Confirmation Modal */}
      <ConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setSelectedUser(null);
        }}
        onConfirm={handleDeleteUser}
        title="Delete User"
        description={`Are you sure you want to delete ${selectedUser?.firstName} ${selectedUser?.lastName}? This action cannot be undone.`}
      />
    </div>
  );
};

export default Users;
