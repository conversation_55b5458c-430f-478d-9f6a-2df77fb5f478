import { useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import { hybridAuth } from '../lib/hybridAuth';
import { Card, CardContent } from '@/components/ui/card';

const AuthCallback = () => {
  const [, setLocation] = useLocation();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const processCallback = async () => {
      try {
        // Get the authorization code from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const error = urlParams.get('error');

        if (error) {
          throw new Error(`OAuth error: ${error}`);
        }

        if (!code) {
          throw new Error('No authorization code received');
        }

        // Exchange code for token using hybrid auth
        const authData = await hybridAuth.handleOAuthCallback(code);
        
        // Store user data
        localStorage.setItem('auth-user', JSON.stringify({
          id: authData.user.id,
          username: authData.user.email.split('@')[0], // Use email prefix as username
          email: authData.user.email,
          firstName: authData.user.name.split(' ')[0] || '',
          lastName: authData.user.name.split(' ').slice(1).join(' ') || '',
          role: 'client' as const,
          status: 'active' as const
        }));

        setStatus('success');
        
        // Redirect to dashboard after a short delay
        setTimeout(() => {
          setLocation('/dashboard');
        }, 2000);

      } catch (err) {
        console.error('OAuth callback error:', err);
        setError(err instanceof Error ? err.message : 'Authentication failed');
        setStatus('error');
        
        // Redirect to login after error
        setTimeout(() => {
          setLocation('/login');
        }, 3000);
      }
    };

    processCallback();
  }, [setLocation]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
      <Card className="w-full max-w-md">
        <CardContent className="pt-8 pb-8 text-center">
          {status === 'loading' && (
            <>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Completing Sign In
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Please wait while we complete your Google authentication...
              </p>
            </>
          )}
          
          {status === 'success' && (
            <>
              <div className="h-12 w-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="h-6 w-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Sign In Successful!
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Redirecting you to the dashboard...
              </p>
            </>
          )}
          
          {status === 'error' && (
            <>
              <div className="h-12 w-12 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="h-6 w-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Authentication Failed
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {error || 'Something went wrong during authentication.'}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Redirecting you back to the login page...
              </p>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthCallback;
