import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getAgents, createAgent, updateAgent, deleteAgent } from '../services/api';
import DataTable from '../components/shared/DataTable';
import { Button } from '@/components/ui/button';
import { Plus, Pencil, Trash, Database } from 'lucide-react';
import AgentModal from '../components/modals/AgentModal';
import ConfirmDeleteModal from '../components/modals/ConfirmDeleteModal';
import { useToast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '../hooks/useAuth.tsx';
import { Card, CardContent } from '@/components/ui/card';

const Agents = () => {
  const { user: currentUser } = useAuth();
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<any>(null);
  
  const queryClient = useQueryClient();
  const { toast } = useToast();
  
  const { data, isLoading } = useQuery({
    queryKey: ['/api/agents', { page, limit: pageSize }],
  });
  
  const createMutation = useMutation({
    mutationFn: createAgent,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/agents'] });
      toast({
        title: "Agent created successfully",
      });
    },
  });
  
  const updateMutation = useMutation({
    mutationFn: (data: any) => updateAgent(data.id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/agents'] });
      toast({
        title: "Agent updated successfully",
      });
    },
  });
  
  const deleteMutation = useMutation({
    mutationFn: deleteAgent,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/agents'] });
      toast({
        title: "Agent deleted successfully",
      });
    },
  });
  
  const handleAddAgent = (agentData: any) => {
    return createMutation.mutateAsync(agentData);
  };
  
  const handleEditAgent = (agentData: any) => {
    if (!selectedAgent) return Promise.reject(new Error('No agent selected'));
    return updateMutation.mutateAsync({ ...agentData, id: selectedAgent.id });
  };
  
  const handleDeleteAgent = () => {
    if (!selectedAgent) return Promise.reject(new Error('No agent selected'));
    return deleteMutation.mutateAsync(selectedAgent.id);
  };
  
  const truncateText = (text: string, maxLength: number) => {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  };
  
  const columns = [
    {
      key: 'name',
      label: 'Name',
      render: (value: string) => (
        <div className="font-medium text-gray-900 dark:text-gray-100">{value}</div>
      )
    },
    {
      key: 'description',
      label: 'Description',
      render: (value: string) => truncateText(value, 60)
    },
    {
      key: 'systemPrompt',
      label: 'System Prompt',
      render: (value: string) => truncateText(value, 60)
    },
    {
      key: 'isActive',
      label: 'Status',
      render: (value: boolean) => (
        value ? 
          <Badge className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 hover:bg-green-100 dark:hover:bg-green-900">Active</Badge> : 
          <Badge className="bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800">Inactive</Badge>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_: any, row: any) => (
        <div className="flex items-center space-x-3">
          <Button 
            variant="ghost" 
            size="icon"
            onClick={() => {
              setSelectedAgent(row);
              setIsEditModalOpen(true);
            }}
          >
            <Pencil className="h-4 w-4" />
          </Button>
          <Button 
            variant="ghost" 
            size="icon"
            onClick={() => {
              setSelectedAgent(row);
              setIsDeleteModalOpen(true);
            }}
          >
            <Trash className="h-4 w-4 text-red-500" />
          </Button>
        </div>
      )
    }
  ];

  // Only allow access to admin users
  if (currentUser?.role !== 'admin') {
    return (
      <div className="text-center py-10">
        <h2 className="text-2xl font-bold mb-2">Access Restricted</h2>
        <p className="text-gray-600 dark:text-gray-400">
          You don't have permission to access the agent management section.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Agent Management</h1>
        <Button onClick={() => setIsAddModalOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Agent
        </Button>
      </div>
      
      {/* Quick guide */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <div className="p-3 rounded-full bg-primary-100 dark:bg-primary-900">
              <Database className="h-6 w-6 text-primary dark:text-primary-400" />
            </div>
            <div>
              <h3 className="text-lg font-medium mb-1">Managing Chatbot Agents</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm mb-2">
                Agents are chatbot personalities with specific system prompts that define how they interact with users.
                Each agent can be configured for different purposes like style advice, aftercare instructions, or appointment booking.
              </p>
              <ul className="text-sm text-gray-600 dark:text-gray-400 list-disc list-inside space-y-1">
                <li>Use clear, descriptive system prompts to guide agent behavior</li>
                <li>Inactive agents won't be available for user interactions</li>
                <li>Each agent should serve a specific, focused purpose</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <DataTable 
        columns={columns}
        data={data?.agents || []}
        totalItems={data?.total || 0}
        currentPage={page}
        pageSize={pageSize}
        onPageChange={setPage}
        onPageSizeChange={setPageSize}
        isLoading={isLoading}
      />
      
      {/* Add Agent Modal */}
      <AgentModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={handleAddAgent}
      />
      
      {/* Edit Agent Modal */}
      <AgentModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedAgent(null);
        }}
        onSubmit={handleEditAgent}
        defaultValues={selectedAgent}
        isEdit
      />
      
      {/* Delete Confirmation Modal */}
      <ConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setSelectedAgent(null);
        }}
        onConfirm={handleDeleteAgent}
        title="Delete Agent"
        description={`Are you sure you want to delete the "${selectedAgent?.name}" agent? This action cannot be undone.`}
      />
    </div>
  );
};

export default Agents;
