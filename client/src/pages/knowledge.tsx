import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  getKnowledgeBase, 
  createKnowledgeBaseItem, 
  updateKnowledgeBaseItem, 
  deleteKnowledgeBaseItem 
} from '../services/api';
import DataTable from '../components/shared/DataTable';
import { Button } from '@/components/ui/button';
import { Plus, Pencil, Trash, BookOpen, Filter } from 'lucide-react';
import KnowledgeBaseModal from '../components/modals/KnowledgeBaseModal';
import ConfirmDeleteModal from '../components/modals/ConfirmDeleteModal';
import { useToast } from '@/hooks/use-toast';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle,
  CardDescription 
} from '@/components/ui/card';
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '../hooks/useAuth.tsx';

const Knowledge = () => {
  const { user: currentUser } = useAuth();
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  
  const queryClient = useQueryClient();
  const { toast } = useToast();
  
  const { data, isLoading } = useQuery({
    queryKey: ['/api/knowledge', { page, limit: pageSize, category: activeCategory }],
  });
  
  const createMutation = useMutation({
    mutationFn: createKnowledgeBaseItem,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/knowledge'] });
      toast({
        title: "Knowledge base entry created successfully",
      });
    },
  });
  
  const updateMutation = useMutation({
    mutationFn: (data: any) => updateKnowledgeBaseItem(data.id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/knowledge'] });
      toast({
        title: "Knowledge base entry updated successfully",
      });
    },
  });
  
  const deleteMutation = useMutation({
    mutationFn: deleteKnowledgeBaseItem,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/knowledge'] });
      toast({
        title: "Knowledge base entry deleted successfully",
      });
    },
  });
  
  const handleAddItem = (itemData: any) => {
    // Add current user as the creator
    const dataWithUser = {
      ...itemData,
      createdById: currentUser?.id
    };
    return createMutation.mutateAsync(dataWithUser);
  };
  
  const handleEditItem = (itemData: any) => {
    if (!selectedItem) return Promise.reject(new Error('No item selected'));
    return updateMutation.mutateAsync({ ...itemData, id: selectedItem.id });
  };
  
  const handleDeleteItem = () => {
    if (!selectedItem) return Promise.reject(new Error('No item selected'));
    return deleteMutation.mutateAsync(selectedItem.id);
  };
  
  const handleCategoryChange = (category: string) => {
    setActiveCategory(category === 'all' ? null : category);
    setPage(1); // Reset to first page when changing category
  };
  
  const truncateText = (text: string, maxLength: number) => {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  };
  
  const columns = [
    {
      key: 'title',
      label: 'Title',
      render: (value: string) => (
        <div className="font-medium text-gray-900 dark:text-gray-100">{value}</div>
      )
    },
    {
      key: 'category',
      label: 'Category',
      render: (value: string) => (
        <Badge variant="outline" className="capitalize">{value}</Badge>
      )
    },
    {
      key: 'content',
      label: 'Content',
      render: (value: string) => truncateText(value, 100)
    },
    {
      key: 'createdAt',
      label: 'Created',
      render: (value: string) => (
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {new Date(value).toLocaleDateString()}
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_: any, row: any) => {
        // Determine if the current user can edit this item
        const canEdit = currentUser?.role === 'admin' || row.createdById === currentUser?.id;
        
        return (
          <div className="flex items-center space-x-3">
            {canEdit && (
              <>
                <Button 
                  variant="ghost" 
                  size="icon"
                  onClick={() => {
                    setSelectedItem(row);
                    setIsEditModalOpen(true);
                  }}
                >
                  <Pencil className="h-4 w-4" />
                </Button>
                <Button 
                  variant="ghost" 
                  size="icon"
                  onClick={() => {
                    setSelectedItem(row);
                    setIsDeleteModalOpen(true);
                  }}
                >
                  <Trash className="h-4 w-4 text-red-500" />
                </Button>
              </>
            )}
          </div>
        );
      }
    }
  ];

  // Available for both admin and artist roles
  const isAuthorized = currentUser?.role === 'admin' || currentUser?.role === 'artist';
  
  if (!isAuthorized) {
    return (
      <div className="text-center py-10">
        <h2 className="text-2xl font-bold mb-2">Access Restricted</h2>
        <p className="text-gray-600 dark:text-gray-400">
          You don't have permission to access the knowledge base section.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Knowledge Base</h1>
        <Button onClick={() => setIsAddModalOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Entry
        </Button>
      </div>
      
      {/* Guide Card */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <div className="p-3 rounded-full bg-primary-100 dark:bg-primary-900">
              <BookOpen className="h-6 w-6 text-primary dark:text-primary-400" />
            </div>
            <div>
              <h3 className="text-lg font-medium mb-1">Knowledge Base Management</h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                The knowledge base stores information that the chatbot can reference when answering questions.
                This includes aftercare instructions, tattoo style descriptions, FAQ, and other relevant information.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Category Tabs */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Categories</CardTitle>
            <Badge variant="outline" className="flex items-center gap-1">
              <Filter className="h-3 w-3" />
              {activeCategory ? <span className="capitalize">{activeCategory}</span> : 'All Categories'}
            </Badge>
          </div>
          <CardDescription>
            Filter knowledge base entries by category
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" onValueChange={handleCategoryChange}>
            <TabsList className="grid grid-cols-3 md:grid-cols-6">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="aftercare">Aftercare</TabsTrigger>
              <TabsTrigger value="styles">Styles</TabsTrigger>
              <TabsTrigger value="preparation">Preparation</TabsTrigger>
              <TabsTrigger value="faq">FAQ</TabsTrigger>
              <TabsTrigger value="techniques">Techniques</TabsTrigger>
            </TabsList>
          </Tabs>
        </CardContent>
      </Card>
      
      <DataTable 
        columns={columns}
        data={data?.items || []}
        totalItems={data?.total || 0}
        currentPage={page}
        pageSize={pageSize}
        onPageChange={setPage}
        onPageSizeChange={setPageSize}
        isLoading={isLoading}
      />
      
      {/* Add Knowledge Base Modal */}
      <KnowledgeBaseModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={handleAddItem}
      />
      
      {/* Edit Knowledge Base Modal */}
      <KnowledgeBaseModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedItem(null);
        }}
        onSubmit={handleEditItem}
        defaultValues={selectedItem}
        isEdit
      />
      
      {/* Delete Confirmation Modal */}
      <ConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setSelectedItem(null);
        }}
        onConfirm={handleDeleteItem}
        title="Delete Knowledge Base Entry"
        description={`Are you sure you want to delete "${selectedItem?.title}"? This action cannot be undone.`}
      />
    </div>
  );
};

export default Knowledge;
