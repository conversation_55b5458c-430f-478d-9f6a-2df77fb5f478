import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { DesignOptions, generateDesign, saveDesign } from '../services/designService';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '../hooks/useAuth';
import OpenAIAgentsClient from '@/services/openaiAgentsClient';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Download, Save, Wand2, Zap } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { ImageAnalyzer } from '@/components/design/ImageAnalyzer';
import { DesignAssistantChat } from '@/components/design/DesignAssistantChat';
import { getStyleOptions, getStyleByKeyword } from '../data/tattooStyles';
import FluxGenerator from '@/components/flux/FluxGenerator';

export default function DesignGenerator() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('guided');
  const [generatedDesign, setGeneratedDesign] = useState<{
    imageUrl: string;
    prompt?: string;
  } | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [useCustomPrompt, setUseCustomPrompt] = useState(false);
  const [customPrompt, setCustomPrompt] = useState('');
  const [designName, setDesignName] = useState('');
  
  // Handle prompt from image analyzer
  const handlePromptFromAnalyzer = (prompt: string) => {
    setCustomPrompt(prompt);
    setUseCustomPrompt(true);
    setActiveTab('advanced');
    
    toast({
      title: 'Recreation prompt copied',
      description: 'The recreation prompt has been set as your custom prompt.',
      variant: 'default',
    });
  };

  // Form setup
  const { register, handleSubmit, formState: { errors }, watch, setValue } = useForm<DesignOptions>({
    defaultValues: {
      style: '',
      technique: '',
      placement: '',
      colorPalette: '',
      subjectMatter: '',
      clientIntent: '',
      artistSpecialty: '',
    }
  });

  // Mutation for generating design (legacy)
  const designMutation = useMutation({
    mutationFn: async ({ options, customPrompt }: { options: DesignOptions, customPrompt?: string }) => {
      setIsGenerating(true);
      try {
        return await generateDesign(options, customPrompt);
      } finally {
        setIsGenerating(false);
      }
    },
    onSuccess: (data) => {
      setGeneratedDesign(data);
      toast({
        title: 'Design generated successfully',
        description: 'Your tattoo design has been created.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Failed to generate design',
        description: error.message,
        variant: 'destructive',
      });
    }
  });

  // Mutation for OpenAI Agents SDK enhanced design generation
  const agentsDesignMutation = useMutation({
    mutationFn: async ({ prompt, style, bodyZone }: { prompt: string, style?: string, bodyZone?: string }) => {
      setIsGenerating(true);
      try {
        return await OpenAIAgentsClient.generateEnhancedTattooDesign(prompt, style, bodyZone);
      } finally {
        setIsGenerating(false);
      }
    },
    onSuccess: (data) => {
      setGeneratedDesign({ imageUrl: data.imageUrl, prompt: data.prompt });
      toast({
        title: 'Enhanced design generated',
        description: 'Your tattoo design has been created using OpenAI Agents SDK.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Failed to generate enhanced design',
        description: error.message,
        variant: 'destructive',
      });
    }
  });

  // Mutation for saving design
  const saveMutation = useMutation({
    mutationFn: async ({ name, imageUrl, options }: { name: string, imageUrl: string, options: DesignOptions }) => {
      return await saveDesign({ name, imageUrl, options });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/designs'] });
      toast({
        title: 'Design saved successfully',
        description: 'Your tattoo design has been saved to your collection.',
        variant: 'default',
      });
      setDesignName('');
    },
    onError: (error: Error) => {
      toast({
        title: 'Failed to save design',
        description: error.message,
        variant: 'destructive',
      });
    }
  });
  


  const onSubmit = (data: DesignOptions) => {
    designMutation.mutate({ 
      options: data,
      customPrompt: useCustomPrompt ? customPrompt : undefined
    });
  };

  // Enhanced AI submission handler
  const handleEnhancedAISubmit = () => {
    const formData = watch();
    const prompt = useCustomPrompt ? customPrompt : formData.subjectMatter || 'creative tattoo design';
    
    agentsDesignMutation.mutate({
      prompt,
      style: formData.style,
      bodyZone: formData.placement
    });
  };

  const handleSaveDesign = () => {
    if (!generatedDesign || !generatedDesign.imageUrl) {
      toast({
        title: 'No design to save',
        description: 'Please generate a design first before saving.',
        variant: 'destructive',
      });
      return;
    }

    if (!designName.trim()) {
      toast({
        title: 'Design name required',
        description: 'Please provide a name for your design.',
        variant: 'destructive',
      });
      return;
    }

    saveMutation.mutate({
      name: designName,
      imageUrl: generatedDesign.imageUrl,
      options: watch()
    });
  };

  const handleDownload = async () => {
    if (!generatedDesign || !generatedDesign.imageUrl) return;

    try {
      // Fetch the image
      const response = await fetch(generatedDesign.imageUrl);
      const blob = await response.blob();

      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `tattoo-design-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: 'Design downloaded',
        description: 'The tattoo design has been downloaded to your device.',
        variant: 'default',
      });
    } catch (error) {
      toast({
        title: 'Download failed',
        description: 'Failed to download the image. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="container py-6 space-y-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Tattoo Design Generator</h1>
        <p className="text-muted-foreground">
          Create custom tattoo designs using AI. Specify your preferences and generate unique tattoo artwork.
        </p>
      </div>

      <Tabs defaultValue="guided" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5 mb-6">
          <TabsTrigger value="guided">Guided Design</TabsTrigger>
          <TabsTrigger value="enhanced-ai">Enhanced AI</TabsTrigger>
          <TabsTrigger value="flux">
            <Zap className="h-4 w-4 mr-1" />
            FLUX.1
          </TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
          <TabsTrigger value="analyzer">Image Analyzer</TabsTrigger>
        </TabsList>

        {/* FLUX gets full width */}
        {activeTab === 'flux' ? (
          <TabsContent value="flux">
            <FluxGenerator />
          </TabsContent>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Panel - Design Configuration */}
            <div className="space-y-6">

            <TabsContent value="guided" className="space-y-4 mt-4">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div className="space-y-4">
                  {/* Subject Matter */}
                  <div className="space-y-2">
                    <Label htmlFor="subjectMatter">Subject Matter</Label>
                    <Select
                      onValueChange={(value) => setValue("subjectMatter", value)}
                      defaultValue={watch("subjectMatter")}
                    >
                      <SelectTrigger id="subjectMatter">
                        <SelectValue placeholder="Select subject matter" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="floral">Floral</SelectItem>
                        <SelectItem value="animal">Animal</SelectItem>
                        <SelectItem value="abstract">Abstract</SelectItem>
                        <SelectItem value="symbolic">Symbolic</SelectItem>
                        <SelectItem value="portrait">Portrait</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.subjectMatter && (
                      <p className="text-sm text-red-500">{errors.subjectMatter.message}</p>
                    )}
                  </div>

                  {/* Style */}
                  <div className="space-y-2">
                    <Label htmlFor="style">Tattoo Style</Label>
                    <Select
                      onValueChange={(value) => setValue("style", value)}
                      defaultValue={watch("style")}
                    >
                      <SelectTrigger id="style">
                        <SelectValue placeholder="Select tattoo style" />
                      </SelectTrigger>
                      <SelectContent>
                        {getStyleOptions().map((style) => (
                          <SelectItem key={style.value} value={style.value}>
                            <div className="flex flex-col">
                              <span className="font-medium">{style.label}</span>
                              <span className="text-xs text-muted-foreground">{style.description}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.style && (
                      <p className="text-sm text-red-500">{errors.style.message}</p>
                    )}
                    {/* Show additional style information when selected */}
                    {watch("style") && (
                      <div className="p-3 bg-muted rounded-md">
                        <div className="text-sm">
                          <strong>Style Details:</strong>
                          <br />
                          {getStyleByKeyword(watch("style"))?.detailedDescription}
                          <br />
                          <br />
                          <strong>Complexity Level:</strong> {getStyleByKeyword(watch("style"))?.guidanceLevel}
                          <br />
                          <strong>Attributes:</strong> {getStyleByKeyword(watch("style"))?.attributesAndTags}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Placement */}
                  <div className="space-y-2">
                    <Label htmlFor="placement">Placement</Label>
                    <Select
                      onValueChange={(value) => setValue("placement", value)}
                      defaultValue={watch("placement")}
                    >
                      <SelectTrigger id="placement">
                        <SelectValue placeholder="Select placement" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="forearm">Forearm</SelectItem>
                        <SelectItem value="upper-arm">Upper Arm</SelectItem>
                        <SelectItem value="shoulder">Shoulder</SelectItem>
                        <SelectItem value="back">Back</SelectItem>
                        <SelectItem value="chest">Chest</SelectItem>
                        <SelectItem value="leg">Leg</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.placement && (
                      <p className="text-sm text-red-500">{errors.placement.message}</p>
                    )}
                  </div>

                  {/* Color Palette */}
                  <div className="space-y-2">
                    <Label htmlFor="colorPalette">Color Palette</Label>
                    <Select
                      onValueChange={(value) => setValue("colorPalette", value)}
                      defaultValue={watch("colorPalette")}
                    >
                      <SelectTrigger id="colorPalette">
                        <SelectValue placeholder="Select color palette" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="blackAndGray">Black & Gray</SelectItem>
                        <SelectItem value="vibrant">Vibrant Colors</SelectItem>
                        <SelectItem value="pastel">Pastel</SelectItem>
                        <SelectItem value="monochrome">Monochrome</SelectItem>
                        <SelectItem value="neon">Neon</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.colorPalette && (
                      <p className="text-sm text-red-500">{errors.colorPalette.message}</p>
                    )}
                  </div>

                  {/* Tattoo Meaning/Intent */}
                  <div className="space-y-2">
                    <Label htmlFor="clientIntent">What does this tattoo mean to you? (Optional)</Label>
                    <Textarea
                      id="clientIntent"
                      placeholder="E.g., Symbolizes strength, family connection, etc."
                      {...register("clientIntent")}
                      className="resize-none"
                      rows={2}
                    />
                  </div>

                  {/* Advanced Options - Technique */}
                  <div className="space-y-2">
                    <Label htmlFor="technique">Technique (Optional)</Label>
                    <Input
                      id="technique"
                      placeholder="E.g., dotwork, linework, etc."
                      {...register("technique")}
                    />
                  </div>

                  {/* Use Custom Prompt Toggle */}
                  <div className="flex items-center space-x-2 pt-4">
                    <Switch
                      id="custom-prompt"
                      checked={useCustomPrompt}
                      onCheckedChange={setUseCustomPrompt}
                    />
                    <Label htmlFor="custom-prompt">Use custom prompt</Label>
                  </div>

                  {/* Custom Prompt */}
                  {useCustomPrompt && (
                    <div className="space-y-2">
                      <Label htmlFor="customPrompt">Custom Prompt</Label>
                      <Textarea
                        id="customPrompt"
                        placeholder="Describe the tattoo design you want in detail..."
                        value={customPrompt}
                        onChange={(e) => setCustomPrompt(e.target.value)}
                        className="resize-none"
                        rows={4}
                      />
                    </div>
                  )}
                </div>

                <Button 
                  type="submit" 
                  className="w-full" 
                  disabled={isGenerating}
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Wand2 className="mr-2 h-4 w-4" />
                      Generate Design
                    </>
                  )}
                </Button>
              </form>
            </TabsContent>

            <TabsContent value="enhanced-ai" className="space-y-4 mt-4">
              <div className="h-[600px] flex flex-col">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold">AI Design Assistant</h3>
                    <p className="text-sm text-muted-foreground">
                      Chat with your personal tattoo design expert
                    </p>
                  </div>
                  <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                    Real-time
                  </div>
                </div>
                
                <DesignAssistantChat />
              </div>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4 mt-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="advancedPrompt">Detailed Design Prompt</Label>
                  <Textarea
                    id="advancedPrompt"
                    placeholder="Describe your perfect tattoo in detail..."
                    value={customPrompt}
                    onChange={(e) => setCustomPrompt(e.target.value)}
                    className="resize-none"
                    rows={10}
                  />
                </div>

                <Button 
                  onClick={() => {
                    setUseCustomPrompt(true);
                    designMutation.mutate({ 
                      options: { 
                        style: '', 
                        technique: '', 
                        placement: '', 
                        colorPalette: '', 
                        subjectMatter: '', 
                        clientIntent: '', 
                        artistSpecialty: '' 
                      }, 
                      customPrompt 
                    });
                  }}
                  className="w-full" 
                  disabled={isGenerating}
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Wand2 className="mr-2 h-4 w-4" />
                      Generate with Custom Prompt
                    </>
                  )}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="analyzer" className="space-y-4 mt-4">
              <ImageAnalyzer onPromptGenerated={handlePromptFromAnalyzer} />
            </TabsContent>
            </div>

            {/* Right Panel - Design Preview */}
            <div className="space-y-6">
              <Card className="border-2 border-dashed">
                <CardContent className="p-6 min-h-[400px] flex flex-col items-center justify-center">
              {isGenerating ? (
                <div className="flex flex-col items-center justify-center h-full">
                  <Loader2 className="h-12 w-12 animate-spin text-primary" />
                  <p className="mt-4 text-center text-muted-foreground">
                    Creating your tattoo design...
                    <br />
                    This may take up to 30 seconds.
                  </p>
                </div>
              ) : generatedDesign ? (
                <div className="flex flex-col items-center w-full">
                  <img
                    src={generatedDesign.imageUrl}
                    alt="Generated Tattoo Design"
                    className="max-w-full max-h-[400px] object-contain rounded-md"
                  />
                  {generatedDesign.prompt && (
                    <div className="mt-4 w-full">
                      <Label className="text-sm text-muted-foreground">Generated from prompt:</Label>
                      <p className="text-xs text-muted-foreground mt-1 bg-muted p-2 rounded">
                        {generatedDesign.prompt}
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center">
                  <Wand2 className="h-16 w-16 mx-auto text-muted-foreground/50" />
                  <p className="mt-4 text-muted-foreground">
                    Your design will appear here.
                    <br />
                    Fill out the form and click "Generate Design".
                  </p>
                </div>
              )}
                </CardContent>
              </Card>

              {generatedDesign && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="designName">Design Name</Label>
                    <Input
                      id="designName"
                      placeholder="Enter a name for your design"
                      value={designName}
                      onChange={(e) => setDesignName(e.target.value)}
                    />
                  </div>
                  <div className="flex gap-4">
                    <Button
                      variant="default"
                      className="flex-1"
                      onClick={handleSaveDesign}
                      disabled={saveMutation.isPending}
                    >
                      {saveMutation.isPending ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          Save Design
                        </>
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={handleDownload}
                    >
                      <Download className="mr-2 h-4 w-4" />
                      Download
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </Tabs>
    </div>
  );
}