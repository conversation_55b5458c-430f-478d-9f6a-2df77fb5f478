import { useState, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Upload, Camera, Loader2, Download, Palette, FileImage, Package, Scan, Zap } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import CameraScanner from "@/components/3d/CameraScanner";

type BodyPart = "forearm" | "calf" | "torso" | "hand";
type WorkflowStep = "scan" | "imaging" | "preview" | "export";

interface ScanState {
  photo: string | null;
  depthPng: string | null;
  maskPng: string | null;
  uvPng: string | null;
  normalPng: string | null;
}

interface ExportState {
  stencilUrl: string | null;
  svgUrl: string | null;
  palette: string[] | null;
  zipUrl: string | null;
}

export default function ThreeDTryOnPage() {
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState<WorkflowStep>("scan");
  const [bodyPart, setBodyPart] = useState<BodyPart>("forearm");
  const [scanState, setScanState] = useState<ScanState>({
    photo: null,
    depthPng: null,
    maskPng: null,
    uvPng: null,
    normalPng: null
  });
  const [exportState, setExportState] = useState<ExportState>({
    stencilUrl: null,
    svgUrl: null,
    palette: null,
    zipUrl: null
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentTraceId, setCurrentTraceId] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      const photoUrl = e.target?.result as string;
      setScanState(prev => ({ ...prev, photo: photoUrl }));

      setIsProcessing(true);
      try {
        const response = await fetch('/api/3d/scan/ingest', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ photo: photoUrl, bodyPart })
        });

        const result = await response.json();
        if (result.success) {
          setScanState(prev => ({
            ...prev,
            depthPng: result.depthPng,
            maskPng: result.maskPng
          }));
          setCurrentTraceId(result.traceId);
          toast({
            title: "Scan Complete",
            description: "3D depth mapping successful"
          });
        } else {
          throw new Error(result.error);
        }
      } catch (error) {
        toast({
          title: "Scan Failed",
          description: error instanceof Error ? error.message : "Unknown error occurred",
          variant: "destructive"
        });
      } finally {
        setIsProcessing(false);
      }
    };
    reader.readAsDataURL(file);
  };

  const handleUVUnwrap = async () => {
    if (!scanState.depthPng || !scanState.maskPng) return;

    setIsProcessing(true);
    try {
      const response = await fetch('/api/3d/scan/unwrap', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          depthPng: scanState.depthPng, 
          maskPng: scanState.maskPng 
        })
      });

      const result = await response.json();
      if (result.success) {
        setScanState(prev => ({
          ...prev,
          uvPng: result.uvPng,
          normalPng: result.normalPng
        }));
        toast({
          title: "UV Mapping Complete",
          description: "Ready for 3D preview"
        });
        setCurrentStep("preview");
      }
    } catch (error) {
      toast({
        title: "UV Mapping Failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleGenerateStencil = async () => {
    if (!scanState.uvPng) return;

    setIsProcessing(true);
    try {
      const response = await fetch('/api/3d/export/stencil', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ bakedTexture: scanState.uvPng })
      });

      const result = await response.json();
      if (result.success) {
        setExportState(prev => ({
          ...prev,
          stencilUrl: result.stencilPng,
          svgUrl: result.svgVector
        }));
        toast({
          title: "Stencil Generated",
          description: "Professional artist stencil ready"
        });
      }
    } catch (error) {
      toast({
        title: "Stencil Generation Failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleGeneratePalette = async () => {
    if (!scanState.uvPng) return;

    setIsProcessing(true);
    try {
      const response = await fetch('/api/3d/export/palette', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ tattooImage: scanState.uvPng })
      });

      const result = await response.json();
      if (result.success) {
        setExportState(prev => ({
          ...prev,
          palette: result.palette
        }));
        toast({
          title: "Color Palette Extracted",
          description: `Found ${result.palette.length} colors`
        });
      }
    } catch (error) {
      toast({
        title: "Palette Generation Failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCreatePackage = async () => {
    if (!exportState.stencilUrl || !exportState.svgUrl || !exportState.palette) {
      toast({
        title: "Missing Export Data",
        description: "Generate stencil and palette first",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);
    try {
      const response = await fetch('/api/3d/export/package', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          stencilPng: exportState.stencilUrl,
          svgVector: exportState.svgUrl,
          palette: exportState.palette,
          bakedTexture: scanState.uvPng,
          originalPhoto: scanState.photo
        })
      });

      const result = await response.json();
      if (result.success) {
        setExportState(prev => ({
          ...prev,
          zipUrl: result.zipUrl
        }));
        toast({
          title: "Artist Package Ready",
          description: "Complete export package created"
        });
      }
    } catch (error) {
      toast({
        title: "Package Creation Failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">3D Tattoo Try-On</h1>
        <p className="text-muted-foreground">
          Revolutionary 3D scanning technology with privacy-first local processing
        </p>
      </div>

      <Tabs defaultValue="live-scanner" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="live-scanner" className="flex items-center gap-2">
            <Scan className="h-4 w-4" />
            Live Scanner
          </TabsTrigger>
          <TabsTrigger value="scan" className="flex items-center gap-2">
            <Camera className="h-4 w-4" />
            Upload Scan
          </TabsTrigger>
          <TabsTrigger value="imaging" disabled>
            <FileImage className="h-4 w-4" />
            Imaging
          </TabsTrigger>
          <TabsTrigger value="preview" disabled={!scanState.uvPng}>
            <Zap className="h-4 w-4" />
            Preview
          </TabsTrigger>
          <TabsTrigger value="export" disabled={!scanState.uvPng}>
            <Package className="h-4 w-4" />
            Export
          </TabsTrigger>
        </TabsList>

        <TabsContent value="live-scanner">
          <CameraScanner />
        </TabsContent>

        <TabsContent value="scan" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Body Part Scanning</CardTitle>
              <CardDescription>
                Capture a photo to generate 3D depth maps and UV unwrapping
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="bodyPart">Body Part</Label>
                <Select value={bodyPart} onValueChange={(value) => setBodyPart(value as BodyPart)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select body part" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="forearm">Forearm</SelectItem>
                    <SelectItem value="calf">Calf</SelectItem>
                    <SelectItem value="torso">Torso</SelectItem>
                    <SelectItem value="hand">Hand</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Photo Upload</Label>
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                  {scanState.photo ? (
                    <img src={scanState.photo} alt="Uploaded" className="max-w-full max-h-64 mx-auto rounded" />
                  ) : (
                    <div className="space-y-2">
                      <Upload className="h-12 w-12 mx-auto text-muted-foreground" />
                      <p>Click to upload a photo</p>
                    </div>
                  )}
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                  <Button 
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isProcessing}
                    className="mt-4"
                  >
                    {isProcessing ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4 mr-2" />
                        Upload Photo
                      </>
                    )}
                  </Button>
                </div>
              </div>

              {scanState.depthPng && scanState.maskPng && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Depth Map</Label>
                    <img src={scanState.depthPng} alt="Depth Map" className="w-full rounded border" />
                  </div>
                  <div>
                    <Label>Binary Mask</Label>
                    <img src={scanState.maskPng} alt="Binary Mask" className="w-full rounded border" />
                  </div>
                </div>
              )}

              {scanState.depthPng && !scanState.uvPng && (
                <Button 
                  onClick={handleUVUnwrap} 
                  disabled={isProcessing}
                  className="w-full"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Generating UV Maps...
                    </>
                  ) : (
                    "Generate UV Mapping"
                  )}
                </Button>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>3D Preview</CardTitle>
              <CardDescription>
                Ray-traced tattoo overlay with realistic lighting and skin curvature
              </CardDescription>
            </CardHeader>
            <CardContent>
              {scanState.uvPng && scanState.normalPng ? (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>UV Texture Map</Label>
                    <img src={scanState.uvPng} alt="UV Map" className="w-full rounded border" />
                  </div>
                  <div>
                    <Label>Normal Map</Label>
                    <img src={scanState.normalPng} alt="Normal Map" className="w-full rounded border" />
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  Complete the scan step to preview your 3D model
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="export" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Professional Export</CardTitle>
              <CardDescription>
                Generate artist-ready stencils, color palettes, and complete packages
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button onClick={handleGenerateStencil} disabled={isProcessing || !scanState.uvPng}>
                  {isProcessing ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <FileImage className="h-4 w-4 mr-2" />}
                  Generate Stencil
                </Button>
                <Button onClick={handleGeneratePalette} disabled={isProcessing || !scanState.uvPng}>
                  {isProcessing ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Palette className="h-4 w-4 mr-2" />}
                  Extract Palette
                </Button>
                <Button onClick={handleCreatePackage} disabled={isProcessing || !exportState.stencilUrl}>
                  {isProcessing ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Package className="h-4 w-4 mr-2" />}
                  Create Package
                </Button>
              </div>

              {exportState.stencilUrl && (
                <div>
                  <Label>Artist Stencil</Label>
                  <img src={exportState.stencilUrl} alt="Stencil" className="w-full max-w-md rounded border" />
                </div>
              )}

              {exportState.palette && (
                <div>
                  <Label>Color Palette</Label>
                  <div className="flex gap-2 mt-2">
                    {exportState.palette.map((color, index) => (
                      <div key={index} className="flex flex-col items-center">
                        <div 
                          className="w-12 h-12 rounded border" 
                          style={{ backgroundColor: color }}
                        />
                        <Badge variant="outline" className="mt-1 text-xs">
                          {color}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {exportState.zipUrl && (
                <div className="border rounded-lg p-4 bg-muted/50">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Artist Package Ready</h4>
                      <p className="text-sm text-muted-foreground">
                        Complete export with stencil, vectors, and color specifications
                      </p>
                    </div>
                    <Button asChild>
                      <a href={exportState.zipUrl} download>
                        <Download className="h-4 w-4 mr-2" />
                        Download Package
                      </a>
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {currentTraceId && (
        <Card className="mt-6">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">Processing Trace ID</p>
                <p className="text-xs text-muted-foreground">{currentTraceId}</p>
              </div>
              <Badge variant="outline">Privacy-First Local Processing</Badge>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}