import { useState, useRef, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth.tsx';
import { io, Socket } from 'socket.io-client';
import { ChatInterface } from '@/components/chat/ChatInterface';
import { WebRTCVoiceChat } from '@/components/voice/WebRTCVoiceChat';
import { ALL_AGENTS, AGENT_TYPES } from '../services/agents/agentDefinitions';

// Message type
interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  agentId: string;
  timestamp: Date;
  imageUrl?: string;
  isStreaming?: boolean;
}

// Session type
interface Session {
  id: string;
  messages: Message[];
  currentAgentId: string;
}

const Chat = () => {
  const { user } = useAuth();
  
  // State management
  const [activeSession, setActiveSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [socket, setSocket] = useState<Socket | null>(null);
  const messageEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isVoiceSessionActive, setIsVoiceSessionActive] = useState(false);
  const [voiceSessionStatus, setVoiceSessionStatus] = useState<'idle' | 'connecting' | 'connected' | 'error'>('idle');
  const [webrtcConnection, setWebrtcConnection] = useState<any>(null);

  // Initialize WebSocket connection and session
  useEffect(() => {
    // Create WebSocket connection
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const socketUrl = `${protocol}//${window.location.host}`;
    const newSocket = io(socketUrl);
    setSocket(newSocket);

    // Create a new session with manager agent
    const sessionId = `session_${Date.now()}`;
    const newSession: Session = {
      id: sessionId,
      messages: [
        {
          id: `msg_${Date.now()}`,
          role: 'system',
          content: 'Welcome to the Tattoo Multi-Agent System. How can I assist you today?',
          agentId: AGENT_TYPES.MANAGER,
          timestamp: new Date()
        }
      ],
      currentAgentId: AGENT_TYPES.MANAGER
    };
    
    setActiveSession(newSession);
    
    // Join the chat room
    newSocket.emit('chat:start', { sessionId });
    
    // Set up socket event listeners
    newSocket.on('chat:started', (data) => {
      console.log('Chat started:', data);
    });
    
    // Handle streaming messages
    let streamingMessageId: string | null = null;
    
    // Stream start - initialize empty message
    newSocket.on('chat:stream:start', (data) => {
      if (newSession.id === data.sessionId) {
        streamingMessageId = data.messageId;
        
        const streamingMessage: Message = {
          id: streamingMessageId || `msg_${Date.now()}_streaming`,
          role: 'assistant',
          content: '',
          agentId: data.agentId,
          timestamp: new Date(data.timestamp),
          isStreaming: true
        };
        
        setActiveSession(prevSession => {
          if (!prevSession) return prevSession;
          return {
            ...prevSession,
            messages: [...prevSession.messages, streamingMessage]
          };
        });
      }
    });
    
    // Stream chunk - append to existing message
    newSocket.on('chat:stream:chunk', (data) => {
      if (newSession.id === data.sessionId && data.messageId === streamingMessageId) {
        setActiveSession(prevSession => {
          if (!prevSession) return prevSession;
          
          const updatedMessages = prevSession.messages.map(msg => {
            if (msg.id === streamingMessageId) {
              return {
                ...msg,
                content: msg.content + data.chunk
              };
            }
            return msg;
          });
          
          return {
            ...prevSession,
            messages: updatedMessages
          };
        });
      }
    });
    
    // Stream end - finalize message
    newSocket.on('chat:stream:end', (data) => {
      if (newSession.id === data.sessionId && data.messageId === streamingMessageId) {
        setActiveSession(prevSession => {
          if (!prevSession) return prevSession;
          
          const updatedMessages = prevSession.messages.map(msg => {
            if (msg.id === streamingMessageId) {
              return {
                ...msg,
                isStreaming: false
              };
            }
            return msg;
          });
          
          return {
            ...prevSession,
            messages: updatedMessages
          };
        });
        
        streamingMessageId = null;
        setIsLoading(false);
      }
    });
    
    // Legacy non-streaming response (for backward compatibility)
    newSocket.on('chat:response', (data) => {
      if (newSession.id === data.sessionId) {
        const aiMessage: Message = {
          id: `msg_${Date.now()}_ai`,
          role: 'assistant',
          content: data.message,
          agentId: data.agentId,
          timestamp: new Date(data.timestamp)
        };
        
        setActiveSession(prevSession => {
          if (!prevSession) return prevSession;
          return {
            ...prevSession,
            messages: [...prevSession.messages, aiMessage]
          };
        });
        
        setIsLoading(false);
      }
    });

    // Error handling
    newSocket.on('chat:error', (data) => {
      if (newSession.id === data.sessionId) {
        setActiveSession(prevSession => {
          if (!prevSession) return prevSession;
          return {
            ...prevSession,
            messages: [...prevSession.messages, {
              id: `msg_${Date.now()}_error`,
              role: 'system',
              content: `Error: ${data.error}`,
              agentId: prevSession.currentAgentId,
              timestamp: new Date()
            }]
          };
        });
      }
    });
    
    // Clean up on unmount
    return () => {
      if (newSocket) {
        newSocket.disconnect();
      }
    };
  }, []);

  // Send message to AI
  const handleSendMessage = async (messageContent: string, imageUrl?: string) => {
    if (!activeSession || !socket) return;
    
    // Add user message
    const userMessage: Message = {
      id: `msg_${Date.now()}_user`,
      role: 'user',
      content: messageContent,
      agentId: activeSession.currentAgentId,
      timestamp: new Date(),
      imageUrl: imageUrl
    };
    
    // Add to UI
    setActiveSession(prevSession => {
      if (!prevSession) return prevSession;
      return {
        ...prevSession,
        messages: [...prevSession.messages, userMessage]
      };
    });
    
    setIsLoading(true);
    
    try {
      // Send via WebSocket
      socket.emit('chat:message', {
        sessionId: activeSession.id,
        message: userMessage.content,
        agentId: activeSession.currentAgentId,
        imageUrl: userMessage.imageUrl
      });
    } catch (error) {
      console.error('Error sending message:', error);
      
      // Add error message to conversation
      setActiveSession(prevSession => {
        if (!prevSession) return prevSession;
        return {
          ...prevSession,
          messages: [...prevSession.messages, {
            id: `msg_${Date.now()}_error`,
            role: 'system',
            content: 'Sorry, there was an error sending your message. Please try again.',
            agentId: prevSession.currentAgentId,
            timestamp: new Date()
          }]
        };
      });
      
      setIsLoading(false);
    }
  };

  // Handle agent switching
  const handleAgentSwitch = (agentId: string) => {
    if (!activeSession || !socket) return;
    
    // Add system message for agent switch
    const switchMessage: Message = {
      id: `msg_${Date.now()}_switch`,
      role: 'system',
      content: `Switching to ${getAgentName(agentId)} agent...`,
      agentId,
      timestamp: new Date()
    };
    
    // Update UI
    setActiveSession(prevSession => {
      if (!prevSession) return prevSession;
      return {
        ...prevSession,
        messages: [...prevSession.messages, switchMessage],
        currentAgentId: agentId
      };
    });
    
    setIsLoading(true);
    
    try {
      // Send introduction request via socket
      socket.emit('chat:message', {
        sessionId: activeSession.id,
        message: 'Please introduce yourself briefly and explain how you can help.',
        agentId: agentId
      });
    } catch (error) {
      console.error('Error switching agent:', error);
      setIsLoading(false);
    }
  };

  // Voice message handling (voice-to-text)
  const handleVoiceMessage = async (audioData: string) => {
    if (!activeSession || !socket) return;
    
    try {
      const response = await fetch('/api/ai/transcribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          audioData: audioData,
          format: 'webm'
        }),
      });

      if (!response.ok) {
        throw new Error(`Transcription failed: ${response.status}`);
      }

      const data = await response.json();
      const transcribedText = data.text || data.transcription || '';
      
      if (transcribedText.trim()) {
        handleSendMessage(transcribedText.trim());
      }
    } catch (error) {
      console.error('Voice processing error:', error);
    }
  };

  // Start real-time voice session
  const handleStartVoiceSession = async () => {
    setVoiceSessionStatus('connecting');
    setIsVoiceSessionActive(true);
    
    try {
      // Get WebRTC token from our backend
      const tokenResponse = await fetch('/api/realtime/token');
      if (!tokenResponse.ok) {
        throw new Error('Failed to get WebRTC token');
      }
      
      const tokenData = await tokenResponse.json();
      
      // Connect to OpenAI Realtime API
      const pc = new RTCPeerConnection();
      
      // Add audio track for microphone
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => pc.addTrack(track, stream));
      
      // Handle incoming audio from AI
      pc.ontrack = (event) => {
        const audioElement = new Audio();
        audioElement.srcObject = event.streams[0];
        audioElement.play();
      };
      
      // Create offer and connect
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);
      
      // Send offer to OpenAI (simplified - actual implementation would use WebSocket)
      // For now, simulate connection
      setTimeout(() => {
        setVoiceSessionStatus('connected');
        
        // Add AI greeting message
        if (activeSession) {
          const greetingMessage: Message = {
            id: `msg_${Date.now()}_greeting`,
            role: 'assistant',
            content: "Hey, how ya doing? I'm here to help you with all your tattoo needs.",
            agentId: activeSession.currentAgentId,
            timestamp: new Date()
          };
          
          setActiveSession(prevSession => {
            if (!prevSession) return prevSession;
            return {
              ...prevSession,
              messages: [...prevSession.messages, greetingMessage]
            };
          });
        }
      }, 2000);
      
      setWebrtcConnection(pc);
      
    } catch (error) {
      console.error('Error starting voice session:', error);
      setVoiceSessionStatus('error');
      setIsVoiceSessionActive(false);
    }
  };

  // End voice session
  const handleEndVoiceSession = () => {
    if (webrtcConnection) {
      webrtcConnection.close();
      setWebrtcConnection(null);
    }
    
    setIsVoiceSessionActive(false);
    setVoiceSessionStatus('idle');
    
    // Add session end message
    if (activeSession) {
      const endMessage: Message = {
        id: `msg_${Date.now()}_end`,
        role: 'system',
        content: 'Voice session ended',
        agentId: activeSession.currentAgentId,
        timestamp: new Date()
      };
      
      setActiveSession(prevSession => {
        if (!prevSession) return prevSession;
        return {
          ...prevSession,
          messages: [...prevSession.messages, endMessage]
        };
      });
    }
  };

  // Handle image upload
  const handleImageUpload = (file: File) => {
    const reader = new FileReader();
    reader.onload = () => {
      setImageUrl(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  // Get agent name helper
  const getAgentName = (agentId: string) => {
    const agent = ALL_AGENTS.find(a => a.id === agentId);
    return agent?.name || 'Assistant';
  };

  // Prepare agents for ChatInterface
  const agentOptions = ALL_AGENTS.map(agent => ({
    id: agent.id,
    name: agent.name,
    color: 'blue'
  }));

  if (!activeSession) {
    return <div>Loading...</div>;
  }

  return (
    <div className="h-[calc(100vh-80px)] bg-background">
      <ChatInterface
        messages={activeSession.messages}
        onSendMessage={handleSendMessage}
        onVoiceMessage={handleVoiceMessage}
        onStartVoiceSession={handleStartVoiceSession}
        onEndVoiceSession={handleEndVoiceSession}
        isLoading={isLoading}
        currentAgent={activeSession.currentAgentId}
        agents={agentOptions}
        onAgentSwitch={handleAgentSwitch}
        isVoiceSessionActive={isVoiceSessionActive}
        voiceSessionStatus={voiceSessionStatus}
        onImageUpload={handleImageUpload}
      />
    </div>
  );
};

export default Chat;