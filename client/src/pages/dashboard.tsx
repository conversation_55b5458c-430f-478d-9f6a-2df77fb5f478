import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import { getDashboardStats, getChatLogs } from '../services/api';
import StatsCard from '../components/dashboard/StatsCard';
import ActivityChart from '../components/dashboard/ActivityChart';
import UsageChart from '../components/dashboard/UsageChart';
import { MessageSquare, Users, Bot, Book } from 'lucide-react';
import { 
  Card, 
  CardContent, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import StatusBadge from '@/components/shared/StatusBadge';

const Dashboard = () => {
  const { data: stats, isLoading: isLoadingStats } = useQuery({
    queryKey: ['/api/stats'],
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
  
  const { data: recentActivity, isLoading: isLoadingActivity } = useQuery({
    queryKey: ['/api/logs', { page: 1, limit: 5 }],
    staleTime: 1000 * 60 * 1, // 1 minute
  });
  
  // Mock data for charts
  const activityData = [
    { name: 'Mon', value: 120 },
    { name: 'Tu<PERSON>', value: 180 },
    { name: 'Wed', value: 250 },
    { name: 'Thu', value: 190 },
    { name: 'Fri', value: 220 },
    { name: 'Sat', value: 140 },
    { name: 'Sun', value: 110 },
  ];
  
  const usageData = [
    { name: 'Style Advisor', value: 72, color: 'hsl(var(--primary))' },
    { name: 'Appointment Bot', value: 18, color: 'hsl(var(--secondary))' },
    { name: 'Design Assistant', value: 10, color: 'hsl(var(--accent))' },
  ];

  return (
    <div className="space-y-8">
      <h1 className="text-2xl font-bold">Dashboard</h1>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard 
          title="Total Chats"
          value={isLoadingStats ? '...' : stats?.totalChats || 0}
          icon={<MessageSquare size={24} />}
          iconBgColor="bg-primary-100 dark:bg-primary-900"
          iconColor="text-primary dark:text-primary-300"
          percentageChange={12}
        />
        
        <StatsCard 
          title="Active Users"
          value={isLoadingStats ? '...' : stats?.activeUsers || 0}
          icon={<Users size={24} />}
          iconBgColor="bg-secondary-100 dark:bg-secondary-900"
          iconColor="text-secondary-600 dark:text-secondary-300"
          percentageChange={8}
        />
        
        <StatsCard 
          title="Active Agents"
          value={isLoadingStats ? '...' : stats?.activeAgents || 0}
          icon={<Bot size={24} />}
          iconBgColor="bg-accent-100 dark:bg-accent-900"
          iconColor="text-accent-400 dark:text-accent-300"
          percentageChange={-3}
        />
        
        <StatsCard 
          title="KB Entries"
          value={isLoadingStats ? '...' : stats?.kbEntries || 0}
          icon={<Book size={24} />}
          iconBgColor="bg-gray-100 dark:bg-gray-800"
          iconColor="text-gray-600 dark:text-gray-300"
          percentageChange={15}
        />
      </div>
      
      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ActivityChart data={activityData} title="Chat Activity" />
        <UsageChart data={usageData} title="Agent Usage" />
      </div>
      
      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">User</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Action</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Agent</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {isLoadingActivity ? (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center">Loading recent activity...</td>
                  </tr>
                ) : recentActivity?.logs && recentActivity.logs.length > 0 ? (
                  recentActivity.logs.map((log: any, index: number) => (
                    <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                            <span className="text-xs font-medium">
                              {log.user ? `${log.user.firstName?.charAt(0)}${log.user.lastName?.charAt(0)}` : 'NA'}
                            </span>
                          </div>
                          <div className="ml-3">
                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {log.user ? `${log.user.firstName} ${log.user.lastName}` : 'Unknown User'}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {log.user?.email || 'No email'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-gray-100">
                          {log.action || 'Chat interaction'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-gray-100">
                          {log.agent ? log.agent.name : 'N/A'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {new Date(log.createdAt).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <StatusBadge status={log.status || 'completed'} />
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center">No recent activity found</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
        <CardFooter className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div className="flex items-center justify-between w-full">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Showing {recentActivity?.logs?.length || 0} of {recentActivity?.total || 0} activities
            </div>
            <div className="flex space-x-2">
              <button className="px-3 py-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-300 text-sm disabled:opacity-50" disabled>
                Previous
              </button>
              <button className="px-3 py-1 bg-primary text-white rounded text-sm hover:bg-primary-600 transition-colors">
                Next
              </button>
            </div>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default Dashboard;
