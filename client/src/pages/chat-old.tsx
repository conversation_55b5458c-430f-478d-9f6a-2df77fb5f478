import { useState, useRef, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth.tsx';
import { io, Socket } from 'socket.io-client';
import { 
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { VoiceInputButton } from '@/components/voice/VoiceInputButton';
import { PushToTalkButton } from '@/components/voice/PushToTalkButton';
import { WebRTCVoiceChat } from '@/components/voice/WebRTCVoiceChat';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { <PERSON><PERSON>, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  Send, 
  Image as ImageIcon, 
  User, 
  Bot, 
  Compass, 
  Paintbrush, 
  Search, 
  Heart, 
  Calendar 
} from 'lucide-react';
import { apiRequest } from '@/lib/queryClient';

// Import agent definitions
import { 
  ALL_AGENTS, 
  AgentDefinition, 
  AGENT_TYPES,
  ManagerAgent,
  GuidanceAgent,
  DesignerAgent,
  AnalystAgent,
  AftercareAgent,
  BookingAgent
} from '../services/agents/agentDefinitions';

// Message type
interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  agentId: string;
  timestamp: Date;
  imageUrl?: string;
  isStreaming?: boolean;
}

// Chat session type
interface Session {
  id: string;
  messages: Message[];
  currentAgentId: string;
}

// Function to get agent icon
const getAgentIcon = (agentId: string) => {
  switch (agentId) {
    case AGENT_TYPES.MANAGER:
      return <User className="h-5 w-5" />;
    case AGENT_TYPES.GUIDANCE:
      return <Compass className="h-5 w-5" />;
    case AGENT_TYPES.DESIGNER:
      return <Paintbrush className="h-5 w-5" />;
    case AGENT_TYPES.ANALYST:
      return <Search className="h-5 w-5" />;
    case AGENT_TYPES.AFTERCARE:
      return <Heart className="h-5 w-5" />;
    case AGENT_TYPES.BOOKING:
      return <Calendar className="h-5 w-5" />;
    default:
      return <Bot className="h-5 w-5" />;
  }
};

// Function to get agent name
const getAgentName = (agentId: string): string => {
  const agent = ALL_AGENTS.find(a => a.id === agentId);
  return agent ? agent.name : 'Unknown Agent';
};

// Function to format timestamp
const formatTimestamp = (date: Date): string => {
  return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

const Chat = () => {
  const { user } = useAuth();
  const [activeSession, setActiveSession] = useState<Session | null>(null);
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [socket, setSocket] = useState<Socket | null>(null);
  const messageEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isRealTimeMode, setIsRealTimeMode] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessingAudio, setIsProcessingAudio] = useState(false);

  // Initialize WebSocket connection and session
  useEffect(() => {
    // Create WebSocket connection
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const socketUrl = `${protocol}//${window.location.host}`;
    const newSocket = io(socketUrl);
    setSocket(newSocket);

    // Create a new session with manager agent
    const sessionId = `session_${Date.now()}`;
    const newSession: Session = {
      id: sessionId,
      messages: [
        {
          id: `msg_${Date.now()}`,
          role: 'system',
          content: 'Welcome to the Tattoo Multi-Agent System. How can I assist you today?',
          agentId: AGENT_TYPES.MANAGER,
          timestamp: new Date()
        }
      ],
      currentAgentId: AGENT_TYPES.MANAGER
    };
    
    setActiveSession(newSession);
    
    // Join the chat room
    newSocket.emit('chat:start', { sessionId });
    
    // Set up socket event listeners
    newSocket.on('chat:started', (data) => {
      console.log('Chat started:', data);
    });
    
    // Handle streaming messages
    let streamingMessageId: string | null = null;
    
    // Stream start - initialize empty message
    newSocket.on('chat:stream:start', (data) => {
      if (newSession.id === data.sessionId) {
        streamingMessageId = data.messageId;
        
        const streamingMessage: Message = {
          id: streamingMessageId || `msg_${Date.now()}_streaming`,
          role: 'assistant',
          content: '',
          agentId: data.agentId,
          timestamp: new Date(data.timestamp),
          isStreaming: true
        };
        
        setActiveSession(prevSession => {
          if (!prevSession) return prevSession;
          return {
            ...prevSession,
            messages: [...prevSession.messages, streamingMessage]
          };
        });
      }
    });
    
    // Legacy non-streaming response (for backward compatibility)
    newSocket.on('chat:response', (data) => {
      if (newSession.id === data.sessionId) {
        const aiMessage: Message = {
          id: `msg_${Date.now()}_ai`,
          role: 'assistant',
          content: data.message,
          agentId: data.agentId,
          timestamp: new Date(data.timestamp)
        };
        
        setActiveSession(prevSession => {
          if (!prevSession) return prevSession;
          return {
            ...prevSession,
            messages: [...prevSession.messages, aiMessage]
          };
        });
        
        setIsLoading(false);
      }
    });
    
    // Stream chunk - append to existing message
    newSocket.on('chat:stream:chunk', (data) => {
      if (newSession.id === data.sessionId && data.messageId === streamingMessageId) {
        setActiveSession(prevSession => {
          if (!prevSession) return prevSession;
          
          const updatedMessages = prevSession.messages.map(msg => 
            msg.id === streamingMessageId 
              ? { ...msg, content: msg.content + data.chunk }
              : msg
          );
          
          return {
            ...prevSession,
            messages: updatedMessages
          };
        });
      }
    });
    
    // Stream end - finalize the message
    newSocket.on('chat:stream:end', (data) => {
      if (newSession.id === data.sessionId && data.messageId === streamingMessageId) {
        setIsLoading(false);
        streamingMessageId = null;
        
        setActiveSession(prevSession => {
          if (!prevSession) return prevSession;
          
          const updatedMessages = prevSession.messages.map(msg => 
            msg.id === data.messageId 
              ? { 
                  ...msg, 
                  content: data.message, // Use the complete message from server
                  isStreaming: false 
                }
              : msg
          );
          
          return {
            ...prevSession,
            messages: updatedMessages
          };
        });
      }
    });
    
    // Error handling
    newSocket.on('chat:error', (data) => {
      if (newSession.id === data.sessionId) {
        console.error('Chat error:', data.error);
        setIsLoading(false);
        
        setActiveSession(prevSession => {
          if (!prevSession) return prevSession;
          return {
            ...prevSession,
            messages: [...prevSession.messages, {
              id: `msg_${Date.now()}_error`,
              role: 'system',
              content: `Error: ${data.error}`,
              agentId: prevSession.currentAgentId,
              timestamp: new Date()
            }]
          };
        });
      }
    });
    
    // Clean up on unmount
    return () => {
      if (newSocket) {
        newSocket.disconnect();
      }
    };
  }, []);

  // Scroll to bottom when messages change
  useEffect(() => {
    messageEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [activeSession?.messages]);

  // Send message to AI
  const sendMessage = async () => {
    if (!message.trim() && !imageUrl) return;
    if (!activeSession || !socket) return;
    
    // Add user message
    const userMessage: Message = {
      id: `msg_${Date.now()}_user`,
      role: 'user',
      content: message,
      agentId: activeSession.currentAgentId,
      timestamp: new Date(),
      imageUrl: imageUrl || undefined
    };
    
    // Add to UI
    setActiveSession(prevSession => {
      if (!prevSession) return prevSession;
      return {
        ...prevSession,
        messages: [...prevSession.messages, userMessage]
      };
    });
    
    // Clear input and set loading state
    setMessage('');
    setImageUrl(null);
    setIsLoading(true);
    
    try {
      // Send via WebSocket
      socket.emit('chat:message', {
        sessionId: activeSession.id,
        message: userMessage.content,
        agentId: activeSession.currentAgentId,
        imageUrl: userMessage.imageUrl // Include image if present
      });
    } catch (error) {
      console.error('Error sending message:', error);
      
      // Add error message to conversation
      setActiveSession(prevSession => {
        if (!prevSession) return prevSession;
        return {
          ...prevSession,
          messages: [...prevSession.messages, {
            id: `msg_${Date.now()}_error`,
            role: 'system',
            content: 'Sorry, there was an error sending your message. Please try again.',
            agentId: prevSession.currentAgentId,
            timestamp: new Date()
          }]
        };
      });
      
      setIsLoading(false);
    }
  };

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = () => {
      setImageUrl(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  // Trigger file input
  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  // Voice recording handlers
  const handleStartRecording = () => {
    setIsRecording(true);
  };

  const handleStopRecording = () => {
    setIsRecording(false);
  };

  const handlePushToTalkAudio = async (audioData: string) => {
    if (!activeSession || !socket) return;
    
    setIsProcessingAudio(true);
    
    try {
      // Send audio to transcription service first
      const response = await fetch('/api/ai/transcribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          audioData: audioData,
          format: 'webm'
        }),
      });

      if (!response.ok) {
        throw new Error(`Transcription failed: ${response.status}`);
      }

      const data = await response.json();
      const transcribedText = data.text || data.transcription || '';
      
      if (transcribedText.trim()) {
        // Create voice message with transcribed text
        const userMessage: Message = {
          id: `msg_${Date.now()}_voice`,
          role: 'user',
          content: transcribedText.trim(),
          agentId: activeSession.currentAgentId,
          timestamp: new Date()
        };
        
        // Add to UI
        setActiveSession(prevSession => {
          if (!prevSession) return prevSession;
          return {
            ...prevSession,
            messages: [...prevSession.messages, userMessage]
          };
        });
        
        setIsLoading(true);
        
        // Send via WebSocket
        socket.emit('chat:message', {
          sessionId: activeSession.id,
          message: userMessage.content,
          agentId: activeSession.currentAgentId
        });
      }
    } catch (error) {
      console.error('Voice processing error:', error);
    } finally {
      setIsProcessingAudio(false);
    }
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // Switch agent manually
  const switchAgent = async (agentId: string) => {
    if (!activeSession || !socket || activeSession.currentAgentId === agentId) return;
    
    // Add system message for agent switch
    const switchMessage: Message = {
      id: `msg_${Date.now()}_switch`,
      role: 'system',
      content: `Switching to ${getAgentName(agentId)} agent...`,
      agentId,
      timestamp: new Date()
    };
    
    // Update UI
    setActiveSession(prevSession => {
      if (!prevSession) return prevSession;
      return {
        ...prevSession,
        messages: [...prevSession.messages, switchMessage],
        currentAgentId: agentId
      };
    });
    
    setIsLoading(true);
    
    try {
      // Send introduction request via socket
      socket.emit('chat:message', {
        sessionId: activeSession.id,
        message: 'Please introduce yourself briefly and explain how you can help.',
        agentId: agentId
      });
    } catch (error) {
      console.error('Error switching agent:', error);
      
      // Add error message
      setActiveSession(prevSession => {
        if (!prevSession) return prevSession;
        return {
          ...prevSession,
          messages: [...prevSession.messages, {
            id: `msg_${Date.now()}_error`,
            role: 'system',
            content: 'Sorry, there was an error switching agents. Please try again.',
            agentId: prevSession.currentAgentId,
            timestamp: new Date()
          }]
        };
      });
      
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-6 h-[calc(100vh-80px)] flex flex-col">
      <Card className="flex-1 flex flex-col overflow-hidden">
        <CardHeader className="px-6 py-4 border-b">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Tattoo Consultation</CardTitle>
              <CardDescription>
                Chat with our specialized tattoo agents to get personalized assistance
              </CardDescription>
            </div>
            
            {/* Voice Mode Toggle */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <Label htmlFor="voice-mode">Real-time Voice</Label>
                <Switch
                  id="voice-mode"
                  checked={isRealTimeMode}
                  onCheckedChange={setIsRealTimeMode}
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className={activeSession?.currentAgentId === AGENT_TYPES.MANAGER ? 'bg-primary/20' : ''}
                      onClick={() => switchAgent(AGENT_TYPES.MANAGER)}
                    >
                      <User className="h-4 w-4 mr-2" />
                      Manager
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Switch to Tattoo Manager</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant="outline" 
                      size="sm"
                      className={activeSession?.currentAgentId === AGENT_TYPES.GUIDANCE ? 'bg-primary/20' : ''}
                      onClick={() => switchAgent(AGENT_TYPES.GUIDANCE)}
                    >
                      <Compass className="h-4 w-4 mr-2" />
                      Guidance
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Switch to Tattoo Guidance Expert</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant="outline" 
                      size="sm"
                      className={activeSession?.currentAgentId === AGENT_TYPES.DESIGNER ? 'bg-primary/20' : ''}
                      onClick={() => switchAgent(AGENT_TYPES.DESIGNER)}
                    >
                      <Paintbrush className="h-4 w-4 mr-2" />
                      Designer
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Switch to Tattoo Designer</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant="outline" 
                      size="sm"
                      className={activeSession?.currentAgentId === AGENT_TYPES.ANALYST ? 'bg-primary/20' : ''}
                      onClick={() => switchAgent(AGENT_TYPES.ANALYST)}
                    >
                      <Search className="h-4 w-4 mr-2" />
                      Analyst
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Switch to Tattoo Analyst</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant="outline" 
                      size="sm"
                      className={activeSession?.currentAgentId === AGENT_TYPES.AFTERCARE ? 'bg-primary/20' : ''}
                      onClick={() => switchAgent(AGENT_TYPES.AFTERCARE)}
                    >
                      <Heart className="h-4 w-4 mr-2" />
                      Aftercare
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Switch to Aftercare Expert</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant="outline" 
                      size="sm"
                      className={activeSession?.currentAgentId === AGENT_TYPES.BOOKING ? 'bg-primary/20' : ''}
                      onClick={() => switchAgent(AGENT_TYPES.BOOKING)}
                    >
                      <Calendar className="h-4 w-4 mr-2" />
                      Booking
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Switch to Booking Consultant</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </CardHeader>
        
        {/* Real-time Voice Chat Mode */}
        {isRealTimeMode ? (
          <div className="flex-1 p-6">
            <WebRTCVoiceChat
              agentId={activeSession?.currentAgentId || AGENT_TYPES.MANAGER}
              onSessionStart={(sessionId) => {
                console.log('WebRTC session started:', sessionId);
              }}
              onSessionEnd={() => {
                console.log('WebRTC session ended');
              }}
              onError={(error) => {
                console.error('WebRTC error:', error);
              }}
            />
          </div>
        ) : (
          <>
            <ScrollArea className="flex-1 p-4">
          <div className="space-y-4">
            {activeSession?.messages.map((msg) => (
              <div
                key={msg.id}
                className={`flex ${
                  msg.role === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                <div
                  className={`flex max-w-[80%] ${
                    msg.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                  }`}
                >
                  <div
                    className={`flex h-8 w-8 shrink-0 select-none items-center justify-center rounded-full ${
                      msg.role === 'user'
                        ? 'bg-primary text-primary-foreground ml-2'
                        : (msg.role === 'system' ? 'bg-muted text-muted-foreground' : 'bg-accent text-accent-foreground mr-2')
                    }`}
                  >
                    {msg.role === 'user' ? (
                      <User className="h-5 w-5" />
                    ) : (
                      getAgentIcon(msg.agentId)
                    )}
                  </div>
                  
                  <div>
                    <div className="mb-1 text-xs text-muted-foreground flex items-center">
                      <span>{msg.role === 'user' ? 'You' : (msg.role === 'system' ? 'System' : getAgentName(msg.agentId))}</span>
                      <span className="mx-2">•</span>
                      <span>{formatTimestamp(msg.timestamp)}</span>
                    </div>
                    
                    <div
                      className={`rounded-lg px-4 py-3 ${
                        msg.role === 'user'
                          ? 'bg-primary text-primary-foreground'
                          : (msg.role === 'system' ? 'bg-muted text-muted-foreground' : 'bg-accent text-accent-foreground')
                      }`}
                    >
                      {msg.imageUrl && (
                        <div className="mb-3">
                          <img 
                            src={msg.imageUrl}
                            alt="User uploaded"
                            className="rounded-md max-w-full max-h-80 object-contain"
                          />
                        </div>
                      )}
                      <div className="whitespace-pre-wrap">
                        {msg.content}
                        {msg.isStreaming && (
                          <span className="ml-1 inline-block w-2 h-4 bg-current rounded-full animate-pulse"></span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            
            {isLoading && (
              <div className="flex justify-start">
                <div className="flex max-w-[80%] flex-row">
                  <div className="flex h-8 w-8 shrink-0 select-none items-center justify-center rounded-full bg-accent text-accent-foreground mr-2">
                    {getAgentIcon(activeSession?.currentAgentId || AGENT_TYPES.MANAGER)}
                  </div>
                  
                  <div>
                    <div className="mb-1 text-xs text-muted-foreground flex items-center">
                      <span>{getAgentName(activeSession?.currentAgentId || AGENT_TYPES.MANAGER)}</span>
                      <span className="mx-2">•</span>
                      <span>{formatTimestamp(new Date())}</span>
                    </div>
                    
                    <div className="rounded-lg px-4 py-3 bg-accent text-accent-foreground">
                      <div className="flex items-center space-x-2">
                        <div className="h-2 w-2 rounded-full bg-current animate-pulse"></div>
                        <div className="h-2 w-2 rounded-full bg-current animate-pulse delay-75"></div>
                        <div className="h-2 w-2 rounded-full bg-current animate-pulse delay-150"></div>
                        <span className="ml-2 text-sm">Thinking...</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messageEndRef} />
          </div>
        </ScrollArea>
        
        <div className="p-4 border-t">
          {imageUrl && (
            <div className="mb-4 p-2 border rounded-md relative inline-block">
              <img 
                src={imageUrl} 
                alt="Upload preview" 
                className="h-20 object-contain rounded"
              />
              <Button 
                variant="ghost" 
                size="sm" 
                className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0 bg-destructive text-destructive-foreground"
                onClick={() => setImageUrl(null)}
              >
                ×
              </Button>
            </div>
          )}
          
          <div className="flex space-x-2">
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="image/*"
              onChange={handleImageUpload}
            />
            
            <Button
              variant="outline"
              size="icon"
              onClick={triggerFileInput}
              title="Upload image"
            >
              <ImageIcon className="h-5 w-5" />
            </Button>
            
            {/* Original Sleek Voice Button */}
            <PushToTalkButton
              onStartRecording={handleStartRecording}
              onStopRecording={handleStopRecording}
              onAudioData={handlePushToTalkAudio}
              isRecording={isRecording}
              disabled={isLoading || isProcessingAudio}
              className="shrink-0"
            />
            
            <VoiceInputButton
              onTranscription={(text) => {
                setMessage(text);
              }}
              onError={(error) => {
                console.error('Voice input error:', error);
                // Could show a toast notification here
              }}
              disabled={isLoading}
              className="shrink-0"
            />
            
            <Textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder={`Ask the ${getAgentName(activeSession?.currentAgentId || AGENT_TYPES.MANAGER)} about tattoos...`}
              className="min-h-10 flex-1 resize-none"
            />
            
            <Button
              type="submit"
              size="icon"
              disabled={isLoading || (!message.trim() && !imageUrl)}
              onClick={sendMessage}
              title="Send message"
            >
              <Send className="h-5 w-5" />
            </Button>
          </div>
          
          <div className="mt-2 text-xs text-muted-foreground">
            <p>Currently chatting with: <span className="font-medium">{getAgentName(activeSession?.currentAgentId || AGENT_TYPES.MANAGER)}</span></p>
          </div>
        </div>
        </>
        )}
      </Card>
    </div>
  );
};

export default Chat;