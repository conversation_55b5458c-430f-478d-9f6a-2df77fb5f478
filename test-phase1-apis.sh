#!/bin/bash

# Comprehensive Phase 1 API Testing Script
# Tests webhook registration, vector database, settings management

BASE_URL="http://localhost:5000"
echo "🧪 Testing Phase 1 SaaS Foundation APIs..."
echo "========================================"

# Test 1: Vector Database - Get Documents
echo "📚 Testing Knowledge Base - Get Documents..."
curl -s -X GET "$BASE_URL/api/knowledge/documents" | python3 -m json.tool || echo "❌ Knowledge documents endpoint failed"

# Test 2: Vector Database - Add New Document  
echo -e "\n📝 Testing Knowledge Base - Add Document..."
curl -s -X POST "$BASE_URL/api/knowledge/documents" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Watercolor tattoos blend vibrant colors with painterly techniques, creating soft gradients and artistic flowing effects without traditional black outlines.",
    "title": "Watercolor Tattoo Techniques", 
    "category": "tattoo_styles",
    "tags": ["watercolor", "painterly", "gradients", "artistic"],
    "source": "Modern Tattoo Styles Guide"
  }' | jq '.' || echo "❌ Add document failed"

# Test 3: Vector Database - Search Knowledge
echo -e "\n🔍 Testing Vector Search - Traditional Styles..."
curl -s -X POST "$BASE_URL/api/knowledge/search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "traditional tattoo bold lines classic imagery",
    "limit": 3
  }' | jq '.' || echo "❌ Vector search failed"

# Test 4: Vector Database - Generate AI Response
echo -e "\n🤖 Testing AI Context Response - Aftercare..."
curl -s -X POST "$BASE_URL/api/knowledge/generate-response" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "How should I care for my new tattoo?",
    "agentType": "Aftercare Specialist"
  }' | jq '.' || echo "❌ AI response generation failed"

# Test 5: Webhook Registration
echo -e "\n🪝 Testing Webhook Registration..."
curl -s -X POST "$BASE_URL/api/webhooks/register" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://analytics.example.com/tattoo-chat-events"
  }' | jq '.' || echo "❌ Webhook registration failed"

# Test 6: Analytics - Day View
echo -e "\n📊 Testing Analytics - Daily Stats..."
curl -s -X GET "$BASE_URL/api/analytics/day" | jq '.' || echo "❌ Daily analytics failed"

# Test 7: API Settings - Get All
echo -e "\n⚙️ Testing API Settings - Get All..."
curl -s -X GET "$BASE_URL/api/settings/api" | jq '.' || echo "❌ API settings retrieval failed"

# Test 8: API Settings - Update Leonardo AI
echo -e "\n🎨 Testing API Settings - Update Leonardo AI..."
curl -s -X PUT "$BASE_URL/api/settings/api/leonardo" \
  -H "Content-Type: application/json" \
  -d '{
    "apiKey": "leonardo-test-key-456", 
    "status": "active"
  }' | jq '.' || echo "❌ Leonardo API update failed"

# Test 9: SaaS Settings - Get All
echo -e "\n🏢 Testing SaaS Settings - Get All..."
curl -s -X GET "$BASE_URL/api/settings/saas" | jq '.' || echo "❌ SaaS settings retrieval failed"

# Test 10: SaaS Settings - Update Firebase Auth
echo -e "\n🔐 Testing SaaS Settings - Update Firebase..."
curl -s -X PUT "$BASE_URL/api/settings/saas/firebase_auth" \
  -H "Content-Type: application/json" \
  -d '{
    "settings": {
      "projectId": "tattoo-ai-platform",
      "enabled": true
    }
  }' | jq '.' || echo "❌ Firebase settings update failed"

# Test 11: Settings Export
echo -e "\n📤 Testing Settings Export..."
curl -s -X GET "$BASE_URL/api/settings/export" | jq '.' || echo "❌ Settings export failed"

echo -e "\n✅ Phase 1 API Testing Complete!"
echo "🚀 Ready for Phase 2: FastAPI Backend Migration"