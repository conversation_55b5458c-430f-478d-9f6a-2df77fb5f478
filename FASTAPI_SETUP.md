# TattooAdmin FastAPI Setup Guide

## 🚀 Quick Start with FastAPI Backend

This guide will help you set up <PERSON><PERSON>oAdmin with FastAPI as the primary backend and Google OAuth authentication.

## 📋 Prerequisites

- Python 3.8+ installed
- Node.js 18+ installed
- PostgreSQL 13+ installed (optional, can use SQLite for development)
- Google Cloud Console account for OAuth setup

## 🔧 Environment Setup

### 1. Configure Environment Variables

Your `.env` file has been created with your Google OAuth credentials:

```bash
# Google OAuth Configuration
GOOGLE_CLIENT_ID=************-f9tdrok11kr19thsov930t1n657tj9ll.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=http://localhost:5173/auth/callback

# FastAPI Configuration
NODE_ENV=development
PORT=8000
HOST_URL=http://localhost:8000
```

### 2. Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project or create a new one
3. Enable the Google+ API
4. Go to "Credentials" → "OAuth 2.0 Client IDs"
5. Add these authorized redirect URIs:
   - `http://localhost:5173/auth/callback` (for development)
   - `http://localhost:8000/auth/google/callback` (FastAPI callback)

## 🐍 FastAPI Backend Setup

### 1. Start FastAPI Server

```bash
# Make the startup script executable (if not already)
chmod +x start-fastapi.sh

# Start the FastAPI server
./start-fastapi.sh
```

This script will:
- Create a Python virtual environment
- Install all dependencies
- Set up necessary directories
- Start the FastAPI server on port 8000

### 2. Manual Setup (Alternative)

If you prefer manual setup:

```bash
# Navigate to FastAPI backend
cd fastapi-backend

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Create directories
mkdir -p generated/{depth,mask,uv,normal,baked,stencil,svg,packages}
mkdir -p logs uploads temp

# Start server
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## 🌐 Frontend Setup

### 1. Install Dependencies

```bash
# Install Node.js dependencies
npm install
```

### 2. Configure Environment

Create `client/.env.local`:

```bash
VITE_API_URL=http://localhost:8000
VITE_DEV_MODE=false
```

### 3. Start Development Server

```bash
# Start the Vite development server
npm run dev
```

The client will be available at `http://localhost:5173`

## 🗄️ Database Setup (Optional)

### PostgreSQL Setup

1. **Install PostgreSQL** (if not already installed):
   ```bash
   # macOS
   brew install postgresql
   brew services start postgresql
   
   # Ubuntu
   sudo apt-get install postgresql postgresql-contrib
   sudo systemctl start postgresql
   ```

2. **Create Database**:
   ```bash
   # Create user and database
   createuser tattoo_user
   createdb tattoo_db -O tattoo_user
   
   # Set password
   psql -c "ALTER USER tattoo_user PASSWORD 'tattoo_password';"
   ```

3. **Update Environment**:
   ```bash
   USE_DATABASE=true
   DATABASE_URL=postgresql://tattoo_user:tattoo_password@localhost:5432/tattoo_db
   ```

### SQLite Setup (Development)

For development, you can use SQLite:

```bash
USE_DATABASE=false
# FastAPI will use in-memory storage
```

## 🔐 Authentication Flow

### Google OAuth Flow

1. **User clicks "Continue with Google"** on login page
2. **Client requests auth URL** from FastAPI (`/auth/google`)
3. **User redirects to Google** OAuth consent screen
4. **Google redirects back** to `http://localhost:5173/auth/callback`
5. **Client exchanges code** with FastAPI (`/auth/google/callback`)
6. **FastAPI returns JWT token** and user data
7. **Client stores token** and redirects to dashboard

### API Authentication

All API requests include the JWT token:

```javascript
fetch('http://localhost:8000/api/endpoint', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
})
```

## 📚 API Documentation

Once FastAPI is running, visit:

- **Interactive API Docs**: http://localhost:8000/docs
- **ReDoc Documentation**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

## 🔗 Available Endpoints

### Authentication
- `GET /auth/google` - Get Google OAuth URL
- `GET /auth/google/callback` - Handle OAuth callback
- `GET /auth/me` - Get current user
- `POST /auth/logout` - Logout

### Health Check
- `GET /health` - Service health status

### 3D Scanning & AI
- `POST /api/v2/scan/ingest` - Upload and process body scan
- `POST /api/v2/scan/depth-map` - Generate depth map
- `POST /api/v2/scan/uv-unwrap` - UV unwrap surface
- `POST /api/v2/ai/enhance` - AI design enhancement
- `POST /api/v2/export/stencil` - Export tattoo stencil

## 🧪 Testing the Setup

### 1. Test FastAPI Backend

```bash
# Check health endpoint
curl http://localhost:8000/health

# Test Google OAuth URL
curl http://localhost:8000/auth/google
```

### 2. Test Frontend

1. Open http://localhost:5173
2. Click "Continue with Google"
3. Complete OAuth flow
4. Verify dashboard access

## 🐛 Troubleshooting

### Common Issues

1. **Port Already in Use**:
   ```bash
   # Kill process on port 8000
   lsof -ti:8000 | xargs kill -9
   ```

2. **Python Dependencies**:
   ```bash
   # Upgrade pip and reinstall
   pip install --upgrade pip
   pip install -r requirements.txt --force-reinstall
   ```

3. **Google OAuth Errors**:
   - Verify redirect URIs in Google Console
   - Check CORS settings
   - Ensure client ID/secret are correct

4. **Database Connection**:
   ```bash
   # Test PostgreSQL connection
   psql -h localhost -U tattoo_user -d tattoo_db
   ```

### Logs and Debugging

- **FastAPI Logs**: Check terminal where uvicorn is running
- **Client Logs**: Check browser developer console
- **Database Logs**: Check PostgreSQL logs

## 🚀 Production Deployment

### Environment Variables for Production

```bash
NODE_ENV=production
ALLOWED_ORIGINS=https://yourdomain.com
GOOGLE_REDIRECT_URI=https://yourdomain.com/auth/callback
DATABASE_URL=***********************************/tattoo_db
```

### Docker Deployment

```bash
# Build and run with Docker Compose
cd fastapi-backend
docker-compose up -d
```

## 📞 Support

If you encounter issues:

1. Check the logs in both FastAPI and client terminals
2. Verify all environment variables are set correctly
3. Ensure Google OAuth is configured properly
4. Test individual endpoints using the API docs

The FastAPI backend provides comprehensive 3D scanning, AI integration, and authentication services for your TattooAdmin application.
