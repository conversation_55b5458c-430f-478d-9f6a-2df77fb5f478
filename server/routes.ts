import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertUserSchema, insertAgentSchema, insertChatLogSchema, insertKnowledgeBaseSchema } from "@shared/schema";
import { z } from "zod";
import OpenAI from "openai";
import { Server as WebSocketServer } from "socket.io";
import { WebSocket } from "ws";
import { ALL_AGENTS } from "../client/src/services/agents/agentDefinitions";
import * as fs from 'fs';
import * as path from 'path';
import { tattooAgentsService } from './services/openaiAgentsService.js';
import { chatLogService, ChatTrace } from './services/chatLogService';
import { vectorService } from './services/vectorService';
import { settingsService } from './services/settingsService';
import { threeDScanService } from './services/threeDScanService';
import { authService } from './services/authService';
import { socketCorsConfig } from './config/corsConfig';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

// Helper function to validate request body with Zod
function validateBody<T>(schema: z.ZodType<T>) {
  return (req: Request, res: Response, next: Function) => {
    try {
      req.body = schema.parse(req.body);
      next();
    } catch (error) {
      res.status(400).json({ error: "Invalid request body", details: error });
    }
  };
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Auth endpoints
  app.post("/api/auth/login", async (req, res) => {
    try {
      const { username, password } = req.body;

      const result = await authService.login(username, password);

      return res.status(200).json(result);
    } catch (error: any) {
      console.error('Login error:', error);
      return res.status(401).json({
        message: error.message || "Authentication failed"
      });
    }
  });

  // Register endpoint
  app.post("/api/auth/register", async (req, res) => {
    try {
      const { username, password, email, firstName, lastName, role } = req.body;

      const user = await authService.createUser({
        username,
        password,
        email,
        firstName,
        lastName,
        role
      });

      return res.status(201).json({
        message: "User created successfully",
        user
      });
    } catch (error: any) {
      console.error('Registration error:', error);
      return res.status(400).json({
        message: error.message || "Registration failed"
      });
    }
  });

  // Token validation endpoint
  app.get("/api/auth/me", async (req, res) => {
    try {
      const user = await authService.validateTokenAndGetUser(req.headers.authorization);

      if (!user) {
        return res.status(401).json({ message: "Invalid or expired token" });
      }

      const { password: _, ...userWithoutPassword } = user;
      return res.status(200).json({ user: userWithoutPassword });
    } catch (error: any) {
      console.error('Token validation error:', error);
      return res.status(401).json({ message: "Authentication failed" });
    }
  });
  
  // User endpoints
  app.get("/api/users", async (req, res) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    
    try {
      const result = await storage.getUsers(page, limit);
      return res.status(200).json(result);
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  app.get("/api/users/:id", async (req, res) => {
    const id = parseInt(req.params.id);
    
    try {
      const user = await storage.getUser(id);
      
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      
      return res.status(200).json(user);
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  app.post("/api/users", validateBody(insertUserSchema), async (req, res) => {
    try {
      const newUser = await storage.createUser(req.body);
      return res.status(201).json(newUser);
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  app.put("/api/users/:id", async (req, res) => {
    const id = parseInt(req.params.id);
    
    try {
      const updatedUser = await storage.updateUser(id, req.body);
      
      if (!updatedUser) {
        return res.status(404).json({ message: "User not found" });
      }
      
      return res.status(200).json(updatedUser);
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  app.delete("/api/users/:id", async (req, res) => {
    const id = parseInt(req.params.id);
    
    try {
      const result = await storage.deleteUser(id);
      
      if (!result) {
        return res.status(404).json({ message: "User not found" });
      }
      
      return res.status(204).send();
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  // Agent endpoints
  app.get("/api/agents", async (req, res) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    
    try {
      const result = await storage.getAgents(page, limit);
      return res.status(200).json(result);
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  app.get("/api/agents/:id", async (req, res) => {
    const id = parseInt(req.params.id);
    
    try {
      const agent = await storage.getAgent(id);
      
      if (!agent) {
        return res.status(404).json({ message: "Agent not found" });
      }
      
      return res.status(200).json(agent);
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  app.post("/api/agents", validateBody(insertAgentSchema), async (req, res) => {
    try {
      const newAgent = await storage.createAgent(req.body);
      return res.status(201).json(newAgent);
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  app.put("/api/agents/:id", async (req, res) => {
    const id = parseInt(req.params.id);
    
    try {
      const updatedAgent = await storage.updateAgent(id, req.body);
      
      if (!updatedAgent) {
        return res.status(404).json({ message: "Agent not found" });
      }
      
      return res.status(200).json(updatedAgent);
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  app.delete("/api/agents/:id", async (req, res) => {
    const id = parseInt(req.params.id);
    
    try {
      const result = await storage.deleteAgent(id);
      
      if (!result) {
        return res.status(404).json({ message: "Agent not found" });
      }
      
      return res.status(204).send();
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  // Chat logs endpoints
  app.get("/api/logs", async (req, res) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const userId = req.query.userId ? parseInt(req.query.userId as string) : undefined;
    const agentId = req.query.agentId ? parseInt(req.query.agentId as string) : undefined;
    
    try {
      const result = await storage.getChatLogs(page, limit, userId, agentId);
      return res.status(200).json(result);
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  app.get("/api/logs/:id", async (req, res) => {
    const id = parseInt(req.params.id);
    
    try {
      const log = await storage.getChatLog(id);
      
      if (!log) {
        return res.status(404).json({ message: "Chat log not found" });
      }
      
      return res.status(200).json(log);
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  app.post("/api/logs", validateBody(insertChatLogSchema), async (req, res) => {
    try {
      const newLog = await storage.createChatLog(req.body);
      return res.status(201).json(newLog);
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  // Knowledge base endpoints
  app.get("/api/knowledge", async (req, res) => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const category = req.query.category as string | undefined;
    
    try {
      const result = await storage.getKnowledgeBase(page, limit, category);
      return res.status(200).json(result);
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  app.get("/api/knowledge/:id", async (req, res) => {
    const id = parseInt(req.params.id);
    
    try {
      const item = await storage.getKnowledgeBaseItem(id);
      
      if (!item) {
        return res.status(404).json({ message: "Knowledge base item not found" });
      }
      
      return res.status(200).json(item);
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  app.post("/api/knowledge", validateBody(insertKnowledgeBaseSchema), async (req, res) => {
    try {
      const newItem = await storage.createKnowledgeBaseItem(req.body);
      return res.status(201).json(newItem);
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  app.put("/api/knowledge/:id", async (req, res) => {
    const id = parseInt(req.params.id);
    
    try {
      const updatedItem = await storage.updateKnowledgeBaseItem(id, req.body);
      
      if (!updatedItem) {
        return res.status(404).json({ message: "Knowledge base item not found" });
      }
      
      return res.status(200).json(updatedItem);
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  app.delete("/api/knowledge/:id", async (req, res) => {
    const id = parseInt(req.params.id);
    
    try {
      const result = await storage.deleteKnowledgeBaseItem(id);
      
      if (!result) {
        return res.status(404).json({ message: "Knowledge base item not found" });
      }
      
      return res.status(204).send();
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  // Settings endpoints
  app.get("/api/settings", async (req, res) => {
    try {
      const settings = await storage.getSettings();
      return res.status(200).json(settings);
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  app.get("/api/settings/:key", async (req, res) => {
    const key = req.params.key;
    
    try {
      const setting = await storage.getSetting(key);
      
      if (!setting) {
        return res.status(404).json({ message: "Setting not found" });
      }
      
      return res.status(200).json(setting);
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  app.put("/api/settings/:key", async (req, res) => {
    const key = req.params.key;
    const { value } = req.body;
    
    if (!value) {
      return res.status(400).json({ message: "Value is required" });
    }
    
    try {
      const updatedSetting = await storage.updateSetting(key, value);
      return res.status(200).json(updatedSetting);
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  // OpenAI API endpoints
  app.post("/api/ai/chat", async (req, res) => {
    try {
      const { messages, model = "gpt-4o", temperature = 0.7, jsonMode = false } = req.body;
      
      if (!messages || !Array.isArray(messages)) {
        return res.status(400).json({ message: "Invalid messages array" });
      }
      
      const requestOptions: any = {
        model,
        messages,
        temperature,
      };
      
      if (jsonMode) {
        requestOptions.response_format = { type: "json_object" };
      }
      
      const response = await openai.chat.completions.create(requestOptions);
      
      res.json({
        message: response.choices[0].message.content,
        rawResponse: response
      });
    } catch (error: any) {
      console.error("OpenAI API error:", error);
      res.status(500).json({ 
        message: "Error generating AI response", 
        error: error.message 
      });
    }
  });
  
  app.post("/api/ai/generate-image", async (req, res) => {
    try {
      const { prompt, size = "1024x1024", quality = "standard", style = "vivid" } = req.body;
      
      if (!prompt) {
        return res.status(400).json({ message: "Prompt is required" });
      }
      
      const response = await openai.images.generate({
        model: "dall-e-3",
        prompt,
        n: 1,
        size: size,
        quality: quality,
        style: style,
      });
      
      res.json({
        url: response.data[0].url,
        revisedPrompt: response.data[0].revised_prompt
      });
    } catch (error: any) {
      console.error("OpenAI image generation error:", error);
      res.status(500).json({ 
        message: "Error generating image", 
        error: error.message 
      });
    }
  });
  
  app.post("/api/ai/analyze-image", async (req, res) => {
    try {
      const { imageUrl, prompt = "Analyze this tattoo image in detail." } = req.body;
      
      if (!imageUrl) {
        return res.status(400).json({ message: "Image URL is required" });
      }
      
      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "user",
            content: [
              { type: "text", text: prompt },
              {
                type: "image_url",
                image_url: { url: imageUrl }
              }
            ]
          }
        ] as OpenAI.Chat.ChatCompletionMessageParam[]
      });
      
      res.json({
        analysis: response.choices[0].message.content
      });
    } catch (error: any) {
      console.error("OpenAI image analysis error:", error);
      res.status(500).json({ 
        message: "Error analyzing image", 
        error: error.message 
      });
    }
  });

  // OpenAI Realtime API ephemeral token endpoint for WebRTC
  app.get("/api/realtime/token", async (req, res) => {
    try {
      const sessionConfig = {
        type: "realtime",
        model: "gpt-realtime-preview",
        instructions: "You are a specialized tattoo consultation assistant. Help users with tattoo-related questions, design ideas, placement advice, aftercare, and booking information. Be conversational, friendly, and knowledgeable about tattoo culture and techniques.",
        voice: "alloy",
        turn_detection: {
          type: "server_vad",
          threshold: 0.5,
          prefix_padding_ms: 300,
          silence_duration_ms: 200
        }
      };

      const response = await fetch(
        "https://api.openai.com/v1/realtime/sessions",
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(sessionConfig),
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error("OpenAI API error response:", errorText);
        throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      res.json(data);
    } catch (error: any) {
      console.error("Realtime token generation error:", error);
      res.status(500).json({ 
        error: "Failed to generate realtime token",
        message: error.message 
      });
    }
  });

  // Audio transcription endpoint for voice input in regular chat
  app.post("/api/ai/transcribe", async (req, res) => {
    try {
      const { audioData, format = 'webm' } = req.body;
      
      if (!audioData) {
        return res.status(400).json({ message: "Audio data is required" });
      }
      
      // Convert base64 audio to buffer
      const base64Data = audioData.split(',')[1];
      const buffer = Buffer.from(base64Data, 'base64');
      
      // Check if audio is long enough
      if (buffer.length < 1000) { // Less than ~1KB is likely too short
        return res.status(400).json({ message: "Audio too short or empty" });
      }
      
      // Create temporary file with proper extension
      const tempFilePath = `./temp_audio_${Date.now()}.wav`;
      fs.writeFileSync(tempFilePath, buffer);
      
      console.log(`Created temporary file: ${tempFilePath} (${buffer.length} bytes)`);
      
      // Use OpenAI Whisper for transcription
      const transcript = await openai.audio.transcriptions.create({
        file: fs.createReadStream(tempFilePath),
        model: "whisper-1",
        response_format: "text",
      });
      
      // Clean up temp file
      fs.unlinkSync(tempFilePath);
      
      res.json({
        text: transcript,
        transcription: transcript // Backward compatibility
      });
    } catch (error: any) {
      console.error("Transcription error:", error);
      res.status(500).json({ 
        message: "Error transcribing audio", 
        error: error.message 
      });
    }
  });

  // OpenAI Agents SDK endpoints
  app.post("/api/agents/tattoo/generate-design", async (req, res) => {
    try {
      const { prompt, style, size = "1024x1024" } = req.body;
      
      if (!prompt) {
        return res.status(400).json({ message: "Prompt is required" });
      }
      
      const imageUrl = await tattooAgentsService.generateTattooDesign(prompt, style, size);
      
      res.json({
        imageUrl,
        prompt,
        style,
        size
      });
    } catch (error: any) {
      console.error("Tattoo design generation error:", error);
      res.status(500).json({ 
        message: "Error generating tattoo design", 
        error: error.message 
      });
    }
  });

  app.post("/api/agents/tattoo/analyze-image", async (req, res) => {
    try {
      const { imageUrl, analysisType = "comprehensive_review" } = req.body;
      
      if (!imageUrl) {
        return res.status(400).json({ message: "Image URL is required" });
      }
      
      const analysis = await tattooAgentsService.analyzeTattooImage(imageUrl, analysisType);
      
      res.json({
        analysis,
        imageUrl,
        analysisType
      });
    } catch (error: any) {
      console.error("Tattoo image analysis error:", error);
      res.status(500).json({ 
        message: "Error analyzing tattoo image", 
        error: error.message 
      });
    }
  });

  app.post("/api/agents/run", async (req, res) => {
    try {
      const { agentId, message, imageUrl } = req.body;
      
      if (!agentId || !message) {
        return res.status(400).json({ message: "Agent ID and message are required" });
      }
      
      const response = await tattooAgentsService.runAgent(agentId, message, imageUrl);
      
      res.json({
        response,
        agentId,
        message
      });
    } catch (error: any) {
      console.error("Agent run error:", error);
      res.status(500).json({ 
        message: "Error running agent", 
        error: error.message 
      });
    }
  });

  // Design generation endpoints
  app.post("/api/designs/generate", async (req, res) => {
    try {
      const { options, customPrompt } = req.body;
      
      if (!options) {
        return res.status(400).json({ message: "Design options are required" });
      }
      
      // Build the prompt based on the options
      let prompt = customPrompt || "";
      
      if (!prompt) {
        // Generate a prompt based on the options if custom prompt not provided
        const components: string[] = [];
        
        if (options.subjectMatter) {
          components.push(`A ${options.subjectMatter} tattoo design`);
        }
        
        if (options.style) {
          components.push(`in ${options.style} style`);
        }
        
        if (options.colorPalette) {
          components.push(`with a ${options.colorPalette} color palette`);
        }
        
        if (options.placement) {
          components.push(`for ${options.placement} placement`);
        }
        
        if (options.technique) {
          components.push(`using ${options.technique} technique`);
        }
        
        prompt = components.join(" ") + ". High-quality, professional tattoo design. Single tattoo isolated on plain background with no text or border.";
      }
      
      // Generate the image using DALL-E
      const response = await openai.images.generate({
        model: "dall-e-3",
        prompt,
        n: 1,
        size: "1024x1024",
        quality: "standard",
        style: "vivid",
      });
      
      return res.status(200).json({
        imageUrl: response.data[0].url,
        prompt
      });
    } catch (error: any) {
      console.error("Design generation error:", error);
      return res.status(500).json({ 
        message: "Failed to generate design",
        error: error.message 
      });
    }
  });
  
  // Image analysis endpoint for tattoo designs
  app.post("/api/designs/analyze", async (req, res) => {
    try {
      const { imageUrl, customPrompt } = req.body;
      
      if (!imageUrl) {
        return res.status(400).json({ message: "Image URL is required" });
      }
      
      // Default analysis prompt if not provided
      const analysisPrompt = customPrompt || 
        "Please analyze this tattoo design and provide two things: (1) A detailed description of the design including style, elements, techniques, and characteristics, and (2) A prompt that could help recreate a similar design with an AI image generator.";
      
      // Analyze the image using OpenAI's GPT-4 Vision
      const response = await openai.chat.completions.create({
        model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
        messages: [
          {
            role: "system",
            content: "You are a professional tattoo artist and design expert. Your task is to analyze tattoo designs, " +
                     "describe their elements in detail, and create prompts for recreating similar designs. " +
                     "Format your response as JSON with 'analysis' and 'recreationPrompt' properties."
          },
          {
            role: "user", 
            content: [
              { type: "text", text: analysisPrompt },
              {
                type: "image_url",
                image_url: { 
                  url: imageUrl.startsWith('data:') ? imageUrl : imageUrl
                }
              }
            ]
          }
        ],
        max_tokens: 1000
      });
      
      // Parse the response to get analysis and recreation prompt
      const content = response.choices[0].message.content || "";
      let parsedContent;
      
      try {
        // Try to find JSON in the content (model might add markdown code blocks)
        const jsonMatch = content.match(/```json\s*([\s\S]*)\s*```/) || content.match(/{[\s\S]*}/);
        const jsonContent = jsonMatch ? jsonMatch[0] : content;
        
        try {
          // Try to parse as JSON
          parsedContent = JSON.parse(jsonContent);
        } catch (innerError) {
          // Not valid JSON, use regex to extract sections
          console.log("Failed to parse as JSON, trying regex extraction");
          
          // Look for analysis section
          const analysisMatch = content.match(/analysis:?\s*([\s\S]*?)(?=recreation|prompt|\n\n|$)/i);
          // Look for recreation prompt section
          const promptMatch = content.match(/(?:recreation|prompt):?\s*([\s\S]*?)(?=analysis|\n\n|$)/i);
          
          parsedContent = {
            analysis: analysisMatch ? analysisMatch[1].trim() : "No analysis found in response",
            recreationPrompt: promptMatch ? promptMatch[1].trim() : "No recreation prompt found in response"
          };
        }
      } catch (parseError) {
        console.log("Error parsing response:", parseError);
        // If all parsing attempts fail, use the whole content for analysis
        parsedContent = {
          analysis: content,
          recreationPrompt: "Unable to extract a specific recreation prompt from the response."
        };
      }
      
      return res.status(200).json({
        analysis: parsedContent.analysis || "No analysis provided.",
        recreationPrompt: parsedContent.recreationPrompt || "No recreation prompt provided."
      });
    } catch (error: any) {
      console.error("Image analysis error:", error);
      return res.status(500).json({ 
        message: "Failed to analyze image",
        error: error.message 
      });
    }
  });
  
  // Dummy endpoint for designs (in-memory storage)
  interface Design {
    id: number;
    name: string;
    imageUrl: string;
    options: Record<string, any>;
    userId: number;
    createdAt: Date;
  }
  
  const designs: Design[] = [];
  let designIdCounter = 1;
  
  app.post("/api/designs", async (req, res) => {
    try {
      const { name, imageUrl, options } = req.body;
      
      if (!name || !imageUrl || !options) {
        return res.status(400).json({ message: "Name, imageUrl, and options are required" });
      }
      
      const newDesign: Design = {
        id: designIdCounter++,
        name,
        imageUrl,
        options,
        userId: 1, // Mock user ID
        createdAt: new Date()
      };
      
      designs.push(newDesign);
      
      return res.status(201).json(newDesign);
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  app.get("/api/designs", async (req, res) => {
    try {
      const userId = req.query.userId ? parseInt(req.query.userId as string) : undefined;
      
      let result = [...designs];
      
      if (userId) {
        result = result.filter(design => design.userId === userId);
      }
      
      return res.status(200).json(result);
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });
  
  // Stats endpoint for dashboard
  app.get("/api/stats", async (req, res) => {
    try {
      const { total: totalUsers } = await storage.getUsers(1, 0);
      const { total: totalAgents } = await storage.getAgents(1, 0);
      const { total: totalLogs } = await storage.getChatLogs(1, 0);
      const { total: totalKnowledgeBase } = await storage.getKnowledgeBase(1, 0);
      
      res.status(200).json({
        totalUsers,
        activeUsers: Math.floor(totalUsers * 0.8), // 80% of users are active (mock data)
        totalAgents,
        activeAgents: Math.floor(totalAgents * 0.9), // 90% of agents are active (mock data)
        totalChats: totalLogs,
        kbEntries: totalKnowledgeBase
      });
    } catch (error) {
      return res.status(500).json({ message: "Internal server error" });
    }
  });

  const httpServer = createServer(app);

  // Set up WebSocket server for real-time communication
  const io = new WebSocketServer(httpServer, {
    cors: socketCorsConfig
  });

  // Initialize audio transcription and TTS endpoints
  app.post("/api/ai/transcribe", async (req, res) => {
    try {
      const { audioData } = req.body;
      
      if (!audioData) {
        return res.status(400).json({ message: "Audio data is required" });
      }
      
      // Convert base64 audio to buffer
      const buffer = Buffer.from(audioData.split(',')[1], 'base64');
      
      // Create temporary file
      const tempFilePath = `./temp_audio_${Date.now()}.webm`;
      require('fs').writeFileSync(tempFilePath, buffer);
      
      // Transcribe using OpenAI Whisper
      const transcript = await openai.audio.transcriptions.create({
        file: require('fs').createReadStream(tempFilePath),
        model: "whisper-1",
      });
      
      // Clean up temp file
      require('fs').unlinkSync(tempFilePath);
      
      res.json({
        text: transcript.text
      });
    } catch (error: any) {
      console.error("OpenAI transcription error:", error);
      res.status(500).json({ 
        message: "Error transcribing audio", 
        error: error.message 
      });
    }
  });
  
  app.post("/api/ai/text-to-speech", async (req, res) => {
    try {
      const { text, voice = "alloy" } = req.body;
      
      if (!text) {
        return res.status(400).json({ message: "Text is required" });
      }
      
      const mp3 = await openai.audio.speech.create({
        model: "tts-1",
        voice: voice,
        input: text,
      });
      
      const buffer = Buffer.from(await mp3.arrayBuffer());
      res.setHeader('Content-Type', 'audio/mpeg');
      res.send(buffer);
    } catch (error: any) {
      console.error("OpenAI TTS error:", error);
      res.status(500).json({ 
        message: "Error generating speech", 
        error: error.message 
      });
    }
  });
  
  // WebSocket message handling for real-time agent communication
  io.on("connection", (socket) => {
    console.log("Client connected:", socket.id);
    
    socket.on("chat:start", (data) => {
      console.log("Chat started:", data);
      socket.join(`chat:${data.sessionId}`);
      io.to(`chat:${data.sessionId}`).emit("chat:started", { sessionId: data.sessionId });
    });
    
    socket.on("chat:message", async (data) => {
      const { sessionId, message, agentId, imageUrl } = data;
      console.log(`Received message from ${socket.id} for agent ${agentId}: ${message}`);
      
      // Emit typing indicator
      io.to(`chat:${sessionId}`).emit("chat:typing", { agentId });
      
      try {
        // Use enhanced TattooAgentsService with function calling
        const agentResult = await tattooAgentsService.runAgent(agentId, message, imageUrl);
        
        let messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
        
        // Emit agent response with tool usage information
        io.to(`chat:${sessionId}`).emit("chat:response", {
          sessionId,
          agentId,
          messageId,
          content: agentResult.content,
          timestamp: new Date(),
          toolsUsed: agentResult.toolsUsed || [],
          functionCall: agentResult.functionCall || null,
          imageUrl: agentResult.imageUrl || null
        });
        
        // Generate TTS for voice response
        const voiceType = data.voiceType || "alloy"; // Use specified voice or default to "alloy"
        const mp3 = await openai.audio.speech.create({
          model: "tts-1",
          voice: voiceType as "alloy" | "echo" | "fable" | "onyx" | "nova" | "shimmer",
          input: agentResult.content || "I'm sorry, I couldn't generate a response.",
        });
        
        const buffer = await mp3.arrayBuffer();
        const audioBase64 = Buffer.from(buffer).toString('base64');
        
        // Send audio response
        io.to(`chat:${sessionId}`).emit("chat:voice", {
          sessionId,
          agentId,
          audioData: `data:audio/mpeg;base64,${audioBase64}`
        });
      } catch (error: any) {
        console.error("Error handling real-time chat:", error);
        io.to(`chat:${sessionId}`).emit("chat:error", {
          sessionId,
          error: error.message
        });
      }
    });
    
    socket.on("chat:audio", async (data) => {
      const { sessionId, audioData, agentId, voiceType } = data;
      console.log(`Received audio from ${socket.id} for agent ${agentId}, voice: ${voiceType || 'alloy'}`);
      
      try {
        // First, emit an acknowledgment to let the client know we received the audio
        socket.emit("chat:audio:received", { 
          sessionId, 
          message: "Audio received, processing transcription..." 
        });
        
        let transcribedText = "";
        
        // Attempt to use OpenAI Whisper for transcription
        try {
          // Convert base64 audio to buffer
          const base64Data = audioData.split(',')[1];
          const buffer = Buffer.from(base64Data, 'base64');
          
          // Create temporary file with proper extension (.webm for WebM audio format)
          const tempFilePath = `./temp_audio_${Date.now()}.webm`;
          fs.writeFileSync(tempFilePath, buffer);
          
          console.log(`Created temporary file: ${tempFilePath} (${buffer.length} bytes)`);
          
          // Use the file for the OpenAI API
          const transcript = await openai.audio.transcriptions.create({
            file: fs.createReadStream(tempFilePath),
            model: "whisper-1",
          });
          
          // Store the transcribed text
          transcribedText = transcript.text;
          console.log(`Received transcription: "${transcribedText}"`);
          
          // Clean up temp file
          fs.unlinkSync(tempFilePath);
        } catch (transcriptionError) {
          console.error("Transcription error:", transcriptionError);
          // Fallback to a predefined message if transcription fails
          transcribedText = "I'd like to chat about tattoos.";
        }
        
        // Emit transcribed text to all clients in the room
        io.to(`chat:${sessionId}`).emit("chat:transcription", {
          sessionId,
          text: transcribedText
        });
        
        // Find which agent definition matches this agent ID
        const agentDefinition = ALL_AGENTS.find(a => a.id === agentId);
        
        // Send the transcribed text as a user message to be processed by the AI
        socket.emit("chat:message", {
          sessionId,
          message: transcribedText,
          agentId,
          voiceType,
          // Include agent-specific information
          agentName: agentDefinition?.name || 'Tattoo Agent',
          agentDescription: agentDefinition?.description || 'Specialized tattoo assistance'
        });
      } catch (error: any) {
        console.error("Error processing audio:", error);
        io.to(`chat:${sessionId}`).emit("chat:error", {
          sessionId,
          error: error.message || "Failed to process audio recording"
        });
      }
    });
    
    socket.on("disconnect", () => {
      console.log("Client disconnected:", socket.id);
    });
  });
  
  // Initialize vector service with default knowledge
  vectorService.initializeDefaultKnowledge().catch(console.error);

  // Chat logs webhook endpoints
  app.post("/api/webhooks/register", async (req, res) => {
    try {
      const { url } = req.body;
      if (!url || typeof url !== 'string') {
        return res.status(400).json({ error: "Valid webhook URL required" });
      }
      
      chatLogService.registerWebhook(url);
      res.json({ message: "Webhook registered successfully", url });
    } catch (error) {
      console.error('Error registering webhook:', error);
      res.status(500).json({ error: "Failed to register webhook" });
    }
  });

  // Get chat traces for analytics
  app.get("/api/chat-traces", async (req, res) => {
    try {
      const { sessionId, userId, agentId, startDate, endDate, limit } = req.query;
      
      const filters = {
        sessionId: sessionId as string,
        userId: userId as string,
        agentId: agentId as string,
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
        limit: limit ? parseInt(limit as string) : 50
      };

      const traces = await chatLogService.getChatTraces(filters);
      res.json({ traces, total: traces.length });
    } catch (error) {
      console.error('Error fetching chat traces:', error);
      res.status(500).json({ error: "Failed to fetch chat traces" });
    }
  });

  // Get analytics from chat logs
  app.get("/api/analytics/:timeframe", async (req, res) => {
    try {
      const { timeframe } = req.params;
      
      if (!['day', 'week', 'month'].includes(timeframe)) {
        return res.status(400).json({ error: "Invalid timeframe. Use: day, week, or month" });
      }

      const analytics = await chatLogService.getAnalytics(timeframe as 'day' | 'week' | 'month');
      res.json(analytics);
    } catch (error) {
      console.error('Error generating analytics:', error);
      res.status(500).json({ error: "Failed to generate analytics" });
    }
  });

  // Vector database knowledge base endpoints
  app.post("/api/knowledge/documents", async (req, res) => {
    try {
      const { content, title, category, tags, source } = req.body;
      
      if (!content || !title || !category) {
        return res.status(400).json({ error: "Content, title, and category are required" });
      }

      const document = await vectorService.addDocument({
        content,
        metadata: {
          title,
          category,
          tags: tags || [],
          source: source || 'manual',
          lastUpdated: new Date()
        }
      });

      res.json({ message: "Document added successfully", document });
    } catch (error) {
      console.error('Error adding document:', error);
      res.status(500).json({ error: "Failed to add document" });
    }
  });

  // Search knowledge base
  app.post("/api/knowledge/search", async (req, res) => {
    try {
      const { query, limit, category } = req.body;
      
      if (!query) {
        return res.status(400).json({ error: "Search query is required" });
      }

      const results = await vectorService.searchKnowledge(
        query, 
        limit || 5, 
        category
      );

      res.json({ results, total: results.length });
    } catch (error) {
      console.error('Error searching knowledge base:', error);
      res.status(500).json({ error: "Failed to search knowledge base" });
    }
  });

  // Generate contextual response using knowledge base
  app.post("/api/knowledge/generate-response", async (req, res) => {
    try {
      const { query, agentType } = req.body;
      
      if (!query || !agentType) {
        return res.status(400).json({ error: "Query and agent type are required" });
      }

      const response = await vectorService.generateContextualResponse(query, agentType);
      res.json({ response });
    } catch (error) {
      console.error('Error generating contextual response:', error);
      res.status(500).json({ error: "Failed to generate response" });
    }
  });

  // Get all knowledge documents (for management)
  app.get("/api/knowledge/documents", async (req, res) => {
    try {
      const documents = vectorService.getAllDocuments();
      res.json({ documents, total: documents.length });
    } catch (error) {
      console.error('Error fetching documents:', error);
      res.status(500).json({ error: "Failed to fetch documents" });
    }
  });

  // Update knowledge document
  app.put("/api/knowledge/documents/:id", async (req, res) => {
    try {
      const { id } = req.params;
      const updates = req.body;

      const updated = await vectorService.updateDocument(id, updates);
      res.json({ message: "Document updated successfully", document: updated });
    } catch (error) {
      console.error('Error updating document:', error);
      res.status(500).json({ error: "Failed to update document" });
    }
  });

  // Delete knowledge document
  app.delete("/api/knowledge/documents/:id", async (req, res) => {
    try {
      const { id } = req.params;
      const removed = vectorService.removeDocument(id);
      
      if (!removed) {
        return res.status(404).json({ error: "Document not found" });
      }

      res.json({ message: "Document deleted successfully" });
    } catch (error) {
      console.error('Error deleting document:', error);
      res.status(500).json({ error: "Failed to delete document" });
    }
  });

  // API Settings Management for SaaS
  app.get("/api/settings/api", async (req, res) => {
    try {
      const settings = await settingsService.getAPISettings();
      res.json({ settings });
    } catch (error) {
      console.error('Error fetching API settings:', error);
      res.status(500).json({ error: "Failed to fetch API settings" });
    }
  });

  app.put("/api/settings/api/:id", async (req, res) => {
    try {
      const { id } = req.params;
      const updates = req.body;
      
      const updated = await settingsService.updateAPISettings(id, updates);
      res.json({ message: "API settings updated successfully", setting: updated });
    } catch (error) {
      console.error('Error updating API settings:', error);
      res.status(500).json({ error: "Failed to update API settings" });
    }
  });

  app.post("/api/settings/api", async (req, res) => {
    try {
      const setting = await settingsService.createCustomAPISettings(req.body);
      res.json({ message: "Custom API settings created successfully", setting });
    } catch (error) {
      console.error('Error creating API settings:', error);
      res.status(500).json({ error: "Failed to create API settings" });
    }
  });

  // SaaS Settings Management
  app.get("/api/settings/saas", async (req, res) => {
    try {
      const settings = await settingsService.getSaaSSettings();
      res.json({ settings });
    } catch (error) {
      console.error('Error fetching SaaS settings:', error);
      res.status(500).json({ error: "Failed to fetch SaaS settings" });
    }
  });

  app.put("/api/settings/saas/:id", async (req, res) => {
    try {
      const { id } = req.params;
      const updates = req.body;
      
      const updated = await settingsService.updateSaaSSetting(id, updates);
      res.json({ message: "SaaS settings updated successfully", setting: updated });
    } catch (error) {
      console.error('Error updating SaaS settings:', error);
      res.status(500).json({ error: "Failed to update SaaS settings" });
    }
  });

  // Settings export/import for migration
  app.get("/api/settings/export", async (req, res) => {
    try {
      const exportData = await settingsService.exportSettings();
      res.json(exportData);
    } catch (error) {
      console.error('Error exporting settings:', error);
      res.status(500).json({ error: "Failed to export settings" });
    }
  });

  app.post("/api/settings/import", async (req, res) => {
    try {
      await settingsService.importSettings(req.body);
      res.json({ message: "Settings imported successfully" });
    } catch (error) {
      console.error('Error importing settings:', error);
      res.status(500).json({ error: "Failed to import settings" });
    }
  });

  // 3D Scan & Try-On System Endpoints
  app.post("/api/3d/scan/ingest", async (req, res) => {
    try {
      const { photo, bodyPart } = req.body;
      
      if (!photo || !bodyPart) {
        return res.status(400).json({ error: "Photo and body part are required" });
      }

      // Convert base64 to buffer
      const base64Data = photo.split(',')[1];
      const photoBuffer = Buffer.from(base64Data, 'base64');

      // Validate input
      const validation = threeDScanService.validateScanInput(photoBuffer, bodyPart);
      if (!validation.valid) {
        return res.status(400).json({ 
          error: "Invalid scan input", 
          issues: validation.issues,
          recommendations: validation.recommendations 
        });
      }

      // Process scan
      const result = await threeDScanService.generateDepthMap(photoBuffer);
      
      // Log the scan trace
      const scanTrace = {
        id: `trace_${Date.now()}_scan`,
        sessionId: req.headers['x-session-id'] || 'anonymous',
        userId: 'current_user',
        agentId: '3d_scanner',
        messageId: result.traceId,
        role: 'system' as const,
        content: `3D scan processed for ${bodyPart}`,
        timestamp: new Date(),
        metadata: {
          bodyPart,
          processingTime: Date.now(),
          traceId: result.traceId
        }
      };
      
      await chatLogService.logChatTrace(scanTrace);

      res.json({
        success: true,
        traceId: result.traceId,
        depthPng: result.depthPng,
        maskPng: result.maskPng,
        validation: validation.recommendations
      });
    } catch (error) {
      console.error('Error processing 3D scan:', error);
      res.status(500).json({ error: "Failed to process 3D scan" });
    }
  });

  app.post("/api/3d/scan/unwrap", async (req, res) => {
    try {
      const { depthPng, maskPng } = req.body;
      
      if (!depthPng || !maskPng) {
        return res.status(400).json({ error: "Depth map and mask are required" });
      }

      const result = await threeDScanService.generateUVUnwrap(depthPng, maskPng);
      res.json({
        success: true,
        traceId: result.traceId,
        uvPng: result.uvPng,
        normalPng: result.normalPng
      });
    } catch (error) {
      console.error('Error generating UV unwrap:', error);
      res.status(500).json({ error: "Failed to generate UV unwrap" });
    }
  });

  app.post("/api/3d/preview/bake", async (req, res) => {
    try {
      const { tattooImage, uvMap, normalMap, options } = req.body;
      
      if (!tattooImage || !uvMap || !normalMap) {
        return res.status(400).json({ error: "Tattoo image, UV map, and normal map are required" });
      }

      const result = await threeDScanService.bakeTattooToMesh(
        tattooImage, 
        uvMap, 
        normalMap, 
        options
      );
      
      res.json({
        success: true,
        traceId: result.traceId,
        bakedTexture: result.bakedTexture
      });
    } catch (error) {
      console.error('Error baking tattoo to mesh:', error);
      res.status(500).json({ error: "Failed to bake tattoo to mesh" });
    }
  });

  app.post("/api/3d/export/stencil", async (req, res) => {
    try {
      const { bakedTexture } = req.body;
      
      if (!bakedTexture) {
        return res.status(400).json({ error: "Baked texture is required" });
      }

      const result = await threeDScanService.generateStencil(bakedTexture);
      res.json({
        success: true,
        traceId: result.traceId,
        stencilPng: result.stencilPng,
        svgVector: result.svgVector
      });
    } catch (error) {
      console.error('Error generating stencil:', error);
      res.status(500).json({ error: "Failed to generate stencil" });
    }
  });

  app.post("/api/3d/export/palette", async (req, res) => {
    try {
      const { tattooImage } = req.body;
      
      if (!tattooImage) {
        return res.status(400).json({ error: "Tattoo image is required" });
      }

      const result = await threeDScanService.extractColorPalette(tattooImage);
      res.json({
        success: true,
        traceId: result.traceId,
        palette: result.palette,
        inkRecommendations: result.inkRecommendations
      });
    } catch (error) {
      console.error('Error extracting color palette:', error);
      res.status(500).json({ error: "Failed to extract color palette" });
    }
  });

  app.post("/api/3d/export/package", async (req, res) => {
    try {
      const { stencilPng, svgVector, palette, bakedTexture, originalPhoto } = req.body;
      
      if (!stencilPng || !svgVector || !palette || !bakedTexture) {
        return res.status(400).json({ error: "All export assets are required" });
      }

      const result = await threeDScanService.createExportPackage({
        stencilPng,
        svgVector,
        palette,
        bakedTexture,
        originalPhoto
      });
      
      res.json({
        success: true,
        traceId: result.traceId,
        zipUrl: result.zipUrl
      });
    } catch (error) {
      console.error('Error creating export package:', error);
      res.status(500).json({ error: "Failed to create export package" });
    }
  });

  app.get("/api/3d/status/:traceId", async (req, res) => {
    try {
      const { traceId } = req.params;
      const status = await threeDScanService.getProcessingStatus(traceId);
      res.json(status);
    } catch (error) {
      console.error('Error getting processing status:', error);
      res.status(500).json({ error: "Failed to get processing status" });
    }
  });

  // Leonardo AI FLUX.1 Kontext endpoints
  app.get("/api/leonardo/styles", async (req, res) => {
    try {
      const styles = {
        realistic_bw: {
          id: '22a9a7d2-2166-4d86-80ff-22e2643adbcf',
          name: 'Realistic Black & Grey',
          description: 'Pro B&W photography style for realistic tattoo rendering',
          category: 'realism'
        },
        portrait: {
          id: '8e2bc543-a2a5-4c93-b59c-7b3a5a2d8f91',
          name: 'Portrait',
          description: 'Realistic lighting reference for detailed work',
          category: 'realism'
        },
        ray_traced: {
          id: 'b504f83c-7e2d-4a9b-8c6f-3d5e1a2b9c8d',
          name: 'Ray Traced',
          description: 'Strong shading for depth and dimension',
          category: 'realism'
        },
        graphic_design: {
          id: '703d6fe5-7f1c-4a9e-8da0-5331f214d5cf',
          name: 'Graphic Design 2D',
          description: 'Clean linework for flash and traditional styles',
          category: 'linework'
        },
        illustration: {
          id: '645e4195-8b3c-4d2f-9e5a-1f6b7c8d9e0f',
          name: 'Illustration',
          description: 'Artistic illustration style with bold lines',
          category: 'linework'
        }
      };

      res.json({
        success: true,
        styles
      });
    } catch (error) {
      console.error("Error fetching style presets:", error);
      res.status(500).json({ error: "Failed to fetch style presets" });
    }
  });

  app.get("/api/leonardo/body-parts", async (req, res) => {
    try {
      const bodyParts = {
        forearm: { width: 832, height: 1248 },
        calf: { width: 832, height: 1248 },
        upper_arm: { width: 1024, height: 1456 },
        hand: { width: 944, height: 1392 },
        torso: { width: 1248, height: 1568 },
        back: { width: 1248, height: 1568 },
        chest: { width: 1248, height: 1568 }
      };

      res.json({
        success: true,
        body_parts: bodyParts
      });
    } catch (error) {
      console.error("Error fetching body part sizes:", error);
      res.status(500).json({ error: "Failed to fetch body part sizes" });
    }
  });

  app.post("/api/leonardo/flux/generate", async (req, res) => {
    try {
      const {
        subject,
        body_part = "forearm",
        style = "realistic_bw",
        width,
        height,
        context_images,
        seed,
        additional_instructions = "",
        enhance_prompt = false
      } = req.body;

      if (!subject?.trim()) {
        return res.status(400).json({ error: "Subject is required" });
      }

      if (!process.env.LEONARDO_API_KEY) {
        return res.status(500).json({ error: "Leonardo API key not configured" });
      }

      // For demo purposes, return a mock generation response
      // In production, this would call the actual Leonardo API
      const generationId = `flux_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const traceId = `trace_${Date.now()}`;

      // Log the generation request
      console.log(`FLUX Generation Request: ${subject} (${body_part}, ${style})`);
      
      res.json({
        success: true,
        generation_id: generationId,
        trace_id: traceId,
        processing_time: 0.1,
        status: "processing",
        message: "FLUX generation started - this would integrate with Leonardo AI in production"
      });

    } catch (error) {
      console.error("FLUX generation error:", error);
      res.status(500).json({ error: "Failed to start FLUX generation" });
    }
  });

  app.get("/api/leonardo/generation/:generationId", async (req, res) => {
    try {
      const { generationId } = req.params;
      
      // For demo purposes, simulate completion after a short delay
      // In production, this would check actual Leonardo API status
      res.json({
        success: true,
        status: {
          generations_by_pk: {
            status: "COMPLETE",
            generated_images: [{
              id: `img_${generationId}`,
              url: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop&crop=center",
              // This would be the actual Leonardo-generated image URL
            }]
          }
        }
      });

    } catch (error) {
      console.error("Generation status check error:", error);
      res.status(500).json({ error: "Failed to check generation status" });
    }
  });

  app.post("/api/openai/stencil", async (req, res) => {
    try {
      const { image_url, mode = "stencil", mask_url } = req.body;

      if (!image_url) {
        return res.status(400).json({ error: "Image URL is required" });
      }

      if (!process.env.OPENAI_API_KEY) {
        return res.status(500).json({ error: "OpenAI API key not configured" });
      }

      // For demo purposes, return a mock stencil response
      // In production, this would call OpenAI's image editing API
      console.log(`Stencil creation request: ${image_url} (${mode})`);

      res.json({
        success: true,
        stencil_url: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=600&fit=crop&crop=center",
        svg_url: null,
        palette: ["#000000", "#333333", "#666666", "#999999"],
        processing_time: 2.5,
        message: "Stencil creation would integrate with OpenAI in production"
      });

    } catch (error) {
      console.error("Stencil creation error:", error);
      res.status(500).json({ error: "Failed to create stencil" });
    }
  });

  // Enhanced 3D scanning endpoints for live camera integration
  app.post("/api/3d/scan/live-capture", async (req, res) => {
    try {
      const { imageData, bodyPart, privacyMode, scanMode } = req.body;

      if (!imageData) {
        return res.status(400).json({ error: "Image data is required" });
      }

      console.log(`Live 3D scan: ${bodyPart} (${scanMode}, privacy: ${privacyMode})`);

      // Simulate processing time based on scan mode
      const processingTime = scanMode === 'full' ? 3000 : 
                           scanMode === 'uv' ? 2000 : 1000;

      await new Promise(resolve => setTimeout(resolve, processingTime));

      // Mock depth mapping and UV unwrapping results
      const scanResult = {
        scanId: `live_scan_${Date.now()}`,
        depthMap: imageData, // In production, this would be actual depth data
        uvMap: imageData,
        bodyMesh: imageData,
        confidence: 0.94,
        bodyPart,
        dimensions: {
          width: bodyPart === 'forearm' ? 8.5 : bodyPart === 'hand' ? 6.2 : 12.3,
          height: bodyPart === 'forearm' ? 25.3 : bodyPart === 'hand' ? 18.5 : 30.2,
          depth: bodyPart === 'forearm' ? 6.2 : bodyPart === 'hand' ? 4.1 : 8.7
        },
        metadata: {
          timestamp: new Date(),
          privacyMode,
          scanMode,
          processingTime
        }
      };

      res.json({
        success: true,
        scan: scanResult,
        message: `3D ${scanMode} scan completed with ${(scanResult.confidence * 100).toFixed(1)}% confidence`
      });

    } catch (error) {
      console.error("Live scan error:", error);
      res.status(500).json({ error: "Failed to process live scan" });
    }
  });

  app.post("/api/3d/tattoo/apply-overlay", async (req, res) => {
    try {
      const { scanData, tattooImageUrl, lightingSettings } = req.body;

      if (!scanData || !tattooImageUrl) {
        return res.status(400).json({ error: "Scan data and tattoo image are required" });
      }

      console.log(`Applying ray-traced overlay to scan: ${scanData.scanId}`);

      // Simulate ray-tracing computation
      await new Promise(resolve => setTimeout(resolve, 4000));

      const overlayResult = {
        compositeImage: tattooImageUrl, // Mock composite result
        rayTracedOverlay: tattooImageUrl,
        lightingAnalysis: {
          ambientLight: 0.35,
          directionalLight: { x: 0.6, y: 0.8, z: 0.2 },
          skinTone: '#F2C2A1',
          shadowIntensity: 0.4,
          highlightIntensity: 0.7
        },
        qualityScore: 0.91,
        renderingStats: {
          samples: 1024,
          bounces: 8,
          renderTime: 3.8
        }
      };

      res.json({
        success: true,
        overlay: overlayResult,
        message: `Ray-traced overlay applied with ${(overlayResult.qualityScore * 100).toFixed(1)}% quality`
      });

    } catch (error) {
      console.error("Overlay application error:", error);
      res.status(500).json({ error: "Failed to apply tattoo overlay" });
    }
  });

  app.post("/api/3d/export/artist-package", async (req, res) => {
    try {
      const { scanData, overlayData, stencilOptions } = req.body;

      if (!scanData || !overlayData) {
        return res.status(400).json({ error: "Scan and overlay data are required" });
      }

      console.log(`Creating artist package for scan: ${scanData.scanId}`);

      // Simulate package creation
      await new Promise(resolve => setTimeout(resolve, 2000));

      const artistPackage = {
        packageId: `pkg_${Date.now()}`,
        stencilImage: overlayData.compositeImage,
        vectorStencil: null, // Would be SVG in production
        colorPalette: ["#000000", "#2D2D2D", "#555555", "#808080", "#AAAAAA"],
        dimensions: scanData.dimensions,
        placement: {
          bodyPart: scanData.bodyPart,
          coordinates: { x: 0, y: 0, z: 0 },
          rotation: { x: 0, y: 0, z: 0 }
        },
        instructions: `Professional tattoo stencil for ${scanData.bodyPart} placement. Dimensions: ${scanData.dimensions.width}cm x ${scanData.dimensions.height}cm`,
        downloadUrls: {
          stencilPng: overlayData.compositeImage,
          vectorSvg: null,
          zipPackage: null
        }
      };

      res.json({
        success: true,
        package: artistPackage,
        message: "Professional artist package created successfully"
      });

    } catch (error) {
      console.error("Artist package creation error:", error);
      res.status(500).json({ error: "Failed to create artist package" });
    }
  });

  return httpServer;
}
