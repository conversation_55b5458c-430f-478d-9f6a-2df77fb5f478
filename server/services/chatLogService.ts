import { storage } from '../storage';

export interface ChatTrace {
  id: string;
  sessionId: string;
  userId: string;
  agentId: string;
  messageId: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    voiceMode?: boolean;
    imageUrl?: string;
    processingTime?: number;
    tokenCount?: number;
  };
}

export interface WebhookPayload {
  event: 'message_sent' | 'message_received' | 'session_started' | 'session_ended';
  trace: ChatTrace;
  session: {
    id: string;
    startTime: Date;
    endTime?: Date;
    messageCount: number;
    participants: string[];
  };
}

class ChatLogService {
  private webhookUrls: string[] = [];

  // Register webhook URLs for external services
  registerWebhook(url: string) {
    if (!this.webhookUrls.includes(url)) {
      this.webhookUrls.push(url);
    }
  }

  // Log chat interaction and trigger webhooks
  async logChatTrace(trace: ChatTrace) {
    try {
      // Store in local database first
      await this.storeChatTrace(trace);

      // Send to registered webhooks
      await this.sendWebhooks(trace);

      console.log('Chat trace logged:', trace.id);
    } catch (error) {
      console.error('Error logging chat trace:', error);
    }
  }

  // Store chat trace in database
  private async storeChatTrace(trace: ChatTrace) {
    // For now, store in memory storage
    // In production, this would go to your database
    if (storage && typeof storage.createChatTrace === 'function') {
      await storage.createChatTrace(trace);
    }
  }

  // Send webhooks to external services
  private async sendWebhooks(trace: ChatTrace) {
    const payload: WebhookPayload = {
      event: trace.role === 'user' ? 'message_sent' : 'message_received',
      trace,
      session: {
        id: trace.sessionId,
        startTime: new Date(), // In production, get from session data
        messageCount: 1, // In production, count from session
        participants: [trace.userId, trace.agentId]
      }
    };

    const webhookPromises = this.webhookUrls.map(async (url) => {
      try {
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Webhook-Source': 'tattoo-ai-platform'
          },
          body: JSON.stringify(payload)
        });

        if (!response.ok) {
          console.error(`Webhook failed for ${url}:`, response.status);
        }
      } catch (error) {
        console.error(`Webhook error for ${url}:`, error);
      }
    });

    await Promise.allSettled(webhookPromises);
  }

  // Get chat traces for analytics
  async getChatTraces(filters: {
    sessionId?: string;
    userId?: string;
    agentId?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
  }) {
    // In production, query from database with filters
    return [];
  }

  // Generate analytics from chat traces
  async getAnalytics(timeframe: 'day' | 'week' | 'month') {
    const traces = await this.getChatTraces({
      startDate: this.getTimeframeStart(timeframe),
      endDate: new Date()
    });

    return {
      totalMessages: traces.length,
      uniqueUsers: new Set(traces.map(t => t.userId)).size,
      agentUsage: this.getAgentUsageStats(traces),
      averageSessionLength: this.calculateAverageSessionLength(traces)
    };
  }

  private getTimeframeStart(timeframe: 'day' | 'week' | 'month'): Date {
    const now = new Date();
    switch (timeframe) {
      case 'day':
        return new Date(now.getTime() - 24 * 60 * 60 * 1000);
      case 'week':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case 'month':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
  }

  private getAgentUsageStats(traces: ChatTrace[]) {
    const agentCounts = traces.reduce((acc, trace) => {
      acc[trace.agentId] = (acc[trace.agentId] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(agentCounts).map(([agentId, count]) => ({
      agentId,
      messageCount: count,
      percentage: (count / traces.length) * 100
    }));
  }

  private calculateAverageSessionLength(traces: ChatTrace[]): number {
    const sessions = traces.reduce((acc, trace) => {
      if (!acc[trace.sessionId]) {
        acc[trace.sessionId] = [];
      }
      acc[trace.sessionId].push(trace);
      return acc;
    }, {} as Record<string, ChatTrace[]>);

    const sessionLengths = Object.values(sessions).map(sessionTraces => {
      if (sessionTraces.length === 0) return 0;
      const start = Math.min(...sessionTraces.map(t => t.timestamp.getTime()));
      const end = Math.max(...sessionTraces.map(t => t.timestamp.getTime()));
      return (end - start) / 1000 / 60; // minutes
    });

    return sessionLengths.reduce((sum, length) => sum + length, 0) / sessionLengths.length;
  }
}

export const chatLogService = new ChatLogService();