// OpenAI Agents integration with function calling and vector embeddings
import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY!
});

// Tool definitions for each specialized agent
const agentTools = {
  designer: [
    {
      type: 'function' as const,
      function: {
        name: 'generate_tattoo_design',
        description: 'Generate a tattoo design image using DALL-E with detailed prompts optimized for tattoo artwork',
        parameters: {
          type: 'object',
          properties: {
            prompt: {
              type: 'string',
              description: 'Detailed tattoo design prompt including style, subject, placement considerations'
            },
            style: {
              type: 'string',
              enum: ['traditional', 'realism', 'geometric', 'watercolor', 'blackwork', 'dotwork', 'minimalist'],
              description: 'Tattoo style'
            },
            size: {
              type: 'string',
              enum: ['1024x1024', '1792x1024', '1024x1792'],
              description: 'Image dimensions'
            }
          },
          required: ['prompt']
        }
      }
    },
    {
      type: 'function' as const,
      function: {
        name: 'refine_design',
        description: 'Refine an existing design based on user feedback',
        parameters: {
          type: 'object',
          properties: {
            originalPrompt: { type: 'string', description: 'Original design prompt' },
            refinements: { type: 'string', description: 'Requested changes or refinements' },
            style: { type: 'string', description: 'Tattoo style to maintain or change to' }
          },
          required: ['originalPrompt', 'refinements']
        }
      }
    }
  ],
  analyst: [
    {
      type: 'function' as const,
      function: {
        name: 'analyze_tattoo_image',
        description: 'Analyze a tattoo image for style, technique, quality, and provide detailed feedback',
        parameters: {
          type: 'object',
          properties: {
            imageUrl: { type: 'string', description: 'URL of the tattoo image to analyze' },
            analysisType: {
              type: 'string',
              enum: ['style_identification', 'technical_assessment', 'aging_prediction', 'comprehensive_review'],
              description: 'Type of analysis to perform'
            }
          },
          required: ['imageUrl']
        }
      }
    },
    {
      type: 'function' as const,
      function: {
        name: 'compare_designs',
        description: 'Compare multiple tattoo designs and provide recommendations',
        parameters: {
          type: 'object',
          properties: {
            designs: {
              type: 'array',
              items: { type: 'string' },
              description: 'Array of image URLs to compare'
            },
            criteria: { type: 'string', description: 'Comparison criteria (style, quality, placement, etc.)' }
          },
          required: ['designs']
        }
      }
    }
  ],
  guidance: [
    {
      type: 'function' as const,
      function: {
        name: 'search_tattoo_knowledge',
        description: 'Search the tattoo knowledge base for style information, cultural significance, and placement advice',
        parameters: {
          type: 'object',
          properties: {
            query: { type: 'string', description: 'Search query for tattoo knowledge' },
            category: {
              type: 'string',
              enum: ['styles', 'cultural_significance', 'placement', 'aftercare', 'pain_levels'],
              description: 'Knowledge category to search'
            }
          },
          required: ['query']
        }
      }
    },
    {
      type: 'function' as const,
      function: {
        name: 'get_placement_advice',
        description: 'Get specific advice about tattoo placement including pain levels, healing, and visibility',
        parameters: {
          type: 'object',
          properties: {
            bodyPart: { type: 'string', description: 'Body part for tattoo placement' },
            designSize: { type: 'string', enum: ['small', 'medium', 'large'], description: 'Size of the design' },
            lifestyle: { type: 'string', description: 'User lifestyle considerations (professional, active, etc.)' }
          },
          required: ['bodyPart']
        }
      }
    }
  ],
  aftercare: [
    {
      type: 'function' as const,
      function: {
        name: 'get_aftercare_instructions',
        description: 'Provide detailed aftercare instructions based on tattoo type and healing stage',
        parameters: {
          type: 'object',
          properties: {
            tattooAge: { type: 'string', enum: ['fresh', 'day1-3', 'week1', 'week2-4', 'healed'], description: 'Age of the tattoo' },
            tattooSize: { type: 'string', enum: ['small', 'medium', 'large'], description: 'Size of the tattoo' },
            location: { type: 'string', description: 'Body location of the tattoo' },
            issues: { type: 'string', description: 'Any healing issues or concerns' }
          },
          required: ['tattooAge']
        }
      }
    }
  ],
  booking: [
    {
      type: 'function' as const,
      function: {
        name: 'estimate_session_time',
        description: 'Estimate tattoo session time and provide scheduling advice',
        parameters: {
          type: 'object',
          properties: {
            designComplexity: { type: 'string', enum: ['simple', 'moderate', 'complex', 'very_complex'] },
            size: { type: 'string', enum: ['small', 'medium', 'large', 'extra_large'] },
            style: { type: 'string', description: 'Tattoo style' },
            location: { type: 'string', description: 'Body location' }
          },
          required: ['designComplexity', 'size']
        }
      }
    }
  ]
};

export class TattooAgentsService {
  private vectorStore: any = null; // Will implement OpenAI vector store
  
  constructor() {
    this.initializeVectorStore();
  }

  private async initializeVectorStore() {
    try {
      // Initialize OpenAI vector store for tattoo knowledge
      // This will store tattoo style information, cultural data, aftercare knowledge
      console.log('Initializing vector store for tattoo knowledge...');
      // Implementation will follow OpenAI's vector store API
    } catch (error) {
      console.error('Vector store initialization failed:', error);
    }
  }

  // Generate tattoo design using DALL-E
  async generateTattooDesign(prompt: string, style?: string, size: string = '1024x1024'): Promise<string> {
    try {
      // Enhance prompt for tattoo-specific generation
      const enhancedPrompt = this.enhanceTattooPrompt(prompt, style);
      
      const response = await openai.images.generate({
        model: 'dall-e-3',
        prompt: enhancedPrompt,
        size: size as '1024x1024' | '1792x1024' | '1024x1792',
        quality: 'hd',
        n: 1
      });

      return response.data[0].url || '';
    } catch (error) {
      console.error('Error generating tattoo design:', error);
      throw new Error('Failed to generate tattoo design');
    }
  }

  // Analyze tattoo image using GPT-4V
  async analyzeTattooImage(imageUrl: string, analysisType: string = 'comprehensive_review'): Promise<string> {
    try {
      const response = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: this.getAnalysisPrompt(analysisType)
              },
              {
                type: 'image_url',
                image_url: {
                  url: imageUrl
                }
              }
            ]
          }
        ],
        max_tokens: 1000
      });

      return response.choices[0].message.content || 'Unable to analyze image';
    } catch (error) {
      console.error('Error analyzing tattoo image:', error);
      throw new Error('Failed to analyze tattoo image');
    }
  }

  // Enhanced prompt engineering for tattoo designs
  private enhanceTattooPrompt(userPrompt: string, style?: string): string {
    let enhancedPrompt = '';
    
    if (style) {
      enhancedPrompt += `${style} tattoo style, `;
    }
    
    enhancedPrompt += `professional tattoo design of ${userPrompt}, `;
    enhancedPrompt += 'high contrast black lines, designed for skin application, ';
    enhancedPrompt += 'clean line work, appropriate shading, tattoo flash style, ';
    enhancedPrompt += 'on white background, professional tattoo art quality';

    return enhancedPrompt;
  }

  // Get analysis prompt based on type
  private getAnalysisPrompt(analysisType: string): string {
    switch (analysisType) {
      case 'style_identification':
        return 'Identify the tattoo style, artistic influences, and design characteristics visible in this image. Focus on technical elements like line work, shading techniques, and color usage.';
      
      case 'technical_assessment':
        return 'Assess the technical execution of this tattoo, including line quality, shading consistency, color saturation, and overall craftsmanship. Consider how well it will age over time.';
      
      case 'comprehensive_review':
      default:
        return 'Provide a comprehensive analysis of this tattoo including: style identification, technical assessment, artistic qualities, potential improvements, and how it might age over time. Be constructive and educational in your feedback.';
    }
  }

  // Advanced agent runner with function calling and tool access
  async runAgent(agentId: string, message: string, imageUrl?: string): Promise<{
    content: string;
    toolsUsed?: string[];
    functionCall?: {
      name: string;
      arguments: any;
      result: any;
    };
    imageUrl?: string;
  }> {
    try {
      const systemPrompt = this.getSystemPromptForAgent(agentId);
      const tools = agentTools[agentId as keyof typeof agentTools] || [];
      
      // Prepare messages array
      const messages: any[] = [
        { role: 'system', content: systemPrompt }
      ];

      // Add user message with optional image
      if (imageUrl && (agentId === 'analyst' || agentId === 'designer')) {
        messages.push({
          role: 'user',
          content: [
            { type: 'text', text: message },
            { type: 'image_url', image_url: { url: imageUrl } }
          ]
        });
      } else {
        messages.push({ role: 'user', content: message });
      }

      // Call OpenAI with function calling capability
      const response = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages,
        tools: tools.length > 0 ? tools : undefined,
        tool_choice: tools.length > 0 ? 'auto' : undefined,
        temperature: 0.7,
        max_tokens: 1500
      });

      const choice = response.choices[0];
      
      // Handle function calls
      if (choice.message.tool_calls && choice.message.tool_calls.length > 0) {
        const toolCall = choice.message.tool_calls[0];
        const functionName = toolCall.function.name;
        const functionArgs = JSON.parse(toolCall.function.arguments);
        
        // Execute the function and get result
        const functionResult = await this.executeTool(functionName, functionArgs);
        
        // Add function call and result to conversation
        messages.push(choice.message);
        messages.push({
          role: 'tool',
          tool_call_id: toolCall.id,
          content: JSON.stringify(functionResult)
        });
        
        // Get final response from agent
        const finalResponse = await openai.chat.completions.create({
          model: 'gpt-4o',
          messages,
          temperature: 0.7,
          max_tokens: 1000
        });
        
        const content = finalResponse.choices[0].message.content || 'Function executed successfully';
        
        // Return structured response with tool usage information
        return {
          content,
          toolsUsed: [functionName],
          functionCall: {
            name: functionName,
            arguments: functionArgs,
            result: functionResult
          },
          imageUrl: functionResult?.imageUrl // Include image URL if generated
        };
      }

      return {
        content: choice.message.content || 'No response generated'
      };
    } catch (error) {
      console.error(`Error running agent ${agentId}:`, error);
      throw new Error(`Failed to run agent: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Legacy method for backward compatibility
  async runAgentSimple(agentId: string, message: string, imageUrl?: string): Promise<string> {
    const result = await this.runAgent(agentId, message, imageUrl);
    return result.content;
  }

  // Execute agent-specific tools
  private async executeTool(functionName: string, args: any): Promise<any> {
    try {
      switch (functionName) {
        case 'generate_tattoo_design':
          return await this.generateTattooDesign(args.prompt, args.style, args.size);
          
        case 'refine_design':
          return await this.refineDesign(args.originalPrompt, args.refinements, args.style);
          
        case 'analyze_tattoo_image':
          return await this.analyzeTattooImage(args.imageUrl, args.analysisType);
          
        case 'compare_designs':
          return await this.compareDesigns(args.designs, args.criteria);
          
        case 'search_tattoo_knowledge':
          return await this.searchTattooKnowledge(args.query, args.category);
          
        case 'get_placement_advice':
          return await this.getPlacementAdvice(args.bodyPart, args.designSize, args.lifestyle);
          
        case 'get_aftercare_instructions':
          return await this.getAftercareInstructions(args.tattooAge, args.tattooSize, args.location, args.issues);
          
        case 'estimate_session_time':
          return await this.estimateSessionTime(args.designComplexity, args.size, args.style, args.location);
          
        default:
          throw new Error(`Unknown function: ${functionName}`);
      }
    } catch (error) {
      console.error(`Error executing tool ${functionName}:`, error);
      return { error: `Failed to execute ${functionName}: ${error instanceof Error ? error.message : 'Unknown error'}` };
    }
  }

  // Get system prompts for different agents
  private getSystemPromptForAgent(agentId: string): string {
    const prompts = {
      designer: `You are an expert tattoo designer specializing in creating and visualizing tattoo concepts. You provide:
      - Technical advice on tattoo execution and design optimization
      - Style-specific guidance and recommendations
      - Suggestions for modifications that improve tattoo longevity
      - Detailed explanations of design elements and techniques
      
      Focus on creating designs that will translate well to skin and age gracefully. Consider line work quality, shading techniques, and placement considerations.`,
      
      analyst: `You are an expert tattoo analyst who examines and provides insights on tattoo artwork. You provide:
      - Detailed analysis of tattoo styles and techniques
      - Assessment of artistic execution and technical quality
      - Identification of artistic influences and cultural significance
      - Constructive feedback on composition and design elements
      
      When analyzing tattoos, be educational and respectful while providing honest, constructive feedback about both strengths and areas for improvement.`,
      
      manager: `You are a tattoo consultation manager who helps coordinate between different specialists. You:
      - Route queries to appropriate specialists
      - Provide general tattoo information and guidance
      - Help users understand the tattoo process
      - Coordinate between design, analysis, and aftercare needs`,
      
      guidance: `You are a tattoo guidance counselor specializing in helping people make informed decisions about tattoos. You provide:
      - Style recommendations based on preferences and lifestyle
      - Placement advice considering pain levels and professional considerations
      - Cultural and symbolic meaning explanations
      - First-time tattoo guidance and preparation advice`,
      
      aftercare: `You are a tattoo aftercare specialist focused on healing and maintenance. You provide:
      - Detailed aftercare instructions for different healing stages
      - Troubleshooting for common healing issues
      - Long-term maintenance and touch-up guidance
      - Product recommendations for tattoo care`,
      
      booking: `You are a tattoo booking coordinator who helps with appointment and consultation logistics. You provide:
      - Information about consultation processes
      - Guidance on preparing for tattoo appointments
      - Explanation of typical timelines and pricing structures
      - Advice on what to bring to consultations`
    };

    return prompts[agentId as keyof typeof prompts] || prompts.manager;
  }

  // Tool implementation methods (missing methods added)
  private async refineDesign(originalPrompt: string, refinements: string, style?: string): Promise<any> {
    const refinedPrompt = `${originalPrompt} with these modifications: ${refinements}${style ? ` maintaining ${style} style` : ''}. Professional tattoo artwork.`;
    return await this.generateTattooDesign(refinedPrompt, style);
  }

  private async compareDesigns(designs: string[], criteria?: string): Promise<any> {
    return {
      comparison: `Compared ${designs.length} designs based on ${criteria || 'overall quality'}`,
      designs,
      criteria
    };
  }

  private async searchTattooKnowledge(query: string, category?: string): Promise<any> {
    return {
      query,
      category,
      results: `Knowledge about ${query} in ${category || 'general'} category`
    };
  }

  private async getPlacementAdvice(bodyPart: string, designSize?: string, lifestyle?: string): Promise<any> {
    return {
      bodyPart,
      designSize,
      lifestyle,
      advice: `Placement advice for ${bodyPart} with ${designSize || 'unspecified'} size design`,
      painLevel: 'moderate',
      healingConsiderations: 'Standard healing considerations apply'
    };
  }

  private async getAftercareInstructions(tattooAge: string, tattooSize?: string, location?: string, issues?: string): Promise<any> {
    return {
      tattooAge,
      tattooSize,
      location,
      issues,
      instructions: `Aftercare instructions for ${tattooAge} tattoo`,
      nextSteps: 'Continue following aftercare protocol'
    };
  }

  private async estimateSessionTime(designComplexity: string, size: string, style?: string, location?: string): Promise<any> {
    const timeEstimates = {
      simple: { small: '1-2 hours', medium: '2-4 hours', large: '4-6 hours' },
      moderate: { small: '2-3 hours', medium: '4-6 hours', large: '6-8 hours' },
      complex: { small: '3-4 hours', medium: '6-8 hours', large: '8-12 hours' },
      very_complex: { small: '4-6 hours', medium: '8-12 hours', large: '12+ hours' }
    };

    const estimate = timeEstimates[designComplexity as keyof typeof timeEstimates]?.[size as keyof typeof timeEstimates.simple] || 'Contact artist for estimate';

    return {
      designComplexity,
      size,
      style,
      location,
      estimatedTime: estimate,
      sessions: estimate.includes('+') ? 'Multiple sessions recommended' : 'Single session possible',
      pricing: 'Contact artist for pricing based on time estimate'
    };
  }

  // Handle function calls from agents
  async handleFunctionCall(functionName: string, parameters: any): Promise<any> {
    switch (functionName) {
      case 'generate_tattoo_design':
        return await this.generateTattooDesign(
          parameters.prompt,
          parameters.style,
          parameters.size
        );
      
      case 'analyze_tattoo_image':
        return await this.analyzeTattooImage(
          parameters.image_url,
          parameters.analysis_type
        );
      
      default:
        throw new Error(`Unknown function: ${functionName}`);
    }
  }
}

// Export singleton instance
export const tattooAgentsService = new TattooAgentsService();