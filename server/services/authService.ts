import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { storage } from '../storage';
import { secretsManager } from './secretsManager';
import type { User } from '@shared/schema';

const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';
const BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS || '12');

export interface AuthTokenPayload {
  userId: number;
  username: string;
  role: string;
  iat: number;
  exp: number;
}

export interface LoginResult {
  user: Omit<User, 'password'>;
  token: string;
  expiresAt: Date;
}

class AuthService {
  /**
   * Hash a password using bcrypt
   */
  async hashPassword(password: string): Promise<string> {
    try {
      return await bcrypt.hash(password, BCRYPT_ROUNDS);
    } catch (error) {
      throw new Error('Failed to hash password');
    }
  }

  /**
   * Verify a password against its hash
   */
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      return false;
    }
  }

  /**
   * Generate a JWT token for a user
   */
  generateToken(user: User): string {
    const jwtSecret = secretsManager.getSecretOrEnv('JWT_SECRET');
    if (!jwtSecret) {
      throw new Error('JWT_SECRET not configured');
    }

    const payload: Omit<AuthTokenPayload, 'iat' | 'exp'> = {
      userId: user.id,
      username: user.username,
      role: user.role
    };

    return jwt.sign(payload, jwtSecret, {
      expiresIn: JWT_EXPIRES_IN,
      issuer: 'tattoo-admin',
      audience: 'tattoo-admin-users'
    });
  }

  /**
   * Verify and decode a JWT token
   */
  verifyToken(token: string): AuthTokenPayload | null {
    try {
      const jwtSecret = secretsManager.getSecretOrEnv('JWT_SECRET');
      if (!jwtSecret) {
        console.error('JWT_SECRET not configured');
        return null;
      }

      const decoded = jwt.verify(token, jwtSecret, {
        issuer: 'tattoo-admin',
        audience: 'tattoo-admin-users'
      }) as AuthTokenPayload;

      return decoded;
    } catch (error) {
      console.error('Token verification failed:', error);
      return null;
    }
  }

  /**
   * Authenticate user with username and password
   */
  async login(username: string, password: string): Promise<LoginResult> {
    // Input validation
    if (!username || !password) {
      throw new Error('Username and password are required');
    }

    if (username.length < 3 || username.length > 50) {
      throw new Error('Username must be between 3 and 50 characters');
    }

    if (password.length < 6) {
      throw new Error('Password must be at least 6 characters');
    }

    // Get user from storage
    const user = await storage.getUserByUsername(username);
    if (!user) {
      throw new Error('Invalid credentials');
    }

    // Check if user is active
    if (user.status !== 'active') {
      throw new Error('Account is not active');
    }

    // Verify password
    const isValidPassword = await this.verifyPassword(password, user.password);
    if (!isValidPassword) {
      throw new Error('Invalid credentials');
    }

    // Generate token
    const token = this.generateToken(user);
    const decoded = this.verifyToken(token);
    const expiresAt = new Date(decoded!.exp * 1000);

    // Remove password from user object
    const { password: _, ...userWithoutPassword } = user;

    return {
      user: userWithoutPassword,
      token,
      expiresAt
    };
  }

  /**
   * Get user from token
   */
  async getUserFromToken(token: string): Promise<User | null> {
    const decoded = this.verifyToken(token);
    if (!decoded) {
      return null;
    }

    const user = await storage.getUser(decoded.userId);
    if (!user || user.status !== 'active') {
      return null;
    }

    return user;
  }

  /**
   * Middleware to extract and verify JWT from Authorization header
   */
  extractTokenFromHeader(authHeader: string | undefined): string | null {
    if (!authHeader) {
      return null;
    }

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }

    return parts[1];
  }

  /**
   * Validate token and return user (for middleware use)
   */
  async validateTokenAndGetUser(authHeader: string | undefined): Promise<User | null> {
    const token = this.extractTokenFromHeader(authHeader);
    if (!token) {
      return null;
    }

    return await this.getUserFromToken(token);
  }

  /**
   * Create a new user with hashed password
   */
  async createUser(userData: {
    username: string;
    password: string;
    email: string;
    firstName: string;
    lastName: string;
    role?: 'admin' | 'artist' | 'client';
  }): Promise<Omit<User, 'password'>> {
    // Validate input
    if (!userData.username || !userData.password || !userData.email) {
      throw new Error('Username, password, and email are required');
    }

    if (userData.password.length < 8) {
      throw new Error('Password must be at least 8 characters');
    }

    // Check if user already exists
    const existingUser = await storage.getUserByUsername(userData.username);
    if (existingUser) {
      throw new Error('Username already exists');
    }

    // Hash password
    const hashedPassword = await this.hashPassword(userData.password);

    // Create user
    const newUser = await storage.createUser({
      ...userData,
      password: hashedPassword,
      role: userData.role || 'client',
      status: 'active'
    });

    // Remove password from response
    const { password: _, ...userWithoutPassword } = newUser;
    return userWithoutPassword;
  }

  /**
   * Change user password
   */
  async changePassword(userId: number, currentPassword: string, newPassword: string): Promise<boolean> {
    const user = await storage.getUser(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Verify current password
    const isValidPassword = await this.verifyPassword(currentPassword, user.password);
    if (!isValidPassword) {
      throw new Error('Current password is incorrect');
    }

    // Validate new password
    if (newPassword.length < 8) {
      throw new Error('New password must be at least 8 characters');
    }

    // Hash new password
    const hashedPassword = await this.hashPassword(newPassword);

    // Update user
    await storage.updateUser(userId, { password: hashedPassword });
    return true;
  }
}

export const authService = new AuthService();
