import OpenAI from 'openai';

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

export interface KnowledgeDocument {
  id: string;
  content: string;
  metadata: {
    title: string;
    category: 'tattoo_styles' | 'aftercare' | 'safety' | 'pricing' | 'consultation' | 'design';
    tags: string[];
    source: string;
    lastUpdated: Date;
  };
  embedding?: number[];
}

export interface SearchResult {
  document: KnowledgeDocument;
  similarity: number;
  relevantChunk: string;
}

class VectorService {
  private documents: Map<string, KnowledgeDocument> = new Map();
  private embeddings: Map<string, number[]> = new Map();

  // Add document to knowledge base
  async addDocument(document: Omit<KnowledgeDocument, 'id' | 'embedding'>) {
    const id = `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      // Generate embedding using OpenAI
      const embedding = await this.generateEmbedding(document.content);
      
      const knowledgeDoc: KnowledgeDocument = {
        id,
        ...document,
        embedding
      };

      this.documents.set(id, knowledgeDoc);
      this.embeddings.set(id, embedding);

      console.log(`Added document to knowledge base: ${id}`);
      return knowledgeDoc;
    } catch (error) {
      console.error('Error adding document:', error);
      throw error;
    }
  }

  // Search knowledge base using semantic similarity
  async searchKnowledge(query: string, limit: number = 5, category?: string): Promise<SearchResult[]> {
    try {
      // Generate embedding for the query
      const queryEmbedding = await this.generateEmbedding(query);

      // Calculate similarities
      const results: SearchResult[] = [];

      for (const [docId, document] of this.documents.entries()) {
        // Filter by category if specified
        if (category && document.metadata.category !== category) {
          continue;
        }

        const docEmbedding = this.embeddings.get(docId);
        if (!docEmbedding) continue;

        const similarity = this.calculateCosineSimilarity(queryEmbedding, docEmbedding);
        
        if (similarity > 0.7) { // Similarity threshold
          results.push({
            document,
            similarity,
            relevantChunk: this.extractRelevantChunk(document.content, query)
          });
        }
      }

      // Sort by similarity and return top results
      return results
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit);
    } catch (error) {
      console.error('Error searching knowledge base:', error);
      return [];
    }
  }

  // Generate contextual response using knowledge base
  async generateContextualResponse(query: string, agentType: string): Promise<string> {
    try {
      // Search relevant documents
      const searchResults = await this.searchKnowledge(query, 3);
      
      if (searchResults.length === 0) {
        return "I don't have specific information about that in my knowledge base. Let me help you with general tattoo guidance.";
      }

      // Prepare context from search results
      const context = searchResults
        .map(result => `${result.document.metadata.title}: ${result.relevantChunk}`)
        .join('\n\n');

      // Generate response using OpenAI with context
      const prompt = `You are a ${agentType} tattoo specialist. Based on the following knowledge base information, provide a helpful and accurate response to the user's question.

Knowledge Base Context:
${context}

User Question: ${query}

Provide a comprehensive response that uses the knowledge base information while maintaining your role as a ${agentType} specialist.`;

      const response = await openai.chat.completions.create({
        model: "gpt-4", // the newest OpenAI model is "gpt-5" which was released August 7, 2025. do not change this unless explicitly requested by the user
        messages: [{ role: "user", content: prompt }],
        max_tokens: 500,
        temperature: 0.7
      });

      return response.choices[0].message.content || "I'm having trouble generating a response right now.";
    } catch (error) {
      console.error('Error generating contextual response:', error);
      return "I'm experiencing some technical difficulties. Please try again.";
    }
  }

  // Generate embedding using OpenAI
  private async generateEmbedding(text: string): Promise<number[]> {
    const response = await openai.embeddings.create({
      model: "text-embedding-3-small",
      input: text
    });

    return response.data[0].embedding;
  }

  // Calculate cosine similarity between two vectors
  private calculateCosineSimilarity(vecA: number[], vecB: number[]): number {
    const dotProduct = vecA.reduce((sum, a, i) => sum + a * vecB[i], 0);
    const magnitudeA = Math.sqrt(vecA.reduce((sum, a) => sum + a * a, 0));
    const magnitudeB = Math.sqrt(vecB.reduce((sum, b) => sum + b * b, 0));
    
    return dotProduct / (magnitudeA * magnitudeB);
  }

  // Extract most relevant chunk from document content
  private extractRelevantChunk(content: string, query: string): string {
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const queryWords = query.toLowerCase().split(/\s+/);
    
    let bestSentence = sentences[0] || content.substring(0, 200);
    let maxScore = 0;

    for (const sentence of sentences) {
      const sentenceWords = sentence.toLowerCase().split(/\s+/);
      const score = queryWords.filter(word => 
        sentenceWords.some(sw => sw.includes(word))
      ).length;

      if (score > maxScore) {
        maxScore = score;
        bestSentence = sentence;
      }
    }

    return bestSentence.trim();
  }

  // Initialize with default tattoo knowledge
  async initializeDefaultKnowledge() {
    const defaultDocs = [
      {
        content: "Traditional tattoos, also known as American Traditional or Old School tattoos, are characterized by bold lines, solid colors, and classic imagery like anchors, roses, skulls, and pin-up girls. This style originated in the early 20th century and remains popular today.",
        metadata: {
          title: "Traditional Tattoo Style Guide",
          category: "tattoo_styles" as const,
          tags: ["traditional", "old school", "bold lines", "classic"],
          source: "Tattoo Style Encyclopedia",
          lastUpdated: new Date()
        }
      },
      {
        content: "Proper tattoo aftercare is crucial for healing and maintaining the quality of your tattoo. Keep the tattoo clean and dry, apply a thin layer of unscented moisturizer, avoid soaking in water for 2-3 weeks, and stay out of direct sunlight. Do not pick at scabs or peeling skin.",
        metadata: {
          title: "Essential Tattoo Aftercare Instructions",
          category: "aftercare" as const,
          tags: ["aftercare", "healing", "moisturizer", "sun protection"],
          source: "Professional Tattoo Care Guide",
          lastUpdated: new Date()
        }
      },
      {
        content: "Realism tattoos aim to replicate photographs or real-life subjects with incredible detail and accuracy. This style requires exceptional skill and often takes multiple sessions to complete. Black and grey realism focuses on shading, while color realism incorporates vibrant hues.",
        metadata: {
          title: "Realism Tattoo Techniques",
          category: "tattoo_styles" as const,
          tags: ["realism", "photorealistic", "detailed", "multiple sessions"],
          source: "Advanced Tattoo Techniques",
          lastUpdated: new Date()
        }
      }
    ];

    console.log('Initializing default knowledge base...');
    for (const doc of defaultDocs) {
      await this.addDocument(doc);
    }
    console.log(`Initialized knowledge base with ${defaultDocs.length} documents`);
  }

  // Get all documents (for management)
  getAllDocuments(): KnowledgeDocument[] {
    return Array.from(this.documents.values());
  }

  // Remove document
  removeDocument(id: string): boolean {
    const removed = this.documents.delete(id);
    this.embeddings.delete(id);
    return removed;
  }

  // Update document
  async updateDocument(id: string, updates: Partial<Omit<KnowledgeDocument, 'id' | 'embedding'>>) {
    const existing = this.documents.get(id);
    if (!existing) {
      throw new Error(`Document ${id} not found`);
    }

    const updated = { ...existing, ...updates };
    
    // Regenerate embedding if content changed
    if (updates.content && updates.content !== existing.content) {
      updated.embedding = await this.generateEmbedding(updates.content);
      this.embeddings.set(id, updated.embedding);
    }

    this.documents.set(id, updated);
    return updated;
  }
}

export const vectorService = new VectorService();