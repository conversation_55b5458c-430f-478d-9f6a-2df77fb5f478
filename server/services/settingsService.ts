export interface APISettings {
  id: string;
  name: string;
  apiKey: string;
  endpoint?: string;
  status: 'active' | 'inactive' | 'error';
  lastUpdated: Date;
  usage?: {
    requestCount: number;
    lastRequest: Date;
    monthlyLimit?: number;
  };
}

export interface SaaSSetting {
  id: string;
  category: 'ai_services' | 'databases' | 'authentication' | 'hosting' | 'analytics';
  provider: string;
  settings: Record<string, any>;
  encrypted: boolean;
  lastUpdated: Date;
}

class SettingsService {
  private apiSettings: Map<string, APISettings> = new Map();
  private saasSettings: Map<string, SaaSSetting> = new Map();

  // Initialize with default API settings structure
  constructor() {
    this.initializeDefaultSettings();
  }

  private initializeDefaultSettings() {
    const defaultAPIs = [
      {
        id: 'openai',
        name: 'OpenAI',
        apiKey: process.env.OPENAI_API_KEY || '',
        endpoint: 'https://api.openai.com/v1',
        status: 'active' as const,
        lastUpdated: new Date()
      },
      {
        id: 'leonardo',
        name: '<PERSON>',
        apiKey: '',
        endpoint: 'https://cloud.leonardo.ai/api/rest/v1',
        status: 'inactive' as const,
        lastUpdated: new Date()
      },
      {
        id: 'flux',
        name: 'Flux',
        apiKey: '',
        endpoint: '',
        status: 'inactive' as const,
        lastUpdated: new Date()
      },
      {
        id: 'ideogram',
        name: 'Ideogram',
        apiKey: '',
        endpoint: 'https://api.ideogram.ai/v1',
        status: 'inactive' as const,
        lastUpdated: new Date()
      },
      {
        id: 'airtable',
        name: 'Airtable',
        apiKey: '',
        endpoint: 'https://api.airtable.com/v0',
        status: 'inactive' as const,
        lastUpdated: new Date()
      }
    ];

    defaultAPIs.forEach(api => {
      this.apiSettings.set(api.id, api);
    });

    // Initialize SaaS settings
    const defaultSaaSSettings = [
      {
        id: 'firebase_auth',
        category: 'authentication' as const,
        provider: 'Firebase',
        settings: {
          projectId: '',
          apiKey: '',
          authDomain: '',
          enabled: false
        },
        encrypted: true,
        lastUpdated: new Date()
      },
      {
        id: 'google_cloud',
        category: 'hosting' as const,
        provider: 'Google Cloud Platform',
        settings: {
          projectId: '',
          credentials: '',
          region: 'us-central1',
          enabled: false
        },
        encrypted: true,
        lastUpdated: new Date()
      },
      {
        id: 'vector_database',
        category: 'databases' as const,
        provider: 'OpenAI Embeddings',
        settings: {
          embeddingModel: 'text-embedding-3-small',
          maxTokens: 8191,
          enabled: true
        },
        encrypted: false,
        lastUpdated: new Date()
      }
    ];

    defaultSaaSSettings.forEach(setting => {
      this.saasSettings.set(setting.id, setting);
    });
  }

  // API Settings Management
  async getAPISettings(): Promise<APISettings[]> {
    return Array.from(this.apiSettings.values()).map(setting => ({
      ...setting,
      // Don't expose actual API keys in response
      apiKey: setting.apiKey ? '***********' + setting.apiKey.slice(-4) : ''
    }));
  }

  async getAPISettingById(id: string): Promise<APISettings | null> {
    const setting = this.apiSettings.get(id);
    if (!setting) return null;

    return {
      ...setting,
      apiKey: setting.apiKey ? '***********' + setting.apiKey.slice(-4) : ''
    };
  }

  async updateAPISettings(id: string, updates: Partial<APISettings>): Promise<APISettings> {
    const existing = this.apiSettings.get(id);
    if (!existing) {
      throw new Error(`API setting ${id} not found`);
    }

    const updated = {
      ...existing,
      ...updates,
      lastUpdated: new Date()
    };

    // Validate API key if provided
    if (updates.apiKey && updates.apiKey !== existing.apiKey) {
      updated.status = await this.validateAPIKey(id, updates.apiKey) ? 'active' : 'error';
    }

    this.apiSettings.set(id, updated);
    return updated;
  }

  async createCustomAPISettings(setting: Omit<APISettings, 'lastUpdated'>): Promise<APISettings> {
    const newSetting = {
      ...setting,
      lastUpdated: new Date()
    };

    // Validate API key
    if (setting.apiKey) {
      newSetting.status = await this.validateAPIKey(setting.id, setting.apiKey) ? 'active' : 'error';
    }

    this.apiSettings.set(setting.id, newSetting);
    return newSetting;
  }

  // SaaS Settings Management
  async getSaaSSettings(): Promise<SaaSSetting[]> {
    return Array.from(this.saasSettings.values()).map(setting => ({
      ...setting,
      settings: setting.encrypted ? this.maskSensitiveSettings(setting.settings) : setting.settings
    }));
  }

  async getSaaSSettingById(id: string): Promise<SaaSSetting | null> {
    const setting = this.saasSettings.get(id);
    if (!setting) return null;

    return {
      ...setting,
      settings: setting.encrypted ? this.maskSensitiveSettings(setting.settings) : setting.settings
    };
  }

  async updateSaaSSetting(id: string, updates: Partial<SaaSSetting>): Promise<SaaSSetting> {
    const existing = this.saasSettings.get(id);
    if (!existing) {
      throw new Error(`SaaS setting ${id} not found`);
    }

    const updated = {
      ...existing,
      ...updates,
      lastUpdated: new Date()
    };

    this.saasSettings.set(id, updated);
    return updated;
  }

  // API Key Validation
  private async validateAPIKey(provider: string, apiKey: string): Promise<boolean> {
    try {
      switch (provider) {
        case 'openai':
          return await this.validateOpenAIKey(apiKey);
        case 'leonardo':
          return await this.validateLeonardoKey(apiKey);
        case 'airtable':
          return await this.validateAirtableKey(apiKey);
        default:
          return true; // Assume valid for unknown providers
      }
    } catch (error) {
      console.error(`Error validating ${provider} API key:`, error);
      return false;
    }
  }

  private async validateOpenAIKey(apiKey: string): Promise<boolean> {
    try {
      const response = await fetch('https://api.openai.com/v1/models', {
        headers: { 'Authorization': `Bearer ${apiKey}` }
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  private async validateLeonardoKey(apiKey: string): Promise<boolean> {
    try {
      const response = await fetch('https://cloud.leonardo.ai/api/rest/v1/me', {
        headers: { 'Authorization': `Bearer ${apiKey}` }
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  private async validateAirtableKey(apiKey: string): Promise<boolean> {
    try {
      const response = await fetch('https://api.airtable.com/v0/meta/whoami', {
        headers: { 'Authorization': `Bearer ${apiKey}` }
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  // Utility methods
  private maskSensitiveSettings(settings: Record<string, any>): Record<string, any> {
    const masked = { ...settings };
    const sensitiveKeys = ['apiKey', 'credentials', 'password', 'secret', 'token'];
    
    for (const key in masked) {
      if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
        if (typeof masked[key] === 'string' && masked[key].length > 0) {
          masked[key] = '***********' + masked[key].slice(-4);
        }
      }
    }
    
    return masked;
  }

  // Get actual API key for internal use
  async getActualAPIKey(provider: string): Promise<string | null> {
    const setting = this.apiSettings.get(provider);
    return setting?.apiKey || null;
  }

  // Usage tracking
  async trackAPIUsage(provider: string) {
    const setting = this.apiSettings.get(provider);
    if (!setting) return;

    if (!setting.usage) {
      setting.usage = {
        requestCount: 0,
        lastRequest: new Date()
      };
    }

    setting.usage.requestCount++;
    setting.usage.lastRequest = new Date();
    
    this.apiSettings.set(provider, setting);
  }

  // Export/Import settings for migration
  async exportSettings(): Promise<{
    apiSettings: APISettings[];
    saasSettings: SaaSSetting[];
    exportDate: Date;
  }> {
    return {
      apiSettings: Array.from(this.apiSettings.values()),
      saasSettings: Array.from(this.saasSettings.values()),
      exportDate: new Date()
    };
  }

  async importSettings(data: {
    apiSettings?: APISettings[];
    saasSettings?: SaaSSetting[];
  }) {
    if (data.apiSettings) {
      data.apiSettings.forEach(setting => {
        this.apiSettings.set(setting.id, setting);
      });
    }

    if (data.saasSettings) {
      data.saasSettings.forEach(setting => {
        this.saasSettings.set(setting.id, setting);
      });
    }
  }
}

export const settingsService = new SettingsService();