import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

interface EncryptedSecret {
  value: string;
  iv: string;
  tag: string;
  createdAt: Date;
  lastRotated?: Date;
  rotationInterval?: number; // in days
}

interface SecretMetadata {
  name: string;
  description?: string;
  category: 'api_key' | 'database' | 'jwt' | 'encryption' | 'external_service';
  required: boolean;
  rotationInterval?: number;
}

class SecretsManager {
  private masterKey: Buffer;
  private secrets: Map<string, EncryptedSecret> = new Map();
  private secretsFilePath: string;
  private algorithm = 'aes-256-gcm';

  constructor() {
    this.secretsFilePath = path.join(process.cwd(), '.secrets.enc');
    this.masterKey = this.getMasterKey();
    this.loadSecrets();
  }

  /**
   * Get or generate master encryption key
   */
  private getMasterKey(): Buffer {
    const keyPath = path.join(process.cwd(), '.master.key');
    
    try {
      if (fs.existsSync(keyPath)) {
        return fs.readFileSync(keyPath);
      }
    } catch (error) {
      console.warn('Could not read master key file');
    }

    // Generate new master key
    const masterKey = crypto.randomBytes(32);
    
    try {
      fs.writeFileSync(keyPath, masterKey, { mode: 0o600 });
      console.log('Generated new master encryption key');
    } catch (error) {
      console.error('Could not save master key:', error);
    }

    return masterKey;
  }

  /**
   * Encrypt a secret value
   */
  private encrypt(plaintext: string): { value: string; iv: string; tag: string } {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipherGCM(this.algorithm, this.masterKey, iv);
    cipher.setAAD(Buffer.from('secrets-manager'));

    let encrypted = cipher.update(plaintext, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const tag = cipher.getAuthTag();

    return {
      value: encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex')
    };
  }

  /**
   * Decrypt a secret value
   */
  private decrypt(encrypted: { value: string; iv: string; tag: string }): string {
    const iv = Buffer.from(encrypted.iv, 'hex');
    const decipher = crypto.createDecipherGCM(this.algorithm, this.masterKey, iv);
    decipher.setAAD(Buffer.from('secrets-manager'));
    decipher.setAuthTag(Buffer.from(encrypted.tag, 'hex'));

    let decrypted = decipher.update(encrypted.value, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }

  /**
   * Load secrets from encrypted file
   */
  private loadSecrets(): void {
    try {
      if (!fs.existsSync(this.secretsFilePath)) {
        console.log('No secrets file found, starting with empty secrets');
        return;
      }

      const encryptedData = fs.readFileSync(this.secretsFilePath, 'utf8');
      const secretsData = JSON.parse(encryptedData);

      for (const [name, secret] of Object.entries(secretsData)) {
        this.secrets.set(name, secret as EncryptedSecret);
      }

      console.log(`Loaded ${this.secrets.size} secrets`);
    } catch (error) {
      console.error('Failed to load secrets:', error);
    }
  }

  /**
   * Save secrets to encrypted file
   */
  private saveSecrets(): void {
    try {
      const secretsData = Object.fromEntries(this.secrets);
      const encryptedData = JSON.stringify(secretsData, null, 2);
      
      fs.writeFileSync(this.secretsFilePath, encryptedData, { mode: 0o600 });
    } catch (error) {
      console.error('Failed to save secrets:', error);
      throw error;
    }
  }

  /**
   * Set a secret value
   */
  setSecret(name: string, value: string, metadata?: Partial<SecretMetadata>): void {
    if (!name || !value) {
      throw new Error('Secret name and value are required');
    }

    const encrypted = this.encrypt(value);
    const secret: EncryptedSecret = {
      ...encrypted,
      createdAt: new Date(),
      rotationInterval: metadata?.rotationInterval
    };

    this.secrets.set(name, secret);
    this.saveSecrets();

    console.log(`Secret '${name}' has been set`);
  }

  /**
   * Get a secret value
   */
  getSecret(name: string): string | null {
    const secret = this.secrets.get(name);
    if (!secret) {
      return null;
    }

    try {
      return this.decrypt(secret);
    } catch (error) {
      console.error(`Failed to decrypt secret '${name}':`, error);
      return null;
    }
  }

  /**
   * Get secret with fallback to environment variable
   */
  getSecretOrEnv(name: string, envVar?: string): string | null {
    // First try to get from secrets manager
    const secret = this.getSecret(name);
    if (secret) {
      return secret;
    }

    // Fallback to environment variable
    const envVarName = envVar || name.toUpperCase();
    const envValue = process.env[envVarName];
    
    if (envValue) {
      console.warn(`Using environment variable ${envVarName} for secret ${name}. Consider migrating to secrets manager.`);
      return envValue;
    }

    return null;
  }

  /**
   * Delete a secret
   */
  deleteSecret(name: string): boolean {
    const deleted = this.secrets.delete(name);
    if (deleted) {
      this.saveSecrets();
      console.log(`Secret '${name}' has been deleted`);
    }
    return deleted;
  }

  /**
   * List all secret names (not values)
   */
  listSecrets(): string[] {
    return Array.from(this.secrets.keys());
  }

  /**
   * Rotate a secret (generate new encryption)
   */
  rotateSecret(name: string): boolean {
    const secret = this.secrets.get(name);
    if (!secret) {
      return false;
    }

    try {
      // Decrypt current value
      const currentValue = this.decrypt(secret);
      
      // Re-encrypt with new IV
      const newEncrypted = this.encrypt(currentValue);
      
      // Update secret
      const updatedSecret: EncryptedSecret = {
        ...newEncrypted,
        createdAt: secret.createdAt,
        lastRotated: new Date(),
        rotationInterval: secret.rotationInterval
      };

      this.secrets.set(name, updatedSecret);
      this.saveSecrets();

      console.log(`Secret '${name}' has been rotated`);
      return true;
    } catch (error) {
      console.error(`Failed to rotate secret '${name}':`, error);
      return false;
    }
  }

  /**
   * Check which secrets need rotation
   */
  getSecretsNeedingRotation(): string[] {
    const needsRotation: string[] = [];
    const now = new Date();

    for (const [name, secret] of this.secrets) {
      if (!secret.rotationInterval) continue;

      const lastRotated = secret.lastRotated || secret.createdAt;
      const daysSinceRotation = (now.getTime() - lastRotated.getTime()) / (1000 * 60 * 60 * 24);

      if (daysSinceRotation >= secret.rotationInterval) {
        needsRotation.push(name);
      }
    }

    return needsRotation;
  }

  /**
   * Initialize default secrets from environment
   */
  initializeFromEnvironment(): void {
    const defaultSecrets = [
      { name: 'JWT_SECRET', envVar: 'JWT_SECRET', category: 'jwt' as const, required: true },
      { name: 'OPENAI_API_KEY', envVar: 'OPENAI_API_KEY', category: 'api_key' as const, required: true },
      { name: 'LEONARDO_API_KEY', envVar: 'LEONARDO_API_KEY', category: 'api_key' as const, required: false },
      { name: 'DATABASE_URL', envVar: 'DATABASE_URL', category: 'database' as const, required: true },
      { name: 'FIREBASE_PROJECT_ID', envVar: 'FIREBASE_PROJECT_ID', category: 'external_service' as const, required: false },
      { name: 'GCP_STORAGE_BUCKET', envVar: 'GCP_STORAGE_BUCKET', category: 'external_service' as const, required: false }
    ];

    let migrated = 0;
    for (const secretConfig of defaultSecrets) {
      const envValue = process.env[secretConfig.envVar];
      if (envValue && !this.secrets.has(secretConfig.name)) {
        this.setSecret(secretConfig.name, envValue, {
          category: secretConfig.category,
          required: secretConfig.required,
          rotationInterval: secretConfig.category === 'jwt' ? 30 : undefined // Rotate JWT secrets monthly
        });
        migrated++;
      }
    }

    if (migrated > 0) {
      console.log(`Migrated ${migrated} secrets from environment variables`);
    }
  }

  /**
   * Validate that all required secrets are present
   */
  validateRequiredSecrets(): { valid: boolean; missing: string[] } {
    const requiredSecrets = ['JWT_SECRET', 'OPENAI_API_KEY'];
    const missing: string[] = [];

    for (const secretName of requiredSecrets) {
      if (!this.getSecretOrEnv(secretName)) {
        missing.push(secretName);
      }
    }

    return {
      valid: missing.length === 0,
      missing
    };
  }
}

// Singleton instance
export const secretsManager = new SecretsManager();
