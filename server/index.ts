// Load environment variables first
import dotenv from 'dotenv';
dotenv.config();

import express, { type Request, Response, NextFunction } from "express";
import cors from "cors";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import { corsConfig, logCorsRequests } from "./config/corsConfig";
import {
  helmetConfig,
  securityHeaders,
  sanitizeRequest,
  securityLogger,
  validateRequestSize,
  generalRateLimit
} from "./config/security";
import { secretsManager } from "./services/secretsManager";
import { initializeDatabase } from "./database/connection";
import { migrationManager } from "./database/migrations";

const app = express();

// Initialize secrets manager (disabled for development)
try {
  secretsManager.initializeFromEnvironment();

  // Validate required secrets
  const secretsValidation = secretsManager.validateRequiredSecrets();
  if (!secretsValidation.valid) {
    console.warn('Missing required secrets:', secretsValidation.missing);
  }
} catch (error) {
  console.warn('Secrets manager initialization failed, using environment variables:', error);
}

// Security middleware
app.use(helmetConfig);
app.use(securityHeaders);
app.use(securityLogger);
app.use(validateRequestSize);
app.use(generalRateLimit);
app.use(cors(corsConfig));
app.use(logCorsRequests);
app.use(sanitizeRequest);

// Body parsing middleware
app.use(express.json({ limit: '10mb' })); // Reduced from 50mb for security
app.use(express.urlencoded({ extended: false, limit: '10mb' }));

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  // Initialize database (optional)
  if (process.env.NODE_ENV === 'production' || process.env.USE_DATABASE === 'true') {
    console.log('🗄️  Initializing database...');

    try {
      const dbInitialized = await initializeDatabase();
      if (dbInitialized) {
        // Run migrations
        await migrationManager.createInitialMigration();
        await migrationManager.runMigrations();

        // Validate schema
        const validation = await migrationManager.validateSchema();
        if (!validation.valid) {
          console.warn('⚠️  Database schema validation failed:', validation.errors);
        } else {
          console.log('✅ Database initialized successfully');
        }
      } else {
        console.warn('⚠️  Database initialization failed, using memory storage');
      }
    } catch (error) {
      console.warn('⚠️  Database setup failed, using memory storage:', error);
    }
  } else {
    console.log('📝 Using memory storage for development');
  }

  const server = await registerRoutes(app);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // Serve the app on the configured port
  const port = parseInt(process.env.PORT || '5000');
  const host = process.env.NODE_ENV === 'development' ? 'localhost' : '0.0.0.0';

  server.listen(port, host, () => {
    log(`🚀 Server running on http://${host}:${port}`);
    log(`📚 API docs available at http://${host}:${port}/api/docs`);
  });
})();
