import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import { secretsManager } from '../services/secretsManager';
import * as schema from '../../shared/schema';

// Database connection configuration
interface DatabaseConfig {
  url: string;
  maxConnections: number;
  connectionTimeout: number;
  idleTimeout: number;
  ssl: boolean;
}

class DatabaseConnection {
  private db: ReturnType<typeof drizzle> | null = null;
  private config: DatabaseConfig;
  private isConnected = false;
  private connectionAttempts = 0;
  private maxRetries = 3;

  constructor() {
    this.config = this.loadConfig();
  }

  private loadConfig(): DatabaseConfig {
    const databaseUrl = secretsManager.getSecretOrEnv('DATABASE_URL') || process.env.DATABASE_URL;

    if (!databaseUrl) {
      console.warn('DATABASE_URL not configured, database features will be disabled');
      // Return a dummy config for development
      return {
        url: '',
        maxConnections: 0,
        connectionTimeout: 0,
        idleTimeout: 0,
        ssl: false
      };
    }

    return {
      url: databaseUrl,
      maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '10'),
      connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '30000'),
      idleTimeout: parseInt(process.env.DB_IDLE_TIMEOUT || '600000'),
      ssl: process.env.NODE_ENV === 'production'
    };
  }

  /**
   * Initialize database connection
   */
  async connect(): Promise<void> {
    if (this.isConnected && this.db) {
      return;
    }

    // Skip connection if no URL configured
    if (!this.config.url) {
      console.log('⚠️  No database URL configured, skipping database connection');
      return;
    }

    try {
      console.log('🔌 Connecting to database...');

      // Create Neon HTTP client
      const sql = neon(this.config.url);

      // Initialize Drizzle with schema
      this.db = drizzle(sql, { schema });

      // Test connection
      await this.testConnection();

      this.isConnected = true;
      this.connectionAttempts = 0;

      console.log('✅ Database connected successfully');
    } catch (error) {
      this.connectionAttempts++;
      console.error(`❌ Database connection failed (attempt ${this.connectionAttempts}):`, error);

      if (this.connectionAttempts < this.maxRetries) {
        console.log(`🔄 Retrying connection in 5 seconds...`);
        await this.delay(5000);
        return this.connect();
      }

      throw new Error(`Failed to connect to database after ${this.maxRetries} attempts`);
    }
  }

  /**
   * Test database connection
   */
  private async testConnection(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      // Simple query to test connection
      await this.db.execute('SELECT 1 as test');
    } catch (error) {
      throw new Error(`Database connection test failed: ${error}`);
    }
  }

  /**
   * Get database instance
   */
  getDatabase() {
    if (!this.isConnected || !this.db) {
      throw new Error('Database not connected. Call connect() first.');
    }
    return this.db;
  }

  /**
   * Check if database is connected
   */
  isHealthy(): boolean {
    return this.isConnected && this.db !== null;
  }

  /**
   * Get connection status
   */
  getStatus() {
    return {
      connected: this.isConnected,
      attempts: this.connectionAttempts,
      config: {
        maxConnections: this.config.maxConnections,
        connectionTimeout: this.config.connectionTimeout,
        idleTimeout: this.config.idleTimeout,
        ssl: this.config.ssl
      }
    };
  }

  /**
   * Gracefully close database connection
   */
  async disconnect(): Promise<void> {
    if (this.db) {
      try {
        // Neon HTTP doesn't require explicit closing
        this.db = null;
        this.isConnected = false;
        console.log('🔌 Database disconnected');
      } catch (error) {
        console.error('Error disconnecting from database:', error);
      }
    }
  }

  /**
   * Execute a transaction
   */
  async transaction<T>(callback: (tx: any) => Promise<T>): Promise<T> {
    const db = this.getDatabase();
    return await db.transaction(callback);
  }

  /**
   * Health check for monitoring
   */
  async healthCheck(): Promise<{ status: string; latency: number; error?: string }> {
    const startTime = Date.now();
    
    try {
      if (!this.isConnected || !this.db) {
        return {
          status: 'disconnected',
          latency: 0,
          error: 'Database not connected'
        };
      }

      await this.db.execute('SELECT 1 as health_check');
      const latency = Date.now() - startTime;
      
      return {
        status: 'healthy',
        latency
      };
    } catch (error: any) {
      const latency = Date.now() - startTime;
      return {
        status: 'unhealthy',
        latency,
        error: error.message
      };
    }
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get database statistics
   */
  async getStats() {
    try {
      const db = this.getDatabase();
      
      // Get table counts
      const userCount = await db.select().from(schema.users);
      const agentCount = await db.select().from(schema.agents);
      const chatLogCount = await db.select().from(schema.chatLogs);
      const knowledgeCount = await db.select().from(schema.knowledgeBase);
      
      return {
        tables: {
          users: userCount.length,
          agents: agentCount.length,
          chatLogs: chatLogCount.length,
          knowledgeBase: knowledgeCount.length
        },
        connection: this.getStatus()
      };
    } catch (error: any) {
      return {
        error: error.message,
        connection: this.getStatus()
      };
    }
  }
}

// Singleton instance
export const dbConnection = new DatabaseConnection();

// Helper function to get database instance
export const getDb = () => dbConnection.getDatabase();

// Initialize connection on module load
export const initializeDatabase = async () => {
  try {
    await dbConnection.connect();
    return true;
  } catch (error) {
    console.error('Failed to initialize database:', error);
    return false;
  }
};

// Graceful shutdown handler
process.on('SIGINT', async () => {
  console.log('🛑 Shutting down database connection...');
  await dbConnection.disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('🛑 Shutting down database connection...');
  await dbConnection.disconnect();
  process.exit(0);
});
