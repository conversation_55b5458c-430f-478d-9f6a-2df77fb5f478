import { migrate } from 'drizzle-orm/neon-http/migrator';
import { dbConnection } from './connection';
import fs from 'fs';
import path from 'path';

interface MigrationInfo {
  id: string;
  name: string;
  timestamp: Date;
  applied: boolean;
  sql?: string;
}

class MigrationManager {
  private migrationsPath: string;

  constructor() {
    this.migrationsPath = path.join(process.cwd(), 'migrations');
    this.ensureMigrationsDirectory();
  }

  private ensureMigrationsDirectory(): void {
    if (!fs.existsSync(this.migrationsPath)) {
      fs.mkdirSync(this.migrationsPath, { recursive: true });
      console.log('📁 Created migrations directory');
    }
  }

  /**
   * Run all pending migrations
   */
  async runMigrations(): Promise<void> {
    try {
      console.log('🚀 Running database migrations...');
      
      const db = dbConnection.getDatabase();
      
      await migrate(db, {
        migrationsFolder: this.migrationsPath,
      });
      
      console.log('✅ Migrations completed successfully');
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Create initial migration with default data
   */
  async createInitialMigration(): Promise<void> {
    const migrationName = '0001_initial_schema';
    const migrationFile = path.join(this.migrationsPath, `${migrationName}.sql`);
    
    if (fs.existsSync(migrationFile)) {
      console.log('📋 Initial migration already exists');
      return;
    }

    const initialSQL = `
-- Initial schema migration
-- Created: ${new Date().toISOString()}

-- Create users table
CREATE TABLE IF NOT EXISTS "users" (
  "id" SERIAL PRIMARY KEY,
  "username" TEXT NOT NULL UNIQUE,
  "password" TEXT NOT NULL,
  "email" TEXT NOT NULL UNIQUE,
  "first_name" TEXT NOT NULL,
  "last_name" TEXT NOT NULL,
  "role" TEXT NOT NULL DEFAULT 'client' CHECK ("role" IN ('admin', 'artist', 'client')),
  "status" TEXT NOT NULL DEFAULT 'active' CHECK ("status" IN ('active', 'inactive', 'pending')),
  "created_at" TIMESTAMP DEFAULT NOW()
);

-- Create agents table
CREATE TABLE IF NOT EXISTS "agents" (
  "id" SERIAL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "system_prompt" TEXT NOT NULL,
  "is_active" BOOLEAN DEFAULT true,
  "created_at" TIMESTAMP DEFAULT NOW(),
  "updated_at" TIMESTAMP DEFAULT NOW()
);

-- Create chat_logs table
CREATE TABLE IF NOT EXISTS "chat_logs" (
  "id" SERIAL PRIMARY KEY,
  "user_id" INTEGER REFERENCES "users"("id"),
  "agent_id" INTEGER REFERENCES "agents"("id"),
  "conversation" TEXT NOT NULL,
  "status" TEXT NOT NULL DEFAULT 'completed' CHECK ("status" IN ('active', 'completed', 'pending', 'failed')),
  "created_at" TIMESTAMP DEFAULT NOW()
);

-- Create knowledge_base table
CREATE TABLE IF NOT EXISTS "knowledge_base" (
  "id" SERIAL PRIMARY KEY,
  "title" TEXT NOT NULL,
  "content" TEXT NOT NULL,
  "category" TEXT NOT NULL,
  "created_by_id" INTEGER REFERENCES "users"("id"),
  "created_at" TIMESTAMP DEFAULT NOW(),
  "updated_at" TIMESTAMP DEFAULT NOW()
);

-- Create settings table
CREATE TABLE IF NOT EXISTS "settings" (
  "id" SERIAL PRIMARY KEY,
  "key" TEXT NOT NULL UNIQUE,
  "value" TEXT NOT NULL,
  "updated_at" TIMESTAMP DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "idx_users_username" ON "users"("username");
CREATE INDEX IF NOT EXISTS "idx_users_email" ON "users"("email");
CREATE INDEX IF NOT EXISTS "idx_users_role" ON "users"("role");
CREATE INDEX IF NOT EXISTS "idx_users_status" ON "users"("status");

CREATE INDEX IF NOT EXISTS "idx_agents_active" ON "agents"("is_active");
CREATE INDEX IF NOT EXISTS "idx_agents_name" ON "agents"("name");

CREATE INDEX IF NOT EXISTS "idx_chat_logs_user" ON "chat_logs"("user_id");
CREATE INDEX IF NOT EXISTS "idx_chat_logs_agent" ON "chat_logs"("agent_id");
CREATE INDEX IF NOT EXISTS "idx_chat_logs_status" ON "chat_logs"("status");
CREATE INDEX IF NOT EXISTS "idx_chat_logs_created" ON "chat_logs"("created_at");

CREATE INDEX IF NOT EXISTS "idx_knowledge_category" ON "knowledge_base"("category");
CREATE INDEX IF NOT EXISTS "idx_knowledge_created_by" ON "knowledge_base"("created_by_id");
CREATE INDEX IF NOT EXISTS "idx_knowledge_title" ON "knowledge_base"("title");

CREATE INDEX IF NOT EXISTS "idx_settings_key" ON "settings"("key");

-- Insert default admin user (password: admin123 - hashed with bcrypt)
INSERT INTO "users" ("username", "password", "email", "first_name", "last_name", "role", "status")
VALUES (
  'admin',
  '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJL9.KeF.',
  '<EMAIL>',
  'Admin',
  'User',
  'admin',
  'active'
) ON CONFLICT (username) DO NOTHING;

-- Insert default artist user (password: artist123 - hashed with bcrypt)
INSERT INTO "users" ("username", "password", "email", "first_name", "last_name", "role", "status")
VALUES (
  'artist',
  '$2b$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
  '<EMAIL>',
  'Artist',
  'User',
  'artist',
  'active'
) ON CONFLICT (username) DO NOTHING;

-- Insert default agents
INSERT INTO "agents" ("name", "description", "system_prompt", "is_active")
VALUES 
  (
    'Designer Agent',
    'Specialized in tattoo design creation and artistic guidance',
    'You are a specialized tattoo design assistant. Help users create detailed design concepts, understand artistic composition, and translate ideas into visual descriptions for AI generation.',
    true
  ),
  (
    'Manager Agent',
    'General tattoo consultation and coordination',
    'You are a tattoo consultation manager. Help coordinate between different specialists, provide general guidance, and ensure users get comprehensive assistance.',
    true
  ),
  (
    'Aftercare Agent',
    'Expert in tattoo healing and maintenance',
    'You are a tattoo aftercare specialist. Provide expert guidance on proper healing procedures, care products, timeline expectations, and when to seek professional help.',
    true
  ),
  (
    'Booking Agent',
    'Assists with appointment scheduling and preparation',
    'You are a tattoo booking specialist. Help users understand the consultation process, prepare for appointments, connect with appropriate artists, and plan their tattoo journey.',
    true
  )
ON CONFLICT DO NOTHING;

-- Insert default knowledge base entries
INSERT INTO "knowledge_base" ("title", "content", "category", "created_by_id")
VALUES 
  (
    'Tattoo Styles Overview',
    'Comprehensive guide to different tattoo styles including traditional, neo-traditional, realism, geometric, watercolor, and minimalist approaches.',
    'tattoo_styles',
    1
  ),
  (
    'Aftercare Best Practices',
    'Complete aftercare instructions including cleaning procedures, healing timeline, products to use and avoid, and warning signs to watch for.',
    'aftercare',
    1
  ),
  (
    'Safety Guidelines',
    'Important safety information including sterile procedures, equipment standards, health considerations, and regulatory compliance.',
    'safety',
    1
  )
ON CONFLICT DO NOTHING;

-- Insert default settings
INSERT INTO "settings" ("key", "value")
VALUES 
  ('app_version', '1.0.0'),
  ('maintenance_mode', 'false'),
  ('registration_enabled', 'true'),
  ('max_file_size', '10485760'),
  ('session_timeout', '86400')
ON CONFLICT (key) DO NOTHING;
`;

    fs.writeFileSync(migrationFile, initialSQL);
    console.log(`📝 Created initial migration: ${migrationFile}`);
  }

  /**
   * Get migration status
   */
  async getMigrationStatus(): Promise<MigrationInfo[]> {
    const migrations: MigrationInfo[] = [];
    
    try {
      const files = fs.readdirSync(this.migrationsPath)
        .filter(file => file.endsWith('.sql'))
        .sort();
      
      for (const file of files) {
        const filePath = path.join(this.migrationsPath, file);
        const stats = fs.statSync(filePath);
        const sql = fs.readFileSync(filePath, 'utf8');
        
        migrations.push({
          id: file.replace('.sql', ''),
          name: file,
          timestamp: stats.mtime,
          applied: true, // Simplified - in real implementation, check migration table
          sql
        });
      }
    } catch (error) {
      console.error('Error reading migrations:', error);
    }
    
    return migrations;
  }

  /**
   * Rollback last migration (simplified implementation)
   */
  async rollbackLastMigration(): Promise<void> {
    console.warn('⚠️  Migration rollback not implemented in this version');
    console.warn('   Manual database restoration may be required');
  }

  /**
   * Validate database schema
   */
  async validateSchema(): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];
    
    try {
      const db = dbConnection.getDatabase();
      
      // Check if required tables exist
      const requiredTables = ['users', 'agents', 'chat_logs', 'knowledge_base', 'settings'];
      
      for (const table of requiredTables) {
        try {
          await db.execute(`SELECT 1 FROM ${table} LIMIT 1`);
        } catch (error) {
          errors.push(`Table '${table}' does not exist or is not accessible`);
        }
      }
      
      // Check if admin user exists
      try {
        const adminCheck = await db.execute(`SELECT id FROM users WHERE username = 'admin' LIMIT 1`);
        if (!adminCheck || adminCheck.length === 0) {
          errors.push('Default admin user not found');
        }
      } catch (error) {
        errors.push('Cannot verify admin user existence');
      }
      
    } catch (error: any) {
      errors.push(`Schema validation failed: ${error.message}`);
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
}

export const migrationManager = new MigrationManager();
