import { 
  users, 
  User, 
  InsertUser, 
  agents, 
  Agent, 
  InsertAgent,
  chatLogs,
  ChatLog,
  InsertChatLog,
  knowledgeBase,
  KnowledgeBase,
  InsertKnowledgeBase,
  settings,
  Setting,
  InsertSetting
} from "@shared/schema";
import { getDb } from './connection';
import { eq, desc, and, or, like, count, asc } from 'drizzle-orm';
import type { IStorage } from '../storage';

export class DatabaseStorage implements IStorage {
  
  // User management
  async getUser(id: number): Promise<User | undefined> {
    try {
      const db = getDb();
      const result = await db.select().from(users).where(eq(users.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting user:', error);
      return undefined;
    }
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    try {
      const db = getDb();
      const result = await db.select().from(users).where(eq(users.username, username)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting user by username:', error);
      return undefined;
    }
  }

  async getUsers(page: number = 1, limit: number = 10): Promise<{ users: User[], total: number }> {
    try {
      const db = getDb();
      const offset = (page - 1) * limit;
      
      // Get total count
      const totalResult = await db.select({ count: count() }).from(users);
      const total = totalResult[0]?.count || 0;
      
      // Get paginated users
      const usersList = await db
        .select()
        .from(users)
        .orderBy(desc(users.createdAt))
        .limit(limit)
        .offset(offset);
      
      return { users: usersList, total };
    } catch (error) {
      console.error('Error getting users:', error);
      return { users: [], total: 0 };
    }
  }

  async createUser(user: InsertUser): Promise<User> {
    try {
      const db = getDb();
      const result = await db.insert(users).values(user).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating user:', error);
      throw new Error('Failed to create user');
    }
  }

  async updateUser(id: number, user: Partial<InsertUser>): Promise<User | undefined> {
    try {
      const db = getDb();
      const result = await db
        .update(users)
        .set(user)
        .where(eq(users.id, id))
        .returning();
      return result[0];
    } catch (error) {
      console.error('Error updating user:', error);
      return undefined;
    }
  }

  async deleteUser(id: number): Promise<boolean> {
    try {
      const db = getDb();
      const result = await db.delete(users).where(eq(users.id, id));
      return result.rowCount > 0;
    } catch (error) {
      console.error('Error deleting user:', error);
      return false;
    }
  }

  // Agent management
  async getAgent(id: number): Promise<Agent | undefined> {
    try {
      const db = getDb();
      const result = await db.select().from(agents).where(eq(agents.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting agent:', error);
      return undefined;
    }
  }

  async getAgents(page: number = 1, limit: number = 10): Promise<{ agents: Agent[], total: number }> {
    try {
      const db = getDb();
      const offset = (page - 1) * limit;
      
      // Get total count
      const totalResult = await db.select({ count: count() }).from(agents);
      const total = totalResult[0]?.count || 0;
      
      // Get paginated agents
      const agentsList = await db
        .select()
        .from(agents)
        .orderBy(desc(agents.createdAt))
        .limit(limit)
        .offset(offset);
      
      return { agents: agentsList, total };
    } catch (error) {
      console.error('Error getting agents:', error);
      return { agents: [], total: 0 };
    }
  }

  async createAgent(agent: InsertAgent): Promise<Agent> {
    try {
      const db = getDb();
      const result = await db.insert(agents).values(agent).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating agent:', error);
      throw new Error('Failed to create agent');
    }
  }

  async updateAgent(id: number, agent: Partial<InsertAgent>): Promise<Agent | undefined> {
    try {
      const db = getDb();
      const result = await db
        .update(agents)
        .set({ ...agent, updatedAt: new Date() })
        .where(eq(agents.id, id))
        .returning();
      return result[0];
    } catch (error) {
      console.error('Error updating agent:', error);
      return undefined;
    }
  }

  async deleteAgent(id: number): Promise<boolean> {
    try {
      const db = getDb();
      const result = await db.delete(agents).where(eq(agents.id, id));
      return result.rowCount > 0;
    } catch (error) {
      console.error('Error deleting agent:', error);
      return false;
    }
  }

  // Chat log management
  async getChatLog(id: number): Promise<ChatLog | undefined> {
    try {
      const db = getDb();
      const result = await db.select().from(chatLogs).where(eq(chatLogs.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting chat log:', error);
      return undefined;
    }
  }

  async getChatLogs(
    page: number = 1, 
    limit: number = 10, 
    filters: { userId?: number; agentId?: number; status?: string } = {}
  ): Promise<{ chatLogs: ChatLog[], total: number }> {
    try {
      const db = getDb();
      const offset = (page - 1) * limit;
      
      // Build where conditions
      const conditions = [];
      if (filters.userId) conditions.push(eq(chatLogs.userId, filters.userId));
      if (filters.agentId) conditions.push(eq(chatLogs.agentId, filters.agentId));
      if (filters.status) conditions.push(eq(chatLogs.status, filters.status as any));
      
      const whereClause = conditions.length > 0 ? and(...conditions) : undefined;
      
      // Get total count
      const totalResult = await db
        .select({ count: count() })
        .from(chatLogs)
        .where(whereClause);
      const total = totalResult[0]?.count || 0;
      
      // Get paginated chat logs
      const logsList = await db
        .select()
        .from(chatLogs)
        .where(whereClause)
        .orderBy(desc(chatLogs.createdAt))
        .limit(limit)
        .offset(offset);
      
      return { chatLogs: logsList, total };
    } catch (error) {
      console.error('Error getting chat logs:', error);
      return { chatLogs: [], total: 0 };
    }
  }

  async createChatLog(chatLog: InsertChatLog): Promise<ChatLog> {
    try {
      const db = getDb();
      const result = await db.insert(chatLogs).values(chatLog).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating chat log:', error);
      throw new Error('Failed to create chat log');
    }
  }

  async updateChatLog(id: number, chatLog: Partial<InsertChatLog>): Promise<ChatLog | undefined> {
    try {
      const db = getDb();
      const result = await db
        .update(chatLogs)
        .set(chatLog)
        .where(eq(chatLogs.id, id))
        .returning();
      return result[0];
    } catch (error) {
      console.error('Error updating chat log:', error);
      return undefined;
    }
  }

  async deleteChatLog(id: number): Promise<boolean> {
    try {
      const db = getDb();
      const result = await db.delete(chatLogs).where(eq(chatLogs.id, id));
      return result.rowCount > 0;
    } catch (error) {
      console.error('Error deleting chat log:', error);
      return false;
    }
  }

  // Knowledge base management
  async getKnowledgeBaseItem(id: number): Promise<KnowledgeBase | undefined> {
    try {
      const db = getDb();
      const result = await db.select().from(knowledgeBase).where(eq(knowledgeBase.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting knowledge base item:', error);
      return undefined;
    }
  }

  async getKnowledgeBase(
    page: number = 1, 
    limit: number = 10, 
    category?: string
  ): Promise<{ knowledgeBase: KnowledgeBase[], total: number }> {
    try {
      const db = getDb();
      const offset = (page - 1) * limit;
      
      const whereClause = category ? eq(knowledgeBase.category, category) : undefined;
      
      // Get total count
      const totalResult = await db
        .select({ count: count() })
        .from(knowledgeBase)
        .where(whereClause);
      const total = totalResult[0]?.count || 0;
      
      // Get paginated knowledge base items
      const kbList = await db
        .select()
        .from(knowledgeBase)
        .where(whereClause)
        .orderBy(desc(knowledgeBase.updatedAt))
        .limit(limit)
        .offset(offset);
      
      return { knowledgeBase: kbList, total };
    } catch (error) {
      console.error('Error getting knowledge base:', error);
      return { knowledgeBase: [], total: 0 };
    }
  }

  async createKnowledgeBaseItem(item: InsertKnowledgeBase): Promise<KnowledgeBase> {
    try {
      const db = getDb();
      const result = await db.insert(knowledgeBase).values(item).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating knowledge base item:', error);
      throw new Error('Failed to create knowledge base item');
    }
  }

  async updateKnowledgeBaseItem(id: number, item: Partial<InsertKnowledgeBase>): Promise<KnowledgeBase | undefined> {
    try {
      const db = getDb();
      const result = await db
        .update(knowledgeBase)
        .set({ ...item, updatedAt: new Date() })
        .where(eq(knowledgeBase.id, id))
        .returning();
      return result[0];
    } catch (error) {
      console.error('Error updating knowledge base item:', error);
      return undefined;
    }
  }

  async deleteKnowledgeBaseItem(id: number): Promise<boolean> {
    try {
      const db = getDb();
      const result = await db.delete(knowledgeBase).where(eq(knowledgeBase.id, id));
      return result.rowCount > 0;
    } catch (error) {
      console.error('Error deleting knowledge base item:', error);
      return false;
    }
  }

  // Settings management
  async getSetting(key: string): Promise<Setting | undefined> {
    try {
      const db = getDb();
      const result = await db.select().from(settings).where(eq(settings.key, key)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting setting:', error);
      return undefined;
    }
  }

  async updateSetting(key: string, value: string): Promise<Setting | undefined> {
    try {
      const db = getDb();
      
      // Try to update existing setting
      const updateResult = await db
        .update(settings)
        .set({ value, updatedAt: new Date() })
        .where(eq(settings.key, key))
        .returning();
      
      if (updateResult.length > 0) {
        return updateResult[0];
      }
      
      // If no existing setting, create new one
      const insertResult = await db
        .insert(settings)
        .values({ key, value, updatedAt: new Date() })
        .returning();
      
      return insertResult[0];
    } catch (error) {
      console.error('Error updating setting:', error);
      return undefined;
    }
  }
}
