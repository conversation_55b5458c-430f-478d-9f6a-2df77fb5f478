import { Request, Response, NextFunction } from 'express';
import multer from 'multer';
import path from 'path';
import crypto from 'crypto';
import fs from 'fs';

// File type configurations
const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/webp',
  'image/gif'
];

const ALLOWED_AUDIO_TYPES = [
  'audio/webm',
  'audio/wav',
  'audio/mp3',
  'audio/mpeg',
  'audio/ogg'
];

const ALLOWED_DOCUMENT_TYPES = [
  'application/pdf',
  'text/plain',
  'application/json'
];

// File size limits (in bytes)
const MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB
const MAX_AUDIO_SIZE = 50 * 1024 * 1024; // 50MB
const MAX_DOCUMENT_SIZE = 5 * 1024 * 1024; // 5MB

// Magic number signatures for file type validation
const FILE_SIGNATURES: { [key: string]: <PERSON>uffer[] } = {
  'image/jpeg': [
    Buffer.from([0xFF, 0xD8, 0xFF]),
  ],
  'image/png': [
    Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]),
  ],
  'image/gif': [
    Buffer.from([0x47, 0x49, 0x46, 0x38, 0x37, 0x61]),
    Buffer.from([0x47, 0x49, 0x46, 0x38, 0x39, 0x61]),
  ],
  'image/webp': [
    Buffer.from([0x52, 0x49, 0x46, 0x46]),
  ],
  'application/pdf': [
    Buffer.from([0x25, 0x50, 0x44, 0x46]),
  ]
};

/**
 * Validate file type by checking magic numbers
 */
const validateFileSignature = (buffer: Buffer, mimeType: string): boolean => {
  const signatures = FILE_SIGNATURES[mimeType];
  if (!signatures) return true; // Skip validation if no signature defined
  
  return signatures.some(signature => {
    return buffer.subarray(0, signature.length).equals(signature);
  });
};

/**
 * Generate secure filename
 */
const generateSecureFilename = (originalName: string): string => {
  const ext = path.extname(originalName).toLowerCase();
  const timestamp = Date.now();
  const randomBytes = crypto.randomBytes(8).toString('hex');
  return `${timestamp}_${randomBytes}${ext}`;
};

/**
 * Multer configuration for secure file uploads
 */
const createMulterConfig = (fileType: 'image' | 'audio' | 'document') => {
  let allowedTypes: string[];
  let maxSize: number;
  
  switch (fileType) {
    case 'image':
      allowedTypes = ALLOWED_IMAGE_TYPES;
      maxSize = MAX_IMAGE_SIZE;
      break;
    case 'audio':
      allowedTypes = ALLOWED_AUDIO_TYPES;
      maxSize = MAX_AUDIO_SIZE;
      break;
    case 'document':
      allowedTypes = ALLOWED_DOCUMENT_TYPES;
      maxSize = MAX_DOCUMENT_SIZE;
      break;
    default:
      throw new Error('Invalid file type configuration');
  }
  
  return multer({
    storage: multer.memoryStorage(),
    limits: {
      fileSize: maxSize,
      files: 1, // Only allow single file uploads
      fields: 10, // Limit form fields
      fieldNameSize: 100, // Limit field name size
      fieldSize: 1024 * 1024 // 1MB field size limit
    },
    fileFilter: (req, file, cb) => {
      // Check MIME type
      if (!allowedTypes.includes(file.mimetype)) {
        return cb(new Error(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`));
      }
      
      // Sanitize filename
      const sanitizedName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '_');
      file.originalname = sanitizedName;
      
      cb(null, true);
    }
  });
};

/**
 * Comprehensive file validation middleware
 */
export const validateFile = (fileType: 'image' | 'audio' | 'document') => {
  const upload = createMulterConfig(fileType);
  
  return [
    upload.single('file'),
    (req: Request, res: Response, next: NextFunction) => {
      try {
        if (!req.file) {
          return res.status(400).json({
            message: 'No file uploaded',
            code: 'NO_FILE'
          });
        }
        
        const file = req.file;
        
        // Validate file signature against MIME type
        if (!validateFileSignature(file.buffer, file.mimetype)) {
          return res.status(400).json({
            message: 'File content does not match declared type',
            code: 'INVALID_FILE_SIGNATURE'
          });
        }
        
        // Additional security checks
        if (file.originalname.includes('..') || file.originalname.includes('/')) {
          return res.status(400).json({
            message: 'Invalid filename',
            code: 'INVALID_FILENAME'
          });
        }
        
        // Generate secure filename
        const secureFilename = generateSecureFilename(file.originalname);
        
        // Add security metadata to request
        req.fileMetadata = {
          originalName: file.originalname,
          secureFilename,
          mimeType: file.mimetype,
          size: file.size,
          uploadedAt: new Date(),
          hash: crypto.createHash('sha256').update(file.buffer).digest('hex')
        };
        
        next();
      } catch (error: any) {
        console.error('File validation error:', error);
        return res.status(400).json({
          message: error.message || 'File validation failed',
          code: 'VALIDATION_ERROR'
        });
      }
    }
  ];
};

/**
 * Middleware for validating base64 encoded files
 */
export const validateBase64File = (fileType: 'image' | 'audio', maxSize?: number) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const { fileData, fileName } = req.body;
      
      if (!fileData) {
        return res.status(400).json({
          message: 'No file data provided',
          code: 'NO_FILE_DATA'
        });
      }
      
      // Parse base64 data
      const matches = fileData.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);
      if (!matches || matches.length !== 3) {
        return res.status(400).json({
          message: 'Invalid base64 file format',
          code: 'INVALID_BASE64'
        });
      }
      
      const mimeType = matches[1];
      const base64Data = matches[2];
      
      // Validate MIME type
      const allowedTypes = fileType === 'image' ? ALLOWED_IMAGE_TYPES : ALLOWED_AUDIO_TYPES;
      if (!allowedTypes.includes(mimeType)) {
        return res.status(400).json({
          message: `Invalid file type. Allowed types: ${allowedTypes.join(', ')}`,
          code: 'INVALID_MIME_TYPE'
        });
      }
      
      // Convert to buffer
      const buffer = Buffer.from(base64Data, 'base64');
      
      // Check file size
      const sizeLimit = maxSize || (fileType === 'image' ? MAX_IMAGE_SIZE : MAX_AUDIO_SIZE);
      if (buffer.length > sizeLimit) {
        return res.status(400).json({
          message: `File too large. Maximum size: ${Math.round(sizeLimit / 1024 / 1024)}MB`,
          code: 'FILE_TOO_LARGE'
        });
      }
      
      // Validate file signature
      if (!validateFileSignature(buffer, mimeType)) {
        return res.status(400).json({
          message: 'File content does not match declared type',
          code: 'INVALID_FILE_SIGNATURE'
        });
      }
      
      // Generate secure filename
      const ext = mimeType.split('/')[1];
      const secureFilename = generateSecureFilename(`upload.${ext}`);
      
      // Add validated data to request
      req.validatedFile = {
        buffer,
        mimeType,
        size: buffer.length,
        originalName: fileName || 'upload',
        secureFilename,
        hash: crypto.createHash('sha256').update(buffer).digest('hex')
      };
      
      next();
    } catch (error: any) {
      console.error('Base64 file validation error:', error);
      return res.status(400).json({
        message: 'File validation failed',
        code: 'VALIDATION_ERROR'
      });
    }
  };
};

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      fileMetadata?: {
        originalName: string;
        secureFilename: string;
        mimeType: string;
        size: number;
        uploadedAt: Date;
        hash: string;
      };
      validatedFile?: {
        buffer: Buffer;
        mimeType: string;
        size: number;
        originalName: string;
        secureFilename: string;
        hash: string;
      };
    }
  }
}
