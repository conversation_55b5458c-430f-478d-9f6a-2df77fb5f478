import type { Request, Response, NextFunction } from 'express';
import { authService } from '../services/authService';
import type { User } from '@shared/schema';

// Extend Express Request type to include user
declare global {
  namespace Express {
    interface Request {
      user?: User;
    }
  }
}

/**
 * Middleware to authenticate requests using JWT tokens
 */
export const authenticateToken = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const user = await authService.validateTokenAndGetUser(req.headers.authorization);
    
    if (!user) {
      return res.status(401).json({ 
        message: 'Authentication required',
        code: 'UNAUTHORIZED'
      });
    }
    
    req.user = user;
    next();
  } catch (error) {
    console.error('Authentication middleware error:', error);
    return res.status(401).json({ 
      message: 'Authentication failed',
      code: 'AUTH_ERROR'
    });
  }
};

/**
 * Middleware to check if user has required role
 */
export const requireRole = (roles: string | string[]) => {
  const allowedRoles = Array.isArray(roles) ? roles : [roles];
  
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ 
        message: 'Authentication required',
        code: 'UNAUTHORIZED'
      });
    }
    
    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({ 
        message: 'Insufficient permissions',
        code: 'FORBIDDEN',
        required: allowedRoles,
        current: req.user.role
      });
    }
    
    next();
  };
};

/**
 * Middleware to check if user is admin
 */
export const requireAdmin = requireRole('admin');

/**
 * Middleware to check if user is admin or artist
 */
export const requireAdminOrArtist = requireRole(['admin', 'artist']);

/**
 * Optional authentication middleware - sets user if token is valid but doesn't require it
 */
export const optionalAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const user = await authService.validateTokenAndGetUser(req.headers.authorization);
    if (user) {
      req.user = user;
    }
    next();
  } catch (error) {
    // Continue without authentication
    next();
  }
};

/**
 * Middleware to check if user can access their own resources or is admin
 */
export const requireOwnershipOrAdmin = (userIdParam: string = 'id') => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ 
        message: 'Authentication required',
        code: 'UNAUTHORIZED'
      });
    }
    
    const resourceUserId = parseInt(req.params[userIdParam]);
    const isOwner = req.user.id === resourceUserId;
    const isAdmin = req.user.role === 'admin';
    
    if (!isOwner && !isAdmin) {
      return res.status(403).json({ 
        message: 'Access denied. You can only access your own resources.',
        code: 'FORBIDDEN'
      });
    }
    
    next();
  };
};

/**
 * Rate limiting middleware for authentication endpoints
 */
const authAttempts = new Map<string, { count: number; lastAttempt: number }>();
const MAX_AUTH_ATTEMPTS = 5;
const AUTH_WINDOW_MS = 15 * 60 * 1000; // 15 minutes

export const rateLimitAuth = (req: Request, res: Response, next: NextFunction) => {
  const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
  const now = Date.now();
  
  const attempts = authAttempts.get(clientIP);
  
  if (attempts) {
    // Reset if window has passed
    if (now - attempts.lastAttempt > AUTH_WINDOW_MS) {
      authAttempts.delete(clientIP);
    } else if (attempts.count >= MAX_AUTH_ATTEMPTS) {
      return res.status(429).json({
        message: 'Too many authentication attempts. Please try again later.',
        code: 'RATE_LIMITED',
        retryAfter: Math.ceil((AUTH_WINDOW_MS - (now - attempts.lastAttempt)) / 1000)
      });
    }
  }
  
  // Track this attempt
  const currentAttempts = authAttempts.get(clientIP) || { count: 0, lastAttempt: now };
  currentAttempts.count += 1;
  currentAttempts.lastAttempt = now;
  authAttempts.set(clientIP, currentAttempts);
  
  next();
};

/**
 * Middleware to log authentication events
 */
export const logAuthEvent = (event: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.headers['user-agent'] || 'unknown';
    const username = req.body?.username || req.user?.username || 'unknown';
    
    console.log(`[AUTH_EVENT] ${event} - User: ${username}, IP: ${clientIP}, UA: ${userAgent}`);
    
    next();
  };
};
