import type { CorsOptions } from 'cors';

// Environment-based CORS configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Allowed origins based on environment
const getAllowedOrigins = (): string[] => {
  const origins: string[] = [];
  
  // Development origins
  if (isDevelopment) {
    origins.push(
      'http://localhost:3000',
      'http://localhost:5000',
      'http://localhost:5173', // Vite dev server
      'http://127.0.0.1:3000',
      'http://127.0.0.1:5000',
      'http://127.0.0.1:5173'
    );
  }
  
  // Production origins from environment variables
  if (isProduction) {
    const prodOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [];
    origins.push(...prodOrigins.map(origin => origin.trim()));
  }
  
  // Always allow the current host for same-origin requests
  if (process.env.HOST_URL) {
    origins.push(process.env.HOST_URL);
  }
  
  return origins;
};

// CORS configuration for Express
export const corsConfig: CorsOptions = {
  origin: (origin, callback) => {
    const allowedOrigins = getAllowedOrigins();
    
    // Allow requests with no origin (mobile apps, Postman, etc.) in development
    if (!origin && isDevelopment) {
      return callback(null, true);
    }
    
    // Check if origin is allowed
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      console.warn(`CORS blocked request from origin: ${origin}`);
      callback(new Error(`Origin ${origin} not allowed by CORS policy`));
    }
  },
  credentials: true, // Allow cookies and authorization headers
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'Cache-Control',
    'X-API-Key'
  ],
  exposedHeaders: [
    'X-Total-Count',
    'X-Page-Count',
    'X-Rate-Limit-Remaining',
    'X-Rate-Limit-Reset'
  ],
  maxAge: 86400, // 24 hours
  optionsSuccessStatus: 200 // For legacy browser support
};

// CORS configuration for Socket.IO
export const socketCorsConfig = {
  origin: getAllowedOrigins(),
  methods: ['GET', 'POST'],
  credentials: true,
  allowEIO3: true // Allow Engine.IO v3 clients
};

// FastAPI CORS configuration
export const fastApiCorsConfig = {
  allow_origins: getAllowedOrigins(),
  allow_credentials: true,
  allow_methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allow_headers: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'Cache-Control',
    'X-API-Key'
  ],
  expose_headers: [
    'X-Total-Count',
    'X-Page-Count',
    'X-Rate-Limit-Remaining',
    'X-Rate-Limit-Reset'
  ],
  max_age: 86400
};

// Utility function to validate origin
export const isOriginAllowed = (origin: string): boolean => {
  const allowedOrigins = getAllowedOrigins();
  return allowedOrigins.includes(origin);
};

// Middleware to log CORS requests
export const logCorsRequests = (req: any, res: any, next: any) => {
  const origin = req.headers.origin;
  if (origin) {
    console.log(`[CORS] Request from origin: ${origin}`);
  }
  next();
};
