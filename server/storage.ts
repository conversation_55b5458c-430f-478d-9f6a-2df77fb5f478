import {
  users,
  User,
  InsertUser,
  agents,
  Agent,
  InsertAgent,
  chat<PERSON><PERSON><PERSON>,
  Chat<PERSON>og,
  InsertChatLog,
  knowledgeBase,
  KnowledgeBase,
  InsertKnowledgeBase,
  settings,
  Setting,
  InsertSetting
} from "@shared/schema";
import { getDb } from './database/connection';
import { eq, desc, and, or, like, count } from 'drizzle-orm';

// Storage interface for all CRUD operations
export interface IStorage {
  // User management
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUsers(page?: number, limit?: number): Promise<{ users: User[], total: number }>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, user: Partial<InsertUser>): Promise<User | undefined>;
  deleteUser(id: number): Promise<boolean>;

  // Agent management
  getAgent(id: number): Promise<Agent | undefined>;
  getAgents(page?: number, limit?: number): Promise<{ agents: Agent[], total: number }>;
  createAgent(agent: InsertAgent): Promise<Agent>;
  updateAgent(id: number, agent: Partial<InsertAgent>): Promise<Agent | undefined>;
  deleteAgent(id: number): Promise<boolean>;

  // Chat logs
  getChatLog(id: number): Promise<ChatLog | undefined>;
  getChatLogs(page?: number, limit?: number, userId?: number, agentId?: number): Promise<{ logs: ChatLog[], total: number }>;
  createChatLog(log: InsertChatLog): Promise<ChatLog>;

  // Knowledge base
  getKnowledgeBaseItem(id: number): Promise<KnowledgeBase | undefined>;
  getKnowledgeBase(page?: number, limit?: number, category?: string): Promise<{ items: KnowledgeBase[], total: number }>;
  createKnowledgeBaseItem(item: InsertKnowledgeBase): Promise<KnowledgeBase>;
  updateKnowledgeBaseItem(id: number, item: Partial<InsertKnowledgeBase>): Promise<KnowledgeBase | undefined>;
  deleteKnowledgeBaseItem(id: number): Promise<boolean>;

  // Settings
  getSetting(key: string): Promise<Setting | undefined>;
  getSettings(): Promise<Setting[]>;
  updateSetting(key: string, value: string): Promise<Setting | undefined>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private agents: Map<number, Agent>;
  private chatLogs: Map<number, ChatLog>;
  private knowledgeBase: Map<number, KnowledgeBase>;
  private settings: Map<string, Setting>;
  
  private userCurrentId: number;
  private agentCurrentId: number;
  private chatLogCurrentId: number;
  private knowledgeBaseCurrentId: number;
  private settingCurrentId: number;

  constructor() {
    this.users = new Map();
    this.agents = new Map();
    this.chatLogs = new Map();
    this.knowledgeBase = new Map();
    this.settings = new Map();
    
    this.userCurrentId = 1;
    this.agentCurrentId = 1;
    this.chatLogCurrentId = 1;
    this.knowledgeBaseCurrentId = 1;
    this.settingCurrentId = 1;
    
    // Create admin user by default with hashed password
    // Note: In production, these should be created through proper user creation flow
    this.createUser({
      username: "admin",
      password: "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJL9.KeF.", // admin123
      email: "<EMAIL>",
      firstName: "Admin",
      lastName: "User",
      role: "admin",
      status: "active"
    });

    // Create artist user by default with hashed password
    this.createUser({
      username: "artist",
      password: "$2b$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // artist123
      email: "<EMAIL>",
      firstName: "Artist",
      lastName: "User",
      role: "artist",
      status: "active"
    });
    
    // Create some default agents
    this.createAgent({
      name: "Style Advisor",
      description: "Helps users find tattoo styles that match their preferences",
      systemPrompt: "You are a tattoo style advisor. Help clients find the perfect tattoo style.",
      isActive: true
    });
    
    this.createAgent({
      name: "Appointment Bot",
      description: "Handles appointment scheduling and consultation bookings",
      systemPrompt: "You are an appointment booking assistant for a tattoo studio.",
      isActive: true
    });
  }

  // User management methods
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username
    );
  }

  async getUsers(page: number = 1, limit: number = 10): Promise<{ users: User[], total: number }> {
    const allUsers = Array.from(this.users.values());
    const total = allUsers.length;
    const startIdx = (page - 1) * limit;
    const endIdx = startIdx + limit;
    const paginatedUsers = allUsers.slice(startIdx, endIdx);
    
    return { users: paginatedUsers, total };
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.userCurrentId++;
    const now = new Date();
    const user: User = { ...insertUser, id, createdAt: now };
    this.users.set(id, user);
    return user;
  }

  async updateUser(id: number, userData: Partial<InsertUser>): Promise<User | undefined> {
    const existingUser = this.users.get(id);
    if (!existingUser) return undefined;
    
    const updatedUser: User = { ...existingUser, ...userData };
    this.users.set(id, updatedUser);
    return updatedUser;
  }

  async deleteUser(id: number): Promise<boolean> {
    return this.users.delete(id);
  }

  // Agent management methods
  async getAgent(id: number): Promise<Agent | undefined> {
    return this.agents.get(id);
  }

  async getAgents(page: number = 1, limit: number = 10): Promise<{ agents: Agent[], total: number }> {
    const allAgents = Array.from(this.agents.values());
    const total = allAgents.length;
    const startIdx = (page - 1) * limit;
    const endIdx = startIdx + limit;
    const paginatedAgents = allAgents.slice(startIdx, endIdx);
    
    return { agents: paginatedAgents, total };
  }

  async createAgent(insertAgent: InsertAgent): Promise<Agent> {
    const id = this.agentCurrentId++;
    const now = new Date();
    const agent: Agent = { ...insertAgent, id, createdAt: now, updatedAt: now };
    this.agents.set(id, agent);
    return agent;
  }

  async updateAgent(id: number, agentData: Partial<InsertAgent>): Promise<Agent | undefined> {
    const existingAgent = this.agents.get(id);
    if (!existingAgent) return undefined;
    
    const now = new Date();
    const updatedAgent: Agent = { ...existingAgent, ...agentData, updatedAt: now };
    this.agents.set(id, updatedAgent);
    return updatedAgent;
  }

  async deleteAgent(id: number): Promise<boolean> {
    return this.agents.delete(id);
  }

  // Chat logs methods
  async getChatLog(id: number): Promise<ChatLog | undefined> {
    return this.chatLogs.get(id);
  }

  async getChatLogs(
    page: number = 1, 
    limit: number = 10, 
    userId?: number, 
    agentId?: number
  ): Promise<{ logs: ChatLog[], total: number }> {
    let filteredLogs = Array.from(this.chatLogs.values());
    
    if (userId !== undefined) {
      filteredLogs = filteredLogs.filter(log => log.userId === userId);
    }
    
    if (agentId !== undefined) {
      filteredLogs = filteredLogs.filter(log => log.agentId === agentId);
    }
    
    const total = filteredLogs.length;
    const startIdx = (page - 1) * limit;
    const endIdx = startIdx + limit;
    const paginatedLogs = filteredLogs.slice(startIdx, endIdx);
    
    return { logs: paginatedLogs, total };
  }

  async createChatLog(insertLog: InsertChatLog): Promise<ChatLog> {
    const id = this.chatLogCurrentId++;
    const now = new Date();
    const chatLog: ChatLog = { ...insertLog, id, createdAt: now };
    this.chatLogs.set(id, chatLog);
    return chatLog;
  }

  // Knowledge base methods
  async getKnowledgeBaseItem(id: number): Promise<KnowledgeBase | undefined> {
    return this.knowledgeBase.get(id);
  }

  async getKnowledgeBase(
    page: number = 1, 
    limit: number = 10, 
    category?: string
  ): Promise<{ items: KnowledgeBase[], total: number }> {
    let filteredItems = Array.from(this.knowledgeBase.values());
    
    if (category) {
      filteredItems = filteredItems.filter(item => item.category === category);
    }
    
    const total = filteredItems.length;
    const startIdx = (page - 1) * limit;
    const endIdx = startIdx + limit;
    const paginatedItems = filteredItems.slice(startIdx, endIdx);
    
    return { items: paginatedItems, total };
  }

  async createKnowledgeBaseItem(insertItem: InsertKnowledgeBase): Promise<KnowledgeBase> {
    const id = this.knowledgeBaseCurrentId++;
    const now = new Date();
    const item: KnowledgeBase = { ...insertItem, id, createdAt: now, updatedAt: now };
    this.knowledgeBase.set(id, item);
    return item;
  }

  async updateKnowledgeBaseItem(id: number, itemData: Partial<InsertKnowledgeBase>): Promise<KnowledgeBase | undefined> {
    const existingItem = this.knowledgeBase.get(id);
    if (!existingItem) return undefined;
    
    const now = new Date();
    const updatedItem: KnowledgeBase = { ...existingItem, ...itemData, updatedAt: now };
    this.knowledgeBase.set(id, updatedItem);
    return updatedItem;
  }

  async deleteKnowledgeBaseItem(id: number): Promise<boolean> {
    return this.knowledgeBase.delete(id);
  }

  // Settings methods
  async getSetting(key: string): Promise<Setting | undefined> {
    return this.settings.get(key);
  }

  async getSettings(): Promise<Setting[]> {
    return Array.from(this.settings.values());
  }

  async updateSetting(key: string, value: string): Promise<Setting | undefined> {
    const existingSetting = this.settings.get(key);
    const now = new Date();
    
    if (existingSetting) {
      const updatedSetting: Setting = { ...existingSetting, value, updatedAt: now };
      this.settings.set(key, updatedSetting);
      return updatedSetting;
    } else {
      const id = this.settingCurrentId++;
      const newSetting: Setting = { id, key, value, updatedAt: now };
      this.settings.set(key, newSetting);
      return newSetting;
    }
  }
}

import { DatabaseStorage } from './database/storage';

// Use database storage in production, memory storage for development/testing
const useDatabase = process.env.NODE_ENV === 'production' || process.env.USE_DATABASE === 'true';

export const storage: IStorage = useDatabase ? new DatabaseStorage() : new MemStorage();
